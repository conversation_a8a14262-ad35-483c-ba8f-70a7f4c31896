import React, { useState, useEffect } from 'react';

const SymbolSelector = () => {
  const [symbols, setSymbols] = useState([]);
  const [activeSymbols, setActiveSymbols] = useState([]);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState('');
  const [error, setError] = useState('');

  // Fetch current symbols and active symbols
  const fetchSymbols = async () => {
    try {
      setLoading(true);
      const response = await fetch('/api/trading/symbols/active');
      const data = await response.json();

      if (response.ok) {
        setSymbols(data.all_symbols || []);
        setActiveSymbols(data.active_symbols || []);
      } else {
        setError('Failed to fetch symbols');
      }
    } catch (err) {
      setError('Error fetching symbols: ' + err.message);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchSymbols();
  }, []);

  // Handle checkbox change
  const handleSymbolToggle = (symbol) => {
    setActiveSymbols(prev => {
      if (prev.includes(symbol)) {
        return prev.filter(s => s !== symbol);
      } else {
        return [...prev, symbol];
      }
    });
  };

  // Handle select all
  const handleSelectAll = () => {
    setActiveSymbols([...symbols]);
  };

  // Handle clear all
  const handleClearAll = () => {
    setActiveSymbols([]);
  };

  // Save active symbols
  const handleSave = async () => {
    try {
      setSaving(true);
      setMessage('');
      setError('');

      const response = await fetch('/api/trading/symbols/active/update', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          active_symbols: activeSymbols
        })
      });

      const data = await response.json();

      if (response.ok) {
        setMessage('Active symbols updated successfully!');
        setTimeout(() => setMessage(''), 3000);
      } else {
        setError(data.detail || 'Failed to update symbols');
      }
    } catch (err) {
      setError('Error updating symbols: ' + err.message);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white shadow-lg rounded-lg border border-gray-200 p-6">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600 mr-2"></div>
          Loading symbols...
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white shadow-lg rounded-lg border border-gray-200">
      <div className="px-6 py-4 border-b border-gray-200">
        <h3 className="text-lg font-semibold text-gray-900 flex items-center gap-2">
          <span>⚙️</span>
          Symbol Selection for AI Analysis
        </h3>
        <p className="text-sm text-gray-600 mt-2">
          Select which currency pairs should be analyzed by AI. Only selected pairs will be processed.
        </p>
      </div>

      <div className="px-6 py-4 space-y-4">
        {/* Status Messages */}
        {message && (
          <div className="flex items-center gap-2 p-3 bg-green-50 border border-green-200 rounded-lg">
            <span className="text-green-600">✅</span>
            <span className="text-green-700 text-sm">{message}</span>
          </div>
        )}

        {error && (
          <div className="flex items-center gap-2 p-3 bg-red-50 border border-red-200 rounded-lg">
            <span className="text-red-600">❌</span>
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}

        {/* Summary */}
        <div className="flex items-center justify-between p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium">Active Symbols:</span>
            <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
              {activeSymbols.length} of {symbols.length}
            </span>
          </div>
          <div className="flex gap-2">
            <button
              className="inline-flex items-center justify-center rounded-md font-medium transition-colors border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 h-9 px-3 text-sm disabled:opacity-50 disabled:pointer-events-none"
              onClick={handleSelectAll}
              disabled={activeSymbols.length === symbols.length}
            >
              Select All
            </button>
            <button
              className="inline-flex items-center justify-center rounded-md font-medium transition-colors border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 h-9 px-3 text-sm disabled:opacity-50 disabled:pointer-events-none"
              onClick={handleClearAll}
              disabled={activeSymbols.length === 0}
            >
              Clear All
            </button>
          </div>
        </div>

        {/* Symbol Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
          {symbols.map((symbol) => {
            const isActive = activeSymbols.includes(symbol);
            return (
              <div
                key={symbol}
                className={`flex items-center space-x-2 p-3 border rounded-lg cursor-pointer transition-colors ${
                  isActive
                    ? 'bg-blue-50 border-blue-300'
                    : 'bg-gray-50 border-gray-200 hover:bg-gray-100'
                }`}
                onClick={() => handleSymbolToggle(symbol)}
              >
                <input
                  type="checkbox"
                  checked={isActive}
                  onChange={(e) => {
                    e.stopPropagation();
                    handleSymbolToggle(symbol);
                  }}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer"
                />
                <div className="flex-1">
                  <div className="font-medium text-sm">{symbol}</div>
                  <div className="text-xs text-gray-500">
                    {isActive ? 'Active' : 'Inactive'}
                  </div>
                </div>
              </div>
            );
          })}
        </div>

        {/* Save Button */}
        <div className="flex justify-end pt-4 border-t">
          <button
            onClick={handleSave}
            disabled={saving || activeSymbols.length === 0}
            className="inline-flex items-center justify-center rounded-md font-medium transition-colors bg-blue-600 text-white hover:bg-blue-700 h-10 py-2 px-4 min-w-[120px] disabled:opacity-50 disabled:pointer-events-none"
          >
            {saving ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                Saving...
              </>
            ) : (
              'Save Changes'
            )}
          </button>
        </div>

        {/* Info */}
        <div className="text-xs text-gray-500 bg-gray-50 p-3 rounded-lg">
          <strong>Note:</strong> Changes take effect immediately. The AI will analyze only the selected symbols
          in the next analysis cycle (every 5 minutes). Deselected symbols will not be processed until re-enabled.
        </div>
      </div>
    </div>
  );
};

export default SymbolSelector;
