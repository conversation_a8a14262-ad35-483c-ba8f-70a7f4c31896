#!/usr/bin/env python3
"""
Simple validation test to verify the fix
"""

import asyncio
import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.utils.risk_management import RiskManager
from backend.models.trading_models import OrderRequest, OrderType, AccountInfo, Position, MarketData, RiskManagement
from datetime import datetime

def test_validation_fix():
    """Test the validation fix"""
    print("🧪 VALIDATION FIX TEST")
    print("=" * 60)

    # Create risk manager
    risk_params = RiskManagement(
        max_risk_percent=2.0,
        max_open_positions=5,
        max_daily_loss=1000.0,
        max_drawdown_percent=10.0,
        stop_loss_pips=50,
        take_profit_pips=100,
        trailing_stop_pips=30
    )

    risk_manager = RiskManager(risk_params)

    # Test time-based validation
    print("1️⃣ Testing time-based validation...")
    is_allowed = risk_manager._is_trading_allowed()
    print(f"   Trading allowed: {is_allowed}")

    if is_allowed:
        print("   ✅ Trading is allowed (time restriction fixed)")
    else:
        print("   ❌ Trading is still restricted")
        current_time = datetime.now()
        print(f"   Current time: {current_time.strftime('%A %H:%M UTC')}")

    # Test order validation
    print("\n2️⃣ Testing order validation...")

    # Create mock data
    order_request = OrderRequest(
        symbol="BTCUSDm",
        order_type=OrderType.BUY,
        lot_size=0.01,
        price=107000.0,
        stop_loss=106000.0,
        take_profit=108000.0
    )

    account_info = AccountInfo(
        balance=16.25,
        equity=16.25,
        free_margin=16.25,
        margin_level=0.0,
        profit=0.0
    )

    market_data = MarketData(
        symbol="BTCUSDm",
        timestamp=datetime.now(),
        bid=107000.0,
        ask=107010.0,
        spread=10.0,
        volume=100
    )

    positions = []

    # Validate order
    is_valid, message = risk_manager.validate_order(
        order_request, account_info, positions, market_data
    )

    print(f"   Order validation: {is_valid}")
    print(f"   Message: {message}")

    if is_valid:
        print("   ✅ Order validation passed")
    else:
        print(f"   ❌ Order validation failed: {message}")

    print("\n" + "=" * 60)
    print("🎯 VALIDATION TEST COMPLETED")

    return is_allowed and is_valid

if __name__ == "__main__":
    success = test_validation_fix()
    if success:
        print("✅ ALL VALIDATION TESTS PASSED!")
    else:
        print("❌ Some validation tests failed")
