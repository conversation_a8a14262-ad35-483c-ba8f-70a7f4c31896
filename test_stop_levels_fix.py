#!/usr/bin/env python3
"""
Test stop levels fix for BTCUSDm
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.utils.risk_management import RiskManager
from backend.models.trading_models import OrderType, RiskManagement

def test_stop_levels_fix():
    """Test stop levels fix"""
    print("🔧 STOP LEVELS FIX TEST")
    print("=" * 40)
    
    # Create risk manager
    risk_params = RiskManagement(
        max_risk_percent=2.0,
        max_open_positions=5,
        max_daily_loss=1000.0,
        max_drawdown_percent=30.0,
        stop_loss_pips=50,      # Default (will be overridden for BTC)
        take_profit_pips=100,   # Default (will be overridden for BTC)
        trailing_stop_pips=30
    )
    
    risk_manager = RiskManager(risk_params)
    
    # Test BTCUSDm
    print("📊 Testing BTCUSDm Stop Levels:")
    print("-" * 30)
    
    entry_price = 106779.83
    
    # Test SELL order
    stop_loss, take_profit = risk_manager.calculate_stop_loss_take_profit(
        symbol="BTCUSDm",
        entry_price=entry_price,
        order_type=OrderType.SELL
    )
    
    print(f"   Entry Price: ${entry_price:,.2f}")
    print(f"   Stop Loss: ${stop_loss:,.2f}")
    print(f"   Take Profit: ${take_profit:,.2f}")
    print(f"   SL Distance: ${stop_loss - entry_price:,.2f} (should be +$500)")
    print(f"   TP Distance: ${entry_price - take_profit:,.2f} (should be +$1000)")
    
    # Validate distances
    sl_distance = abs(stop_loss - entry_price)
    tp_distance = abs(take_profit - entry_price)
    
    print(f"\n✅ VALIDATION:")
    print(f"   SL Distance: {'✅ VALID' if sl_distance >= 500 else '❌ TOO SMALL'} (${sl_distance:,.2f})")
    print(f"   TP Distance: {'✅ VALID' if tp_distance >= 1000 else '❌ TOO SMALL'} (${tp_distance:,.2f})")
    
    # Test ETHUSDm for comparison
    print("\n📊 Testing ETHUSDm Stop Levels:")
    print("-" * 30)
    
    eth_entry = 2600.0
    eth_sl, eth_tp = risk_manager.calculate_stop_loss_take_profit(
        symbol="ETHUSDm",
        entry_price=eth_entry,
        order_type=OrderType.SELL
    )
    
    print(f"   Entry Price: ${eth_entry:,.2f}")
    print(f"   Stop Loss: ${eth_sl:,.2f}")
    print(f"   Take Profit: ${eth_tp:,.2f}")
    print(f"   SL Distance: ${eth_sl - eth_entry:,.2f} (should be +$20)")
    print(f"   TP Distance: ${eth_entry - eth_tp:,.2f} (should be +$40)")
    
    # Test BUY order for BTCUSDm
    print("\n📊 Testing BTCUSDm BUY Order:")
    print("-" * 30)
    
    buy_sl, buy_tp = risk_manager.calculate_stop_loss_take_profit(
        symbol="BTCUSDm",
        entry_price=entry_price,
        order_type=OrderType.BUY
    )
    
    print(f"   Entry Price: ${entry_price:,.2f}")
    print(f"   Stop Loss: ${buy_sl:,.2f}")
    print(f"   Take Profit: ${buy_tp:,.2f}")
    print(f"   SL Distance: ${entry_price - buy_sl:,.2f} (should be +$500)")
    print(f"   TP Distance: ${buy_tp - entry_price:,.2f} (should be +$1000)")
    
    print("\n🎯 SUMMARY:")
    print("=" * 40)
    
    btc_valid = sl_distance >= 500 and tp_distance >= 1000
    
    if btc_valid:
        print("✅ BTCUSDm stop levels are now VALID")
        print("✅ Should fix 'Invalid stops' error 10016")
        print("✅ Stop Loss: $500 minimum distance")
        print("✅ Take Profit: $1000 minimum distance")
    else:
        print("❌ BTCUSDm stop levels still invalid")
        print("❌ Need to increase distances further")
    
    return btc_valid

if __name__ == "__main__":
    success = test_stop_levels_fix()
    print(f"\n📊 RESULT: {'✅ SUCCESS' if success else '❌ FAILED'}")
    
    if success:
        print("\n🚀 NEXT STEPS:")
        print("1. Restart backend server")
        print("2. Test BTCUSDm order placement")
        print("3. Should no longer get 'Invalid stops' error")
    else:
        print("\n🔧 NEED MORE FIXES:")
        print("1. Increase stop distances further")
        print("2. Check broker's minimum stop levels")
        print("3. Consider dynamic stop calculation")
