#!/usr/bin/env python3
"""
Simple test for margin calculation
"""
import requests
import time

def test_margin():
    """Test margin calculation"""
    print("🔧 === TESTING MARGIN CALCULATION ===")
    
    # Start trading engine
    print("\n1. Starting trading engine...")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Trading engine started")
        else:
            print(f"❌ Failed: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Wait and monitor
    print("\n2. Monitoring for 15 seconds...")
    print("   Watch for margin calculations in backend logs...")
    time.sleep(15)
    
    print("\n✅ Test completed - check backend logs for margin calculations")

if __name__ == "__main__":
    test_margin()
