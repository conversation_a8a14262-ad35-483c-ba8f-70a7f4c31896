# Troubleshooting Guide

## Common Issues and Solutions

### 1. Network Error - Failed to Start Trading Engine

**Problem:** Frontend shows "Failed to start trading engine Network Error"

**Cause:** API request missing required JSON body parameters

**Solution:**
```python
# ❌ Wrong - Missing request body
response = requests.post(url)

# ✅ Correct - Include required parameters
response = requests.post(url, json={"auto_trading_enabled": True})
```

**Fixed in:** `check_engine_status.py` line 48-51

### 2. MT5 Connection Issues

**Problem:** "Failed to connect to MT5"

**Solutions:**
1. Ensure MetaTrader 5 is running
2. Login manually to MT5 first
3. Check credentials in `.env` file
4. Verify server name is correct
5. Run as Administrator

**Test:** `python test_mt5_connection.py`

### 3. GPT/AI Connection Issues

**Problem:** "GPT connection test failed"

**Solutions:**
1. Check OPENAI_API_KEY in `.env`
2. Verify API key is valid
3. Check internet connection
4. Ensure sufficient API credits

**Test:** `python test_gpt_connection.py`

### 4. 422 Unprocessable Entity Error

**Problem:** API returns 422 error

**Cause:** Request body doesn't match expected schema

**Solution:** Ensure all required fields are included:
```json
{
  "auto_trading_enabled": true
}
```

### 5. Trading Engine Status Check

**Commands to diagnose:**
```bash
# Check MT5 connection
python test_mt5_connection.py

# Check engine status
python check_engine_status.py

# Simple start test
python test_start_simple.py
```

### 6. Backend Logs Analysis

**Key log messages to look for:**
- ✅ "Connected to MT5 account: [number]"
- ✅ "Trading Engine initialized successfully"
- ✅ "Trading Engine started"
- ✅ "Processing symbol: [SYMBOL]"

**Error patterns:**
- ❌ "Failed to connect to MT5"
- ❌ "GPT connection test failed"
- ❌ "Failed to initialize trading engine"

### 7. Port and URL Issues

**Default URLs:**
- Backend API: `http://localhost:8001`
- Frontend: `http://localhost:3000`
- WebSocket: `ws://localhost:8001/ws`

**Check if ports are available:**
```bash
netstat -an | findstr :8001
netstat -an | findstr :3000
```

### 8. Environment Variables

**Required in `.env`:**
```env
OPENAI_API_KEY=your_key_here
MT5_LOGIN=your_login
MT5_PASSWORD=your_password
MT5_SERVER=your_server
ACTIVE_SYMBOLS=USDAED,USDAMD
```

### 9. Symbol Configuration

**Problem:** No symbols being analyzed

**Solution:** Update active symbols:
```bash
# Via API
curl -X POST http://localhost:8001/api/trading/symbols/active/update \
  -H "Content-Type: application/json" \
  -d '{"active_symbols": ["USDAED", "USDAMD"]}'

# Or edit .env file
ACTIVE_SYMBOLS=USDAED,USDAMD
```

### 10. Quick Health Check

**Run this sequence:**
1. `python test_mt5_connection.py` - Verify MT5
2. Start backend: `python -m backend.main`
3. Test API: `python test_start_simple.py`
4. Check frontend: Open `http://localhost:3000`

**Expected Results:**
- MT5: ✅ Connected
- GPT: ✅ Available  
- Engine: ✅ Running
- Frontend: ✅ No network errors
