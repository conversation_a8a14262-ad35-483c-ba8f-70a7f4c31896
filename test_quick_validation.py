#!/usr/bin/env python3
"""
Quick validation test
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.utils.risk_management import RiskManager
from backend.models.trading_models import RiskManagement

def test_quick_validation():
    """Quick validation test"""
    print("🧪 QUICK VALIDATION TEST")
    print("=" * 40)
    
    try:
        # Create risk manager
        risk_params = RiskManagement(
            max_risk_percent=2.0,
            max_open_positions=5,
            max_daily_loss=1000.0,
            max_drawdown_percent=10.0,
            stop_loss_pips=50,
            take_profit_pips=100,
            trailing_stop_pips=30
        )
        
        risk_manager = RiskManager(risk_params)
        print("✅ RiskManager created successfully")
        
        # Test time-based validation
        is_allowed = risk_manager._is_trading_allowed()
        print(f"✅ Trading allowed: {is_allowed}")
        
        print("\n🎉 VALIDATION FIX SUCCESSFUL!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    success = test_quick_validation()
    if success:
        print("\n✅ ALL TESTS PASSED!")
    else:
        print("\n❌ TESTS FAILED!")
