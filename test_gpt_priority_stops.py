#!/usr/bin/env python3
"""
Test GPT priority for stop loss/take profit values
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.models.trading_models import GPTAnalysis, SignalType, RiskLevel, MarketData
from backend.services.trading_engine import TradingEngine
from backend.utils.risk_management import RiskManager, RiskManagement
from datetime import datetime

def test_gpt_priority_stops():
    """Test GPT priority for stop loss/take profit"""
    print("🧪 GPT PRIORITY STOP LEVELS TEST")
    print("=" * 50)
    
    # Create risk manager
    risk_params = RiskManagement(
        max_risk_percent=2.0,
        max_open_positions=5,
        max_daily_loss=1000.0,
        max_drawdown_percent=30.0,
        stop_loss_pips=50,      # Fallback values
        take_profit_pips=100,   # Fallback values
        trailing_stop_pips=30
    )
    
    risk_manager = RiskManager(risk_params)
    
    # Test Case 1: GPT provides valid stop levels
    print("1️⃣ Testing with VALID GPT stop levels:")
    print("-" * 40)
    
    gpt_analysis_valid = GPTAnalysis(
        symbol="BTCUSDm",
        timestamp=datetime.now(),
        signal=SignalType.SELL,
        confidence=0.85,
        entry_price=106810.0,
        stop_loss=107310.0,  # GPT recommends $500 stop
        take_profit=105810.0,  # GPT recommends $1000 profit
        risk_level=RiskLevel.MEDIUM,
        reasoning="GPT analysis with valid stops",
        technical_summary="Strong bearish momentum",
        market_sentiment="bearish"
    )
    
    print(f"   GPT Entry: ${gpt_analysis_valid.entry_price:,.2f}")
    print(f"   GPT Stop Loss: ${gpt_analysis_valid.stop_loss:,.2f}")
    print(f"   GPT Take Profit: ${gpt_analysis_valid.take_profit:,.2f}")
    print(f"   SL Distance: ${abs(gpt_analysis_valid.stop_loss - gpt_analysis_valid.entry_price):,.2f}")
    print(f"   TP Distance: ${abs(gpt_analysis_valid.take_profit - gpt_analysis_valid.entry_price):,.2f}")
    
    # Simulate trading engine logic
    entry_price = gpt_analysis_valid.entry_price
    stop_loss = gpt_analysis_valid.stop_loss
    take_profit = gpt_analysis_valid.take_profit
    
    if not stop_loss or not take_profit:
        print("   ❌ GPT didn't provide stops - would use fallback")
        from backend.models.trading_models import OrderType
        fallback_sl, fallback_tp = risk_manager.calculate_stop_loss_take_profit(
            "BTCUSDm", entry_price, OrderType.SELL
        )
        print(f"   Fallback SL: ${fallback_sl:,.2f}")
        print(f"   Fallback TP: ${fallback_tp:,.2f}")
    else:
        print("   ✅ Using GPT stop levels (PRIORITY)")
        print(f"   Final SL: ${stop_loss:,.2f}")
        print(f"   Final TP: ${take_profit:,.2f}")
    
    # Test Case 2: GPT provides no stop levels
    print("\n2️⃣ Testing with NO GPT stop levels:")
    print("-" * 40)
    
    gpt_analysis_no_stops = GPTAnalysis(
        symbol="BTCUSDm",
        timestamp=datetime.now(),
        signal=SignalType.SELL,
        confidence=0.75,
        entry_price=106810.0,
        stop_loss=None,  # No GPT stop loss
        take_profit=None,  # No GPT take profit
        risk_level=RiskLevel.MEDIUM,
        reasoning="GPT analysis without stops",
        technical_summary="Bearish signal",
        market_sentiment="bearish"
    )
    
    print(f"   GPT Entry: ${gpt_analysis_no_stops.entry_price:,.2f}")
    print(f"   GPT Stop Loss: {gpt_analysis_no_stops.stop_loss}")
    print(f"   GPT Take Profit: {gpt_analysis_no_stops.take_profit}")
    
    # Simulate fallback logic
    entry_price_2 = gpt_analysis_no_stops.entry_price
    stop_loss_2 = gpt_analysis_no_stops.stop_loss
    take_profit_2 = gpt_analysis_no_stops.take_profit
    
    if not stop_loss_2 or not take_profit_2:
        print("   ⚠️ GPT didn't provide stops - using fallback")
        from backend.models.trading_models import OrderType
        fallback_sl_2, fallback_tp_2 = risk_manager.calculate_stop_loss_take_profit(
            "BTCUSDm", entry_price_2, OrderType.SELL
        )
        print(f"   Fallback SL: ${fallback_sl_2:,.2f}")
        print(f"   Fallback TP: ${fallback_tp_2:,.2f}")
        print(f"   SL Distance: ${abs(fallback_sl_2 - entry_price_2):,.2f}")
        print(f"   TP Distance: ${abs(fallback_tp_2 - entry_price_2):,.2f}")
    
    # Test Case 3: GPT provides invalid stop levels
    print("\n3️⃣ Testing with INVALID GPT stop levels:")
    print("-" * 40)
    
    gpt_analysis_invalid = GPTAnalysis(
        symbol="BTCUSDm",
        timestamp=datetime.now(),
        signal=SignalType.SELL,
        confidence=0.80,
        entry_price=106810.0,
        stop_loss=106810.5,  # Too close to entry (0.5 distance)
        take_profit=106809.5,  # Too close to entry (0.5 distance)
        risk_level=RiskLevel.MEDIUM,
        reasoning="GPT analysis with invalid stops",
        technical_summary="Bearish signal",
        market_sentiment="bearish"
    )
    
    print(f"   GPT Entry: ${gpt_analysis_invalid.entry_price:,.2f}")
    print(f"   GPT Stop Loss: ${gpt_analysis_invalid.stop_loss:,.2f}")
    print(f"   GPT Take Profit: ${gpt_analysis_invalid.take_profit:,.2f}")
    print(f"   SL Distance: ${abs(gpt_analysis_invalid.stop_loss - gpt_analysis_invalid.entry_price):,.2f}")
    print(f"   TP Distance: ${abs(gpt_analysis_invalid.take_profit - gpt_analysis_invalid.entry_price):,.2f}")
    
    # Simulate validation logic
    entry_price_3 = gpt_analysis_invalid.entry_price
    stop_loss_3 = gpt_analysis_invalid.stop_loss
    take_profit_3 = gpt_analysis_invalid.take_profit
    
    # Check for extreme cases
    if stop_loss_3 <= 0 or take_profit_3 <= 0:
        print("   ❌ Invalid GPT stops (negative values) - using fallback")
    elif abs(stop_loss_3 - entry_price_3) < 1.0 or abs(take_profit_3 - entry_price_3) < 1.0:
        print("   ❌ GPT stops too close to entry - using fallback")
        from backend.models.trading_models import OrderType
        fallback_sl_3, fallback_tp_3 = risk_manager.calculate_stop_loss_take_profit(
            "BTCUSDm", entry_price_3, OrderType.SELL
        )
        print(f"   Fallback SL: ${fallback_sl_3:,.2f}")
        print(f"   Fallback TP: ${fallback_tp_3:,.2f}")
    else:
        print("   ✅ Using GPT stop levels")
    
    print("\n🎯 SUMMARY:")
    print("=" * 50)
    print("✅ PRIORITY ORDER:")
    print("   1. GPT stop levels (if valid)")
    print("   2. Manual calculation (fallback only)")
    print("\n✅ VALIDATION RULES:")
    print("   - GPT stops must exist (not None)")
    print("   - GPT stops must be > 0")
    print("   - GPT stops must be > 1.0 distance from entry")
    print("\n✅ BENEFITS:")
    print("   - Trust GPT technical analysis")
    print("   - Respect support/resistance levels")
    print("   - Better risk/reward ratios")
    print("   - Reduced override of smart decisions")
    
    return True

if __name__ == "__main__":
    print("🧪 TESTING GPT PRIORITY FOR STOP LEVELS")
    print("=" * 60)
    
    success = test_gpt_priority_stops()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"   GPT Priority Test: {'✅ PASS' if success else '❌ FAIL'}")
    
    if success:
        print("\n🎉 GPT PRIORITY IMPLEMENTED!")
        print("✅ GPT stop levels will be used when available")
        print("✅ Manual calculation only as fallback")
        print("✅ Minimal validation for extreme cases")
        print("\n🚀 EXPECTED LOGS:")
        print("   ✅ 'Using GPT stop levels for SYMBOL: SL=X, TP=Y'")
        print("   ⚠️ 'GPT didn't provide stop levels, using fallback'")
        print("   ❌ 'Invalid GPT stop levels, using fallback'")
        print("\n📝 NEXT STEPS:")
        print("   1. Restart backend server")
        print("   2. Test with GPT analysis")
        print("   3. Monitor logs for GPT vs fallback usage")
    else:
        print("\n❌ TEST FAILED")
        print("🔧 Check implementation and try again")
