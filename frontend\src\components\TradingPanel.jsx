import React, { useState, useEffect } from 'react';
import { 
  RefreshCw, 
  TrendingUp, 
  TrendingDown, 
  Brain,
  AlertCircle,
  CheckCircle
} from 'lucide-react';
import { tradingApi, formatDateTime } from '../services/api';

const TradingPanel = () => {
  const [symbols, setSymbols] = useState([]);
  const [selectedSymbol, setSelectedSymbol] = useState('');
  const [marketData, setMarketData] = useState({});
  const [activeSignals, setActiveSignals] = useState([]);
  const [loading, setLoading] = useState(false);
  const [analyzing, setAnalyzing] = useState({});

  useEffect(() => {
    loadTradingData();
  }, []);

  const loadTradingData = async () => {
    try {
      setLoading(true);
      
      // Load symbols and signals
      const [symbolsResponse, signalsResponse] = await Promise.all([
        tradingApi.getSymbols(),
        tradingApi.getActiveSignals()
      ]);

      setSymbols(symbolsResponse.data.symbols || []);
      setActiveSignals(signalsResponse.data.signals || []);
      
      if (symbolsResponse.data.symbols?.length > 0 && !selectedSymbol) {
        setSelectedSymbol(symbolsResponse.data.symbols[0]);
      }

    } catch (error) {
      console.error('Error loading trading data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadMarketData = async (symbol) => {
    try {
      const response = await tradingApi.getMarketData(symbol);
      setMarketData(prev => ({
        ...prev,
        [symbol]: response.data
      }));
    } catch (error) {
      console.error(`Error loading market data for ${symbol}:`, error);
    }
  };

  const handleSymbolChange = (symbol) => {
    setSelectedSymbol(symbol);
    if (symbol && !marketData[symbol]) {
      loadMarketData(symbol);
    }
  };

  const handleForceAnalysis = async (symbol) => {
    try {
      setAnalyzing(prev => ({ ...prev, [symbol]: true }));
      await tradingApi.forceAnalysis(symbol);
      
      // Reload signals after analysis
      setTimeout(async () => {
        const response = await tradingApi.getActiveSignals();
        setActiveSignals(response.data.signals || []);
      }, 2000);
      
    } catch (error) {
      console.error(`Error analyzing ${symbol}:`, error);
      alert(`Failed to analyze ${symbol}: ${error.message}`);
    } finally {
      setAnalyzing(prev => ({ ...prev, [symbol]: false }));
    }
  };

  const refreshMarketData = async () => {
    if (selectedSymbol) {
      await loadMarketData(selectedSymbol);
    }
  };

  const currentMarketData = selectedSymbol ? marketData[selectedSymbol] : null;

  return (
    <div className="space-y-6">
      {/* Symbol Selection */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-medium text-gray-900">Trading Control Panel</h2>
          <button
            onClick={loadTradingData}
            disabled={loading}
            className="btn btn-secondary flex items-center space-x-2"
          >
            <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
            <span>Refresh</span>
          </button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Symbol
            </label>
            <select
              value={selectedSymbol}
              onChange={(e) => handleSymbolChange(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500"
            >
              <option value="">Select a symbol...</option>
              {symbols.map((symbol) => (
                <option key={symbol} value={symbol}>
                  {symbol}
                </option>
              ))}
            </select>
          </div>

          {selectedSymbol && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Actions
              </label>
              <div className="flex space-x-2">
                <button
                  onClick={refreshMarketData}
                  className="btn btn-secondary flex items-center space-x-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  <span>Refresh Data</span>
                </button>
                <button
                  onClick={() => handleForceAnalysis(selectedSymbol)}
                  disabled={analyzing[selectedSymbol]}
                  className="btn btn-primary flex items-center space-x-2"
                >
                  <Brain className={`w-4 h-4 ${analyzing[selectedSymbol] ? 'animate-pulse' : ''}`} />
                  <span>
                    {analyzing[selectedSymbol] ? 'Analyzing...' : 'Force Analysis'}
                  </span>
                </button>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Market Data Display */}
      {currentMarketData && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">
            Market Data - {selectedSymbol}
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <p className="text-sm text-gray-600">Bid Price</p>
              <p className="text-2xl font-semibold text-red-600">
                {currentMarketData.bid?.toFixed(5)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Ask Price</p>
              <p className="text-2xl font-semibold text-green-600">
                {currentMarketData.ask?.toFixed(5)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Spread</p>
              <p className="text-2xl font-semibold text-gray-900">
                {currentMarketData.spread?.toFixed(5)}
              </p>
            </div>
            <div className="text-center">
              <p className="text-sm text-gray-600">Last Update</p>
              <p className="text-sm text-gray-900">
                {formatDateTime(currentMarketData.timestamp)}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Active Signals */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Active Trading Signals</h3>
        
        {activeSignals.length === 0 ? (
          <div className="text-center py-8">
            <AlertCircle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <p className="text-gray-600">No active trading signals</p>
            <p className="text-sm text-gray-500 mt-2">
              Select a symbol and run analysis to generate signals
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {activeSignals.map((signal, index) => (
              <div
                key={signal.id || index}
                className="border border-gray-200 rounded-lg p-4"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-3">
                    <h4 className="text-lg font-medium text-gray-900">
                      {signal.symbol}
                    </h4>
                    <span
                      className={`status-indicator ${
                        signal.signal_type === 'BUY'
                          ? 'bg-green-100 text-green-800'
                          : signal.signal_type === 'SELL'
                          ? 'bg-red-100 text-red-800'
                          : 'bg-gray-100 text-gray-800'
                      }`}
                    >
                      {signal.signal_type}
                    </span>
                    <span
                      className={`status-indicator ${
                        signal.risk_level === 'LOW'
                          ? 'status-healthy'
                          : signal.risk_level === 'MEDIUM'
                          ? 'status-warning'
                          : 'status-error'
                      }`}
                    >
                      {signal.risk_level} Risk
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    {signal.signal_type === 'BUY' ? (
                      <TrendingUp className="w-5 h-5 text-green-600" />
                    ) : signal.signal_type === 'SELL' ? (
                      <TrendingDown className="w-5 h-5 text-red-600" />
                    ) : (
                      <CheckCircle className="w-5 h-5 text-gray-600" />
                    )}
                    <span className="text-sm text-gray-600">
                      Confidence: {(signal.confidence * 100).toFixed(1)}%
                    </span>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-3">
                  <div>
                    <p className="text-sm text-gray-600">Entry Price</p>
                    <p className="font-medium">{signal.entry_price?.toFixed(5)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Stop Loss</p>
                    <p className="font-medium text-red-600">
                      {signal.stop_loss?.toFixed(5) || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Take Profit</p>
                    <p className="font-medium text-green-600">
                      {signal.take_profit?.toFixed(5) || 'N/A'}
                    </p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600">Lot Size</p>
                    <p className="font-medium">{signal.lot_size}</p>
                  </div>
                </div>

                {signal.reasoning && (
                  <div className="bg-gray-50 rounded-md p-3">
                    <p className="text-sm text-gray-700">
                      <strong>Analysis:</strong> {signal.reasoning}
                    </p>
                  </div>
                )}

                <div className="mt-3 text-xs text-gray-500">
                  Generated: {formatDateTime(signal.timestamp)}
                </div>
              </div>
            ))}
          </div>
        )}
      </div>

      {/* Symbol Analysis Grid */}
      <div className="card">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Quick Analysis</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {symbols.map((symbol) => (
            <div
              key={symbol}
              className="border border-gray-200 rounded-lg p-4 hover:border-primary-300 transition-colors"
            >
              <div className="flex items-center justify-between mb-2">
                <h4 className="font-medium text-gray-900">{symbol}</h4>
                <button
                  onClick={() => handleForceAnalysis(symbol)}
                  disabled={analyzing[symbol]}
                  className="text-primary-600 hover:text-primary-700 disabled:text-gray-400"
                >
                  <Brain className={`w-4 h-4 ${analyzing[symbol] ? 'animate-pulse' : ''}`} />
                </button>
              </div>
              
              {marketData[symbol] && (
                <div className="text-sm text-gray-600">
                  <p>Bid: {marketData[symbol].bid?.toFixed(5)}</p>
                  <p>Ask: {marketData[symbol].ask?.toFixed(5)}</p>
                </div>
              )}
              
              {analyzing[symbol] && (
                <div className="mt-2">
                  <div className="animate-pulse bg-gray-200 h-2 rounded"></div>
                  <p className="text-xs text-gray-500 mt-1">Analyzing...</p>
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default TradingPanel;
