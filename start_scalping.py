#!/usr/bin/env python3
"""
Start script untuk SCALPING TRADING SYSTEM
Equivalent dengan "python backend.main" tapi untuk scalping mode
"""

import os
import sys
import asyncio
import uvicorn
from pathlib import Path

def set_scalping_mode():
    """Set environment variable untuk scalping mode"""
    os.environ["SCALPING_MODE"] = "true"
    print("✅ Scalping mode activated")

def print_scalping_banner():
    """Print banner untuk scalping system"""
    print("=" * 70)
    print("🎯 SCALPING TRADING SYSTEM")
    print("=" * 70)
    print("📈 Auto Trading Scalping untuk Futures berbasis Python + GPT-4o")
    print("🧠 GPT sebagai Validator Struktur (bukan prediktor)")
    print("⚡ Pending Orders dengan Buffer & Revalidasi")
    print("📊 Sistem Logging dan Evaluator setelah posisi ditutup")
    print("")
    print("🔧 KOMPONEN AKTIF:")
    print("   📊 Data Fetcher - 30 candle M15 + RSI(14) + ATR(14)")
    print("   🧠 GPT Analyzer - Validator struktur candle + indikator")
    print("   🚨 Trigger Monitor - Pergerakan harga + cooldown timer")
    print("   📋 Order Manager - Pending orders + buffer zones")
    print("   💰 Price Monitor - Real-time tracking + moving window")
    print("   🔄 Reevaluation - Buffer zone analysis + order validation")
    print("   📈 Evaluator - Post-trade analysis + learning insights")
    print("")
    print("⚙️ PAIR THRESHOLDS:")
    print("   BTCUSDm: 0.30% movement triggers analysis")
    print("   ETHUSDm: 0.35% movement triggers analysis")
    print("   EURUSD: 0.15% movement triggers analysis")
    print("   XAUUSD: 0.25% movement triggers analysis")
    print("")
    print("🎯 SCALPING CRITERIA:")
    print("   BUY: Breakout bullish + RSI <40→>50 + Volume +20% + No upper wick")
    print("   SELL: Breakdown bearish + RSI >60→<50 + Volume +20% + No lower tail")
    print("")
    print("⏰ TIMING:")
    print("   Main Cycle: 5 minutes")
    print("   Re-evaluation: 3 minutes")
    print("   Cleanup: 1 hour")
    print("   Order Expiry: 2 hours")
    print("")
    print("📂 LOGS:")
    print("   System: logs/scalping_system.log")
    print("   Orders: logs/pending_orders.json")
    print("   Expired: logs/expired_orders.json")
    print("   Evaluations: logs/evaluations/")
    print("")
    print("🌐 API ENDPOINTS:")
    print("   Status: http://localhost:8001/api/scalping/status")
    print("   Analyze: POST http://localhost:8001/api/scalping/analyze/{symbol}")
    print("   Orders: http://localhost:8001/api/scalping/orders")
    print("   Health: http://localhost:8001/api/scalping/health")
    print("=" * 70)
    print("")

def main():
    """Main function untuk start scalping system"""
    print_scalping_banner()
    
    # Set scalping mode
    set_scalping_mode()
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    print("🚀 Starting Scalping Trading System...")
    print("📊 API Server will be available at: http://localhost:8001")
    print("🔄 Scalping Engine will start automatically")
    print("📈 Monitoring symbols: BTCUSDm, ETHUSDm, EURUSD, XAUUSD")
    print("")
    print("💡 TIPS:")
    print("   - Check status: curl http://localhost:8001/api/scalping/status")
    print("   - Manual analysis: curl -X POST http://localhost:8001/api/scalping/analyze/BTCUSDm")
    print("   - View logs: tail -f logs/scalping_system.log")
    print("   - Stop system: Ctrl+C")
    print("")
    print("🔥 System starting in 3 seconds...")
    
    import time
    time.sleep(3)
    
    try:
        # Import and run the backend main with scalping mode
        from backend.main import app
        
        # Run uvicorn server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info",
            access_log=False  # Reduce log noise
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Scalping system stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting scalping system: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
