#!/usr/bin/env python3
"""
Test broadcast system status fix
"""
import requests
import time
import websocket
import json
import threading

def test_websocket_broadcast():
    """Test WebSocket broadcast for JSON serialization errors"""
    print("🔧 === TESTING BROADCAST SYSTEM STATUS FIX ===")
    
    # Test 1: Start trading engine to trigger broadcast
    print("\n1. Starting trading engine to trigger broadcast...")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Trading engine started successfully")
        else:
            print(f"❌ Failed to start engine: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error starting engine: {e}")
        return False
    
    # Test 2: Connect to WebSocket and listen for broadcasts
    print("\n2. Connecting to WebSocket to monitor broadcasts...")
    
    messages_received = []
    error_occurred = False
    
    def on_message(ws, message):
        nonlocal messages_received, error_occurred
        try:
            data = json.loads(message)
            messages_received.append(data)
            print(f"✅ Received broadcast: {data.get('type', 'unknown')}")
            
            # Check if it's a system_update
            if data.get('type') == 'system_update':
                print(f"   Timestamp: {data.get('timestamp')}")
                system_status = data.get('data', {}).get('system_status')
                if system_status:
                    print(f"   MT5 Connected: {system_status.get('is_mt5_connected')}")
                    print(f"   GPT Available: {system_status.get('is_gpt_available')}")
                    print(f"   Last Analysis: {system_status.get('last_analysis_time')}")
                    print("✅ JSON serialization working correctly!")
                
        except json.JSONDecodeError as e:
            print(f"❌ JSON decode error: {e}")
            error_occurred = True
        except Exception as e:
            print(f"❌ Message processing error: {e}")
            error_occurred = True
    
    def on_error(ws, error):
        nonlocal error_occurred
        print(f"❌ WebSocket error: {error}")
        error_occurred = True
    
    def on_open(ws):
        print("✅ WebSocket connected")
    
    def on_close(ws, close_status_code, close_msg):
        print("📡 WebSocket disconnected")
    
    # Connect to WebSocket
    try:
        ws = websocket.WebSocketApp(
            "ws://localhost:8001/ws",
            on_message=on_message,
            on_error=on_error,
            on_open=on_open,
            on_close=on_close
        )
        
        # Run WebSocket in a separate thread
        ws_thread = threading.Thread(target=ws.run_forever)
        ws_thread.daemon = True
        ws_thread.start()
        
        # Wait for broadcasts (system status broadcasts every 5 seconds)
        print("⏳ Waiting for system status broadcasts...")
        time.sleep(15)  # Wait 15 seconds to receive multiple broadcasts
        
        ws.close()
        
    except Exception as e:
        print(f"❌ WebSocket connection error: {e}")
        return False
    
    # Test 3: Analyze results
    print(f"\n3. Analyzing results...")
    print(f"   Messages received: {len(messages_received)}")
    print(f"   Errors occurred: {error_occurred}")
    
    if error_occurred:
        print("❌ JSON serialization errors still present")
        return False
    elif len(messages_received) > 0:
        print("✅ JSON serialization working correctly")
        print("✅ No more 'Object of type datetime is not JSON serializable' errors")
        return True
    else:
        print("⚠️  No broadcasts received - may need more time")
        return False

def test_api_endpoints():
    """Test API endpoints for datetime serialization"""
    print("\n4. Testing API endpoints for datetime serialization...")
    
    try:
        # Test system status endpoint
        response = requests.get("http://localhost:8001/api/trading/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ System status endpoint working")
            if 'last_analysis_time' in data:
                print(f"   Last analysis time: {data['last_analysis_time']}")
                print("✅ Datetime serialization working in API")
        else:
            print(f"❌ System status endpoint failed: {response.status_code}")
            
    except Exception as e:
        print(f"❌ API test error: {e}")

if __name__ == "__main__":
    print("🎯 Testing Broadcast System Status Fix")
    print("=" * 50)
    
    success = test_websocket_broadcast()
    test_api_endpoints()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 BROADCAST FIX SUCCESSFUL!")
        print("✅ No more JSON serialization errors")
        print("✅ WebSocket broadcasts working correctly")
        print("✅ Datetime fields properly serialized")
        print("\n📝 Summary of fixes:")
        print("   - Added custom datetime serialization in broadcast_system_status()")
        print("   - Added json_encoders to Pydantic models")
        print("   - Datetime objects now converted to ISO format")
    else:
        print("❌ BROADCAST FIX INCOMPLETE")
        print("   Still experiencing JSON serialization issues")
    
    print(f"\n🔍 Check backend logs for:")
    print(f"   ✅ No 'Object of type datetime is not JSON serializable' errors")
    print(f"   ✅ Clean WebSocket broadcasts every 5 seconds")
