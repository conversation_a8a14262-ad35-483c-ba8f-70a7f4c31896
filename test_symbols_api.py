#!/usr/bin/env python3
"""
Test symbols API endpoints
"""
import requests
import json

BASE_URL = "http://localhost:8000"

def test_available_symbols():
    """Test getting available symbols"""
    url = f"{BASE_URL}/api/trading/symbols/available"
    
    try:
        response = requests.get(url)
        print(f"Available Symbols API - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            symbols = data.get('symbols', [])
            print(f"✅ Found {len(symbols)} available symbols")
            
            # Show forex pairs
            forex_symbols = [s for s in symbols if any(pair in s['name'].upper() 
                           for pair in ['EURUSD', 'GBPUSD', 'USDJPY', 'XAUUSD', 'BTCUSD'])]
            
            if forex_symbols:
                print(f"\n📊 Popular Trading Symbols Found:")
                for symbol in forex_symbols[:10]:  # Show first 10
                    spread = symbol.get('spread', 0)
                    selected = "✅" if symbol.get('selected') else "❌"
                    print(f"  {symbol['name']:<10} | Spread: {spread:<8.5f} | Selected: {selected}")
            
            # Show crypto symbols
            crypto_symbols = [s for s in symbols if any(crypto in s['name'].upper() 
                            for crypto in ['BTC', 'ETH', 'LTC', 'XRP'])]
            
            if crypto_symbols:
                print(f"\n💰 Crypto Symbols Found:")
                for symbol in crypto_symbols[:5]:
                    spread = symbol.get('spread', 0)
                    selected = "✅" if symbol.get('selected') else "❌"
                    print(f"  {symbol['name']:<10} | Spread: {spread:<8.5f} | Selected: {selected}")
            
            # Show metals
            metal_symbols = [s for s in symbols if any(metal in s['name'].upper() 
                           for metal in ['XAU', 'XAG', 'GOLD', 'SILVER'])]
            
            if metal_symbols:
                print(f"\n🥇 Precious Metals Found:")
                for symbol in metal_symbols:
                    spread = symbol.get('spread', 0)
                    selected = "✅" if symbol.get('selected') else "❌"
                    print(f"  {symbol['name']:<10} | Spread: {spread:<8.5f} | Selected: {selected}")
            
            return symbols
        else:
            print(f"❌ Error: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Error getting available symbols: {e}")
        return []

def test_current_symbols():
    """Test getting current symbols"""
    url = f"{BASE_URL}/api/trading/symbols/current"
    
    try:
        response = requests.get(url)
        print(f"\nCurrent Symbols API - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            symbols = data.get('symbols', [])
            print(f"✅ Current trading symbols: {symbols}")
            return symbols
        else:
            print(f"❌ Error: {response.text}")
            return []
    except Exception as e:
        print(f"❌ Error getting current symbols: {e}")
        return []

def test_update_symbols(new_symbols):
    """Test updating symbols"""
    url = f"{BASE_URL}/api/trading/symbols/update"
    data = {"symbols": new_symbols}
    
    try:
        response = requests.post(url, json=data)
        print(f"\nUpdate Symbols API - Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ {result.get('message')}")
            print(f"📝 Note: {result.get('note')}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error updating symbols: {e}")
        return False

def suggest_popular_symbols(available_symbols):
    """Suggest popular symbols that are available"""
    popular_symbols = [
        'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD',
        'XAUUSD', 'XAGUSD', 'BTCUSD', 'ETHUSD', 'US30', 'US500'
    ]
    
    available_names = [s['name'] for s in available_symbols]
    found_popular = [symbol for symbol in popular_symbols if symbol in available_names]
    
    if found_popular:
        print(f"\n🎯 Suggested Popular Symbols Available in Your Broker:")
        for symbol in found_popular:
            symbol_info = next((s for s in available_symbols if s['name'] == symbol), None)
            if symbol_info:
                spread = symbol_info.get('spread', 0)
                print(f"  {symbol:<10} | Spread: {spread:<8.5f}")
        
        return found_popular
    else:
        print(f"\n⚠️  No popular symbols found in your broker")
        return []

if __name__ == "__main__":
    print("🔍 === Testing Symbols API ===")
    
    # Test current symbols
    current = test_current_symbols()
    
    # Test available symbols
    available = test_available_symbols()
    
    if available:
        # Suggest popular symbols
        suggested = suggest_popular_symbols(available)
        
        if suggested:
            print(f"\n💡 Would you like to update to popular symbols?")
            print(f"Current: {current}")
            print(f"Suggested: {suggested[:5]}")  # Limit to 5 symbols
            
            choice = input("\nUpdate to suggested symbols? (y/n): ").strip().lower()
            if choice == 'y':
                test_update_symbols(suggested[:5])
    
    print(f"\n📋 Summary:")
    print(f"  - Available symbols in broker: {len(available)}")
    print(f"  - Current trading symbols: {len(current)}")
    print(f"  - To add more symbols, use the update_trading_symbols.py script")
    print(f"  - Or use the API endpoints to manage symbols programmatically")
