<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend Real-time Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .positions-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .positions-table th,
        .positions-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .positions-table th {
            background-color: #f2f2f2;
        }
        .profit-positive { color: #28a745; font-weight: bold; }
        .profit-negative { color: #dc3545; font-weight: bold; }
        .button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .button:hover {
            background-color: #0056b3;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        .stat-card {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 Frontend Real-time Position Updates Test</h1>
        
        <div id="connectionStatus" class="status disconnected">
            ❌ WebSocket Disconnected
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-value" id="messageCount">0</div>
                <div class="stat-label">Messages Received</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="positionCount">0</div>
                <div class="stat-label">Active Positions</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="renderCount">0</div>
                <div class="stat-label">UI Updates</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="lastUpdate">Never</div>
                <div class="stat-label">Last Update</div>
            </div>
        </div>

        <div>
            <button class="button" onclick="connectWebSocket()">🔌 Connect WebSocket</button>
            <button class="button" onclick="disconnectWebSocket()">❌ Disconnect</button>
            <button class="button" onclick="loadPositionsAPI()">📊 Load via API</button>
            <button class="button" onclick="clearLog()">🧹 Clear Log</button>
        </div>

        <h3>📊 Real-time Positions</h3>
        <table class="positions-table">
            <thead>
                <tr>
                    <th>Symbol</th>
                    <th>Type</th>
                    <th>Entry Price</th>
                    <th>Current Price</th>
                    <th>P&L</th>
                    <th>Status</th>
                    <th>Last Update</th>
                </tr>
            </thead>
            <tbody id="positionsTableBody">
                <tr>
                    <td colspan="7" style="text-align: center; color: #6c757d;">
                        No positions loaded yet
                    </td>
                </tr>
            </tbody>
        </table>

        <h3>📝 Real-time Log</h3>
        <div id="log" class="log">
            <div>🚀 Frontend Real-time Test initialized</div>
            <div>💡 Click "Connect WebSocket" to start receiving real-time updates</div>
        </div>
    </div>

    <script>
        let ws = null;
        let messageCount = 0;
        let renderCount = 0;
        let positions = [];

        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        function updateConnectionStatus(connected) {
            const statusDiv = document.getElementById('connectionStatus');
            if (connected) {
                statusDiv.className = 'status connected';
                statusDiv.innerHTML = '✅ WebSocket Connected';
            } else {
                statusDiv.className = 'status disconnected';
                statusDiv.innerHTML = '❌ WebSocket Disconnected';
            }
        }

        function updateStats() {
            document.getElementById('messageCount').textContent = messageCount;
            document.getElementById('positionCount').textContent = positions.length;
            document.getElementById('renderCount').textContent = renderCount;
            document.getElementById('lastUpdate').textContent = new Date().toLocaleTimeString();
        }

        function updatePositionsTable() {
            renderCount++;
            const tbody = document.getElementById('positionsTableBody');
            
            if (positions.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="7" style="text-align: center; color: #6c757d;">
                            No positions available
                        </td>
                    </tr>
                `;
            } else {
                tbody.innerHTML = positions.map(pos => {
                    const profitClass = (pos.profit || 0) >= 0 ? 'profit-positive' : 'profit-negative';
                    return `
                        <tr>
                            <td>${pos.symbol || 'N/A'}</td>
                            <td>${pos.order_type || 'N/A'}</td>
                            <td>$${pos.entry_price?.toFixed(5) || 'N/A'}</td>
                            <td>$${pos.current_price?.toFixed(5) || 'N/A'}</td>
                            <td class="${profitClass}">$${(pos.profit || 0).toFixed(2)}</td>
                            <td>${pos.status || 'N/A'}</td>
                            <td>${new Date().toLocaleTimeString()}</td>
                        </tr>
                    `;
                }).join('');
            }
            
            updateStats();
            log(`🎨 UI updated with ${positions.length} positions (render #${renderCount})`);
        }

        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ WebSocket already connected');
                return;
            }

            log('🔌 Connecting to WebSocket...');
            ws = new WebSocket('ws://localhost:8001/ws');

            ws.onopen = function() {
                log('✅ WebSocket connected successfully');
                updateConnectionStatus(true);
            };

            ws.onmessage = function(event) {
                messageCount++;
                try {
                    const data = JSON.parse(event.data);
                    log(`📨 Message #${messageCount}: ${data.type || 'unknown'}`);
                    
                    if (data.type === 'position_update') {
                        log(`💰 Position update: ${data.positions?.length || 0} positions`);
                        if (data.positions) {
                            positions = data.positions;
                            updatePositionsTable();
                            
                            // Log P&L details
                            data.positions.forEach(pos => {
                                log(`   📊 ${pos.symbol}: Entry $${pos.entry_price?.toFixed(5)}, Current $${pos.current_price?.toFixed(5)}, P&L $${pos.profit?.toFixed(2)}`);
                            });
                        }
                    } else if (data.type === 'system_update') {
                        const systemData = data.data || {};
                        log(`🖥️ System update: ${systemData.positions_count || 0} positions, Balance $${systemData.account_balance?.toFixed(2) || 0}`);
                    } else {
                        log(`📝 Other message: ${JSON.stringify(data).substring(0, 100)}...`);
                    }
                } catch (error) {
                    log(`❌ Error parsing message: ${error.message}`);
                }
            };

            ws.onclose = function() {
                log('❌ WebSocket disconnected');
                updateConnectionStatus(false);
            };

            ws.onerror = function(error) {
                log(`❌ WebSocket error: ${error}`);
                updateConnectionStatus(false);
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                log('🔌 WebSocket disconnected manually');
                updateConnectionStatus(false);
            }
        }

        async function loadPositionsAPI() {
            try {
                log('📊 Loading positions via API...');
                const response = await fetch('http://localhost:8001/api/trading/positions?realtime_pnl=true');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                positions = data || [];
                log(`✅ Loaded ${positions.length} positions via API`);
                updatePositionsTable();
                
                // Log position details
                positions.forEach(pos => {
                    log(`   📊 ${pos.symbol}: Entry $${pos.entry_price?.toFixed(5)}, Current $${pos.current_price?.toFixed(5)}, P&L $${pos.profit?.toFixed(2)}`);
                });
                
            } catch (error) {
                log(`❌ Error loading positions: ${error.message}`);
            }
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '<div>🧹 Log cleared</div>';
            messageCount = 0;
            renderCount = 0;
            updateStats();
        }

        // Auto-connect on page load
        window.onload = function() {
            log('🚀 Page loaded, auto-connecting WebSocket...');
            connectWebSocket();
        };
    </script>
</body>
</html>
