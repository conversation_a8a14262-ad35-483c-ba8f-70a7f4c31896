#!/usr/bin/env python3
"""
Auto Trading System Launcher
Men<PERSON> backend dan frontend secara bersa<PERSON>an
"""
import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path

class SystemLauncher:
    def __init__(self):
        self.backend_process = None
        self.frontend_process = None
        self.running = True

    def check_requirements(self):
        """Check if all requirements are met"""
        print("🔍 Checking system requirements...")

        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ required")
            return False
        print("✅ Python version OK")

        # Check if .env file exists
        if not Path(".env").exists():
            print("❌ .env file not found. Please copy .env.example to .env and configure it.")
            return False
        print("✅ .env file found")

        # Check if requirements.txt dependencies are installed
        try:
            import fastapi
            import MetaTrader5
            import openai
            print("✅ Python dependencies OK")
        except ImportError as e:
            print(f"❌ Missing Python dependency: {e}")
            print("Run: pip install -r requirements.txt")
            return False

        # Check if Node.js is available
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js version: {result.stdout.strip()}")
            else:
                print("❌ Node.js not found")
                return False
        except FileNotFoundError:
            print("❌ Node.js not found")
            return False

        # Check if frontend dependencies are installed
        if not Path("frontend/node_modules").exists():
            print("❌ Frontend dependencies not installed")
            print("Run: cd frontend && npm install")
            return False
        print("✅ Frontend dependencies OK")

        return True

    def start_backend(self):
        """Start the backend server"""
        print("🚀 Starting backend server...")
        try:
            self.backend_process = subprocess.Popen(
                [sys.executable, "-m", "backend.main"],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Monitor backend output in a separate thread
            threading.Thread(
                target=self.monitor_process,
                args=(self.backend_process, "BACKEND"),
                daemon=True
            ).start()

            print("✅ Backend server started")
            return True

        except Exception as e:
            print(f"❌ Failed to start backend: {e}")
            return False

    def start_frontend(self):
        """Start the frontend development server"""
        print("🚀 Starting frontend server...")
        try:
            # Change to frontend directory
            frontend_dir = Path("frontend")

            self.frontend_process = subprocess.Popen(
                ["npm", "run", "dev"],
                cwd=frontend_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )

            # Monitor frontend output in a separate thread
            threading.Thread(
                target=self.monitor_process,
                args=(self.frontend_process, "FRONTEND"),
                daemon=True
            ).start()

            print("✅ Frontend server started")
            return True

        except Exception as e:
            print(f"❌ Failed to start frontend: {e}")
            return False

    def monitor_process(self, process, name):
        """Monitor process output"""
        while self.running and process.poll() is None:
            try:
                line = process.stdout.readline()
                if line:
                    print(f"[{name}] {line.strip()}")
            except:
                break

    def stop_processes(self):
        """Stop all processes"""
        print("\n🛑 Stopping all processes...")
        self.running = False

        if self.backend_process:
            try:
                self.backend_process.terminate()
                self.backend_process.wait(timeout=5)
                print("✅ Backend stopped")
            except subprocess.TimeoutExpired:
                self.backend_process.kill()
                print("⚠️ Backend force killed")
            except Exception as e:
                print(f"❌ Error stopping backend: {e}")

        if self.frontend_process:
            try:
                self.frontend_process.terminate()
                self.frontend_process.wait(timeout=5)
                print("✅ Frontend stopped")
            except subprocess.TimeoutExpired:
                self.frontend_process.kill()
                print("⚠️ Frontend force killed")
            except Exception as e:
                print(f"❌ Error stopping frontend: {e}")

    def signal_handler(self, signum, frame):
        """Handle interrupt signals"""
        print(f"\n📡 Received signal {signum}")
        self.stop_processes()
        sys.exit(0)

    def wait_for_servers(self):
        """Wait for servers to be ready"""
        print("⏳ Waiting for servers to start...")

        # Wait for backend
        backend_ready = False
        for i in range(30):  # Wait up to 30 seconds
            try:
                import requests
                response = requests.get("http://localhost:8001/api/health", timeout=1)
                if response.status_code == 200:
                    backend_ready = True
                    break
            except:
                pass
            time.sleep(1)

        if backend_ready:
            print("✅ Backend is ready at http://localhost:8001")
        else:
            print("⚠️ Backend may not be ready yet")

        # Wait a bit more for frontend
        time.sleep(3)
        print("✅ Frontend should be ready at http://localhost:3000")

    def run(self):
        """Main run method"""
        print("🤖 Auto Trading System Launcher")
        print("=" * 50)

        # Setup signal handlers
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)

        # Check requirements
        if not self.check_requirements():
            print("\n❌ Requirements check failed. Please fix the issues above.")
            return False

        print("\n🎯 All requirements met!")

        # Start backend
        if not self.start_backend():
            return False

        # Wait a bit for backend to start
        time.sleep(2)

        # Start frontend
        if not self.start_frontend():
            self.stop_processes()
            return False

        # Wait for servers to be ready
        self.wait_for_servers()

        print("\n🎉 Auto Trading System is running!")
        print("📊 Frontend: http://localhost:3000")
        print("🔧 Backend API: http://localhost:8001")
        print("📚 API Docs: http://localhost:8001/docs")
        print("\n💡 Press Ctrl+C to stop the system")

        try:
            # Keep the main thread alive
            while self.running:
                time.sleep(1)

                # Check if processes are still running
                if self.backend_process and self.backend_process.poll() is not None:
                    print("❌ Backend process died")
                    break

                if self.frontend_process and self.frontend_process.poll() is not None:
                    print("❌ Frontend process died")
                    break

        except KeyboardInterrupt:
            pass
        finally:
            self.stop_processes()

        return True


def main():
    """Main function"""
    launcher = SystemLauncher()
    success = launcher.run()

    if success:
        print("\n✅ System stopped successfully")
    else:
        print("\n❌ System stopped with errors")
        sys.exit(1)


if __name__ == "__main__":
    main()
