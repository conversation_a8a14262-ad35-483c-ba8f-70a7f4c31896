#!/usr/bin/env python3
"""
Test Root Cause Fix
Verify that the actual root cause has been fixed, not just error handling
"""
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_root_cause_fix():
    """Test that the root cause has been fixed"""
    print("🔧 Testing Root Cause Fix")
    print("=" * 50)
    
    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.services.gpt_analyzer import GPTAnalyzer
        from backend.utils.technical_analysis import TechnicalAnalyzer
        
        # Connect to MT5
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ Cannot connect to MT5")
            return False
        
        print("✅ MT5 connected")
        
        # Test symbol
        symbol = "BTCUSDm"
        
        # Get market data
        market_data = await connector.get_market_data(symbol)
        if not market_data:
            print(f"❌ No market data for {symbol}")
            await connector.disconnect()
            return False
        
        print(f"✅ Market data: ${market_data.mid_price:,.5f}")
        
        # Get multi-timeframe data
        timeframe_data = await connector.get_multi_timeframe_data(
            symbol, 
            timeframes=["15m", "30m", "1h", "4h"]
        )
        
        print(f"✅ Timeframe data: {[f'{tf}:{len(data)}' for tf, data in timeframe_data.items()]}")
        
        if not any(timeframe_data.values()):
            print("❌ No timeframe data available")
            await connector.disconnect()
            return False
        
        # Test 1: Technical Indicators Calculation
        print("\n1️⃣ Testing Technical Indicators Calculation...")
        technical_analyzer = TechnicalAnalyzer()
        technical_indicators = {}
        
        for tf, ohlcv_data in timeframe_data.items():
            if ohlcv_data:
                try:
                    indicators = technical_analyzer.calculate_indicators(ohlcv_data)
                    if indicators:
                        technical_indicators[tf] = indicators
                        print(f"✅ {tf}: Technical indicators calculated successfully")
                        
                        # Test .dict() method access
                        indicators_dict = indicators.dict()
                        print(f"   📊 {tf}: .dict() method works - {len(indicators_dict)} fields")
                    else:
                        print(f"⚠️ {tf}: No indicators (insufficient data)")
                except Exception as e:
                    print(f"❌ {tf}: Error - {e}")
                    await connector.disconnect()
                    return False
        
        # Test 2: GPT Analyzer Initialization
        print("\n2️⃣ Testing GPT Analyzer...")
        try:
            gpt_analyzer = GPTAnalyzer()
            print("✅ GPT Analyzer initialized")
        except Exception as e:
            print(f"❌ GPT Analyzer initialization failed: {e}")
            await connector.disconnect()
            return False
        
        # Test 3: Data Preparation (where the error occurred)
        print("\n3️⃣ Testing Data Preparation (Root Cause Area)...")
        try:
            analysis_data = gpt_analyzer._prepare_multi_timeframe_data(
                symbol, market_data, timeframe_data, technical_indicators
            )
            print(f"✅ Multi-timeframe data preparation successful")
            print(f"   📊 Timeframes processed: {list(analysis_data.get('timeframes', {}).keys())}")
            
            # Verify each timeframe has technical indicators properly handled
            for tf, tf_data in analysis_data.get('timeframes', {}).items():
                indicators = tf_data.get('technical_indicators')
                if indicators:
                    print(f"   ✅ {tf}: Technical indicators properly processed")
                else:
                    print(f"   ⚠️ {tf}: No technical indicators (expected for insufficient data)")
                    
        except Exception as e:
            print(f"❌ Data preparation failed: {e}")
            import traceback
            traceback.print_exc()
            await connector.disconnect()
            return False
        
        # Test 4: Function Return Type
        print("\n4️⃣ Testing Function Return Type...")
        try:
            # This should now return Dict[str, Any] instead of GPTAnalysis
            analysis = await gpt_analyzer.analyze_market_multi_timeframe(
                symbol=symbol,
                market_data=market_data,
                timeframe_data=timeframe_data,
                technical_indicators=technical_indicators
            )
            
            if analysis is None:
                print("⚠️ Analysis returned None (could be API issue)")
                await connector.disconnect()
                return True  # This is acceptable
            elif isinstance(analysis, dict):
                print("✅ Function returns Dict[str, Any] as expected")
                print(f"   📊 Decision: {analysis.get('decision', 'UNKNOWN')}")
                print(f"   🎯 Confidence: {analysis.get('confidence', 0):.2f}")
            else:
                print(f"❌ Function returns unexpected type: {type(analysis)}")
                await connector.disconnect()
                return False
                
        except Exception as e:
            print(f"❌ Multi-timeframe analysis failed: {e}")
            import traceback
            traceback.print_exc()
            await connector.disconnect()
            return False
        
        # Test 5: Safe Indicators Function
        print("\n5️⃣ Testing Safe Indicators Function...")
        try:
            # Test with valid indicators
            for tf, indicators in technical_indicators.items():
                safe_indicators = gpt_analyzer._safe_get_indicators(technical_indicators, tf)
                if safe_indicators:
                    print(f"✅ {tf}: Safe indicators function works")
                else:
                    print(f"⚠️ {tf}: Safe indicators returned None")
            
            # Test with None
            safe_none = gpt_analyzer._safe_get_indicators(None, "15m")
            print(f"✅ None input handled safely: {safe_none}")
            
            # Test with empty dict
            safe_empty = gpt_analyzer._safe_get_indicators({}, "15m")
            print(f"✅ Empty dict handled safely: {safe_empty}")
            
        except Exception as e:
            print(f"❌ Safe indicators test failed: {e}")
            await connector.disconnect()
            return False
        
        await connector.disconnect()
        print("\n✅ All root cause tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_old_function_fix():
    """Test that the old function also works"""
    print("\n🔧 Testing Old Function Fix")
    print("=" * 40)
    
    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.services.gpt_analyzer import GPTAnalyzer
        from backend.utils.technical_analysis import TechnicalAnalyzer
        
        # Connect to MT5
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ Cannot connect to MT5")
            return False
        
        symbol = "BTCUSDm"
        
        # Get single timeframe data
        ohlcv_data = await connector.get_ohlcv_data(symbol, "15m", 100)
        if not ohlcv_data:
            print("❌ No OHLCV data")
            await connector.disconnect()
            return False
        
        # Get market data
        market_data = await connector.get_market_data(symbol)
        if not market_data:
            print("❌ No market data")
            await connector.disconnect()
            return False
        
        # Calculate technical indicators
        technical_analyzer = TechnicalAnalyzer()
        technical_indicators = technical_analyzer.calculate_indicators(ohlcv_data)
        
        # Test old function
        gpt_analyzer = GPTAnalyzer()
        
        # This should not crash with .dict() error anymore
        analysis = await gpt_analyzer.analyze_market(
            symbol=symbol,
            market_data=market_data,
            ohlcv_data=ohlcv_data,
            technical_indicators=technical_indicators
        )
        
        if analysis:
            print("✅ Old function works without .dict() error")
            print(f"   Signal: {analysis.signal}")
            print(f"   Confidence: {analysis.confidence:.2f}")
        else:
            print("⚠️ Old function returned None (could be API issue)")
        
        await connector.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Old function test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 ROOT CAUSE FIX VERIFICATION")
    print("=" * 60)
    
    # Test 1: Root cause fix
    success1 = await test_root_cause_fix()
    
    # Test 2: Old function fix
    success2 = await test_old_function_fix()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ ROOT CAUSE SUCCESSFULLY FIXED!")
        print("\n🎉 FIXES IMPLEMENTED:")
        print("   ✅ Fixed function return type: Dict[str, Any] instead of GPTAnalysis")
        print("   ✅ Fixed technical_indicators.dict() access with proper validation")
        print("   ✅ Added safe indicators handling function")
        print("   ✅ Fixed both new and old functions")
        print("   ✅ No more 'NoneType' object has no attribute 'dict' errors")
    else:
        print("❌ Some tests failed - root cause may not be fully fixed")

if __name__ == "__main__":
    asyncio.run(main())
