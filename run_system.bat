@echo off
echo 🤖 Auto Trading System Launcher
echo ================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python not found. Please install Python 3.8+
    pause
    exit /b 1
)

REM Check if .env file exists
if not exist ".env" (
    echo ❌ .env file not found
    echo Please copy .env.example to .env and configure it
    pause
    exit /b 1
)

REM Run the Python launcher
echo 🚀 Starting Auto Trading System...
python run_system.py

pause
