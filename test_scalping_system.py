#!/usr/bin/env python3
"""
Test script untuk SCALPING TRADING SYSTEM
Test semua komponen dan flow scalping
"""

import asyncio
import sys
import os
from datetime import datetime

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.services.data_fetcher import DataFetcher
from backend.services.scalping_gpt_analyzer import ScalpingGPTAnalyzer
from backend.services.trigger_monitor import TriggerMonitor
from backend.services.order_manager import OrderManager
from backend.services.price_monitor import PriceMonitor
from backend.services.reevaluation_service import ReevaluationService
from backend.services.evaluator_service import EvaluatorService
from backend.services.scalping_engine import ScalpingEngine

async def test_data_fetcher():
    """Test Data Fetcher component"""
    print("1️⃣ TESTING DATA FETCHER")
    print("=" * 50)
    
    try:
        data_fetcher = DataFetcher()
        
        # Test connection
        connected = await data_fetcher.connect_mt5()
        print(f"   MT5 Connection: {'✅ SUCCESS' if connected else '❌ FAILED'}")
        
        if not connected:
            return False
        
        # Test scalping data
        symbol = "BTCUSDm"
        scalping_data = await data_fetcher.get_scalping_data(symbol, "M15")
        
        if scalping_data:
            print(f"   📊 Scalping Data for {symbol}: ✅ SUCCESS")
            print(f"      RSI: {scalping_data.get('rsi', 'N/A')}")
            print(f"      ATR: {scalping_data.get('atr', 'N/A')}")
            print(f"      Volume Change: {scalping_data.get('volume_metrics', {}).get('volume_increase_percent', 'N/A')}%")
            print(f"      Data Quality: {scalping_data.get('data_quality', 'N/A')}")
            print(f"      Candles Count: {len(scalping_data.get('candles', []))}")
        else:
            print(f"   📊 Scalping Data for {symbol}: ❌ FAILED")
            return False
        
        # Test current price
        current_price = await data_fetcher.get_current_price(symbol)
        print(f"   💰 Current Price: {'✅ ' + str(current_price) if current_price else '❌ FAILED'}")
        
        await data_fetcher.disconnect_mt5()
        return True
        
    except Exception as e:
        print(f"   ❌ Data Fetcher Error: {e}")
        return False

async def test_gpt_analyzer():
    """Test GPT Analyzer component"""
    print("\n2️⃣ TESTING GPT ANALYZER")
    print("=" * 50)
    
    try:
        gpt_analyzer = ScalpingGPTAnalyzer()
        data_fetcher = DataFetcher()
        
        # Get test data
        await data_fetcher.connect_mt5()
        scalping_data = await data_fetcher.get_scalping_data("BTCUSDm", "M15")
        
        if not scalping_data:
            print("   ❌ No scalping data for GPT test")
            return False
        
        # Test GPT analysis
        print("   🧠 Sending data to GPT-4o...")
        gpt_analysis = await gpt_analyzer.analyze_scalping_opportunity(scalping_data)
        
        if gpt_analysis:
            print(f"   ✅ GPT Analysis: SUCCESS")
            print(f"      Decision: {gpt_analysis.get('analisis', 'N/A')}")
            print(f"      Reason: {gpt_analysis.get('alasan', 'N/A')[:100]}...")
            print(f"      Entry Price: {gpt_analysis.get('entry_price', 'N/A')}")
            print(f"      Stop Loss: {gpt_analysis.get('stop_loss', 'N/A')}")
            print(f"      Take Profit: {gpt_analysis.get('take_profit', 'N/A')}")
        else:
            print("   ❌ GPT Analysis: FAILED")
            return False
        
        await data_fetcher.disconnect_mt5()
        return True
        
    except Exception as e:
        print(f"   ❌ GPT Analyzer Error: {e}")
        return False

async def test_trigger_monitor():
    """Test Trigger Monitor component"""
    print("\n3️⃣ TESTING TRIGGER MONITOR")
    print("=" * 50)
    
    try:
        trigger_monitor = TriggerMonitor()
        
        # Test trigger check
        symbol = "BTCUSDm"
        trigger_status = await trigger_monitor.manual_trigger_check(symbol)
        
        if "error" not in trigger_status:
            print(f"   ✅ Trigger Check: SUCCESS")
            print(f"      Current Price: ${trigger_status.get('current_price', 'N/A')}")
            print(f"      Movement: {trigger_status.get('movement_percent', 'N/A')}%")
            print(f"      Threshold: {trigger_status.get('movement_threshold', 'N/A')}%")
            print(f"      Trigger Ready: {'✅ YES' if trigger_status.get('trigger_ready') else '❌ NO'}")
        else:
            print(f"   ❌ Trigger Check: FAILED - {trigger_status['error']}")
            return False
        
        # Test monitoring status
        monitoring_status = trigger_monitor.get_monitoring_status()
        print(f"   📊 Monitoring Status: ✅ SUCCESS")
        print(f"      Pair Thresholds: {len(monitoring_status.get('pair_thresholds', {}))}")
        print(f"      Min Interval: {monitoring_status.get('min_interval_minutes', 'N/A')} minutes")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Trigger Monitor Error: {e}")
        return False

async def test_price_monitor():
    """Test Price Monitor component"""
    print("\n4️⃣ TESTING PRICE MONITOR")
    print("=" * 50)
    
    try:
        price_monitor = PriceMonitor()
        
        # Test manual price check
        symbol = "BTCUSDm"
        price_check = await price_monitor.manual_price_check(symbol)
        
        if "error" not in price_check:
            print(f"   ✅ Price Check: SUCCESS")
            print(f"      Current Price: ${price_check.get('current_price', 'N/A')}")
            
            movement_analysis = price_check.get('movement_analysis', {})
            print(f"      Movement: {movement_analysis.get('movement_percent', 'N/A')}%")
            print(f"      Direction: {movement_analysis.get('direction', 'N/A')}")
            print(f"      Significant: {'✅ YES' if movement_analysis.get('significant_move') else '❌ NO'}")
        else:
            print(f"   ❌ Price Check: FAILED - {price_check['error']}")
            return False
        
        # Test monitoring status
        monitoring_status = price_monitor.get_monitoring_status()
        print(f"   📊 Monitoring Status: ✅ SUCCESS")
        print(f"      Window: {monitoring_status.get('window_minutes', 'N/A')} minutes")
        print(f"      Check Interval: {monitoring_status.get('check_interval_seconds', 'N/A')} seconds")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Price Monitor Error: {e}")
        return False

async def test_order_manager():
    """Test Order Manager component"""
    print("\n5️⃣ TESTING ORDER MANAGER")
    print("=" * 50)
    
    try:
        order_manager = OrderManager()
        
        # Test get active orders
        active_orders = order_manager.get_active_orders()
        print(f"   ✅ Active Orders: SUCCESS")
        print(f"      Count: {active_orders.get('count', 0)}")
        
        # Test buffer zone check
        buffer_orders = await order_manager.check_buffer_zones()
        print(f"   📏 Buffer Check: ✅ SUCCESS")
        print(f"      Orders in buffer: {len(buffer_orders)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Order Manager Error: {e}")
        return False

async def test_reevaluation_service():
    """Test Reevaluation Service component"""
    print("\n6️⃣ TESTING REEVALUATION SERVICE")
    print("=" * 50)
    
    try:
        reevaluation_service = ReevaluationService()
        
        # Test reevaluation check
        reevaluation_results = await reevaluation_service.check_and_reevaluate_orders()
        print(f"   ✅ Reevaluation Check: SUCCESS")
        print(f"      Orders reevaluated: {len(reevaluation_results)}")
        
        # Test statistics
        stats = reevaluation_service.get_reevaluation_statistics()
        if "error" not in stats:
            print(f"   📊 Statistics: ✅ SUCCESS")
            print(f"      Total orders: {stats.get('total_orders_reevaluated', 0)}")
            print(f"      Total reevaluations: {stats.get('total_reevaluations', 0)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Reevaluation Service Error: {e}")
        return False

async def test_evaluator_service():
    """Test Evaluator Service component"""
    print("\n7️⃣ TESTING EVALUATOR SERVICE")
    print("=" * 50)
    
    try:
        evaluator_service = EvaluatorService()
        
        # Test evaluation summary
        summary = evaluator_service.get_evaluation_summary()
        
        if "error" not in summary:
            print(f"   ✅ Evaluation Summary: SUCCESS")
            print(f"      Total evaluations: {summary.get('total_evaluations', 0)}")
            print(f"      Symbols evaluated: {len(summary.get('symbols_evaluated', []))}")
        else:
            print(f"   ❌ Evaluation Summary: FAILED - {summary['error']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"   ❌ Evaluator Service Error: {e}")
        return False

async def test_scalping_engine():
    """Test complete Scalping Engine"""
    print("\n8️⃣ TESTING SCALPING ENGINE")
    print("=" * 50)
    
    try:
        scalping_engine = ScalpingEngine()
        
        # Test engine status
        status = scalping_engine.get_engine_status()
        print(f"   ✅ Engine Status: SUCCESS")
        print(f"      Running: {status.get('engine_running', False)}")
        print(f"      Monitored Symbols: {len(status.get('monitored_symbols', []))}")
        print(f"      Active Orders: {status.get('active_orders_count', 0)}")
        
        # Test manual analysis
        symbol = "BTCUSDm"
        analysis_result = await scalping_engine.manual_analyze_symbol(symbol)
        
        if analysis_result.get("success"):
            print(f"   🧠 Manual Analysis: ✅ SUCCESS")
            gpt_analysis = analysis_result.get("gpt_analysis", {})
            print(f"      Decision: {gpt_analysis.get('analisis', 'N/A')}")
        else:
            print(f"   🧠 Manual Analysis: ❌ FAILED - {analysis_result.get('error', 'Unknown error')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Scalping Engine Error: {e}")
        return False

async def run_complete_test():
    """Run complete scalping system test"""
    print("🧪 SCALPING SYSTEM COMPREHENSIVE TEST")
    print("=" * 70)
    print(f"🕒 Test Started: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("")
    
    test_results = []
    
    # Run all tests
    tests = [
        ("Data Fetcher", test_data_fetcher),
        ("GPT Analyzer", test_gpt_analyzer),
        ("Trigger Monitor", test_trigger_monitor),
        ("Price Monitor", test_price_monitor),
        ("Order Manager", test_order_manager),
        ("Reevaluation Service", test_reevaluation_service),
        ("Evaluator Service", test_evaluator_service),
        ("Scalping Engine", test_scalping_engine)
    ]
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ {test_name} Test Failed: {e}")
            test_results.append((test_name, False))
    
    # Print summary
    print("\n" + "=" * 70)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 70)
    
    passed = 0
    failed = 0
    
    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {test_name}: {status}")
        
        if result:
            passed += 1
        else:
            failed += 1
    
    print("")
    print(f"📈 TOTAL TESTS: {len(test_results)}")
    print(f"✅ PASSED: {passed}")
    print(f"❌ FAILED: {failed}")
    print(f"📊 SUCCESS RATE: {(passed/len(test_results)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Scalping system is ready for deployment")
        print("\n🚀 NEXT STEPS:")
        print("   1. Run: python scalping_main.py")
        print("   2. Access API: http://localhost:8001/api/scalping/")
        print("   3. Check status: http://localhost:8001/api/scalping/status")
        print("   4. Monitor logs: logs/scalping_system.log")
    else:
        print(f"\n⚠️ {failed} TESTS FAILED")
        print("🔧 Please fix the issues before deploying")
    
    print("=" * 70)

if __name__ == "__main__":
    print("🎯 SCALPING TRADING SYSTEM - COMPREHENSIVE TEST")
    print("Testing all components and integration...")
    print("")
    
    try:
        asyncio.run(run_complete_test())
    except KeyboardInterrupt:
        print("\n🛑 Test interrupted by user")
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
