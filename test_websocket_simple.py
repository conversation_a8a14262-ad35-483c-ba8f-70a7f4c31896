#!/usr/bin/env python3
"""
Simple WebSocket test to verify real-time P&L data
"""
import asyncio
import websockets
import json

async def test_websocket():
    """Test WebSocket connection and data reception"""
    print("🔌 Connecting to WebSocket...")
    
    try:
        uri = "ws://localhost:8001/ws"
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected!")
            
            # Send a test message
            await websocket.send("Test real-time P&L")
            
            print("📡 Listening for real-time updates...")
            message_count = 0
            
            while message_count < 10:  # Listen for 10 messages
                try:
                    message = await asyncio.wait_for(websocket.recv(), timeout=10.0)
                    message_count += 1
                    
                    try:
                        data = json.loads(message)
                        msg_type = data.get('type', 'unknown')
                        
                        print(f"\n📨 Message #{message_count}: {msg_type}")
                        
                        if msg_type == 'position_update':
                            positions = data.get('positions', [])
                            print(f"💰 Position Update: {len(positions)} positions")
                            
                            for pos in positions:
                                symbol = pos.get('symbol', 'Unknown')
                                entry_price = pos.get('entry_price', 0)
                                current_price = pos.get('current_price', 0)
                                profit = pos.get('profit', 0)
                                order_type = pos.get('order_type', 'Unknown')
                                
                                print(f"   📊 {symbol} ({order_type}):")
                                print(f"      Entry: ${entry_price:.5f}")
                                print(f"      Current: ${current_price:.5f}")
                                print(f"      P&L: ${profit:.2f}")
                        
                        elif msg_type == 'system_update':
                            system_data = data.get('data', {})
                            positions_count = system_data.get('positions_count', 0)
                            account_balance = system_data.get('account_balance', 0)
                            print(f"🖥️ System Update: {positions_count} positions, Balance: ${account_balance:.2f}")
                        
                        else:
                            print(f"📝 Other message type: {msg_type}")
                    
                    except json.JSONDecodeError:
                        print(f"📝 Non-JSON message: {message[:100]}...")
                
                except asyncio.TimeoutError:
                    print("⏰ Timeout waiting for message")
                    break
            
            print(f"\n✅ Test completed! Received {message_count} messages")
            
    except Exception as e:
        print(f"❌ WebSocket error: {e}")

if __name__ == "__main__":
    asyncio.run(test_websocket())
