import MetaTrader5 as mt5
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional
import logging

logger = logging.getLogger(__name__)

class DataFetcher:
    """
    SCALPING DATA FETCHER
    - Ambil 30 candle terakhir dari MT5 (default: M15)
    - Format: list of dicts dengan time, open, high, low, close, volume
    - Hitung RSI(14), Volume metrics, ATR(14) untuk buffer logic
    """
    
    def __init__(self):
        self.mt5_connected = False
    
    async def connect_mt5(self) -> bool:
        """Connect to MetaTrader 5"""
        try:
            if not mt5.initialize():
                logger.error("❌ Failed to initialize MT5")
                return False
            
            self.mt5_connected = True
            logger.info("✅ Connected to MT5 for scalping data")
            return True
        except Exception as e:
            logger.error(f"❌ Error connecting to MT5: {e}")
            return False
    
    async def disconnect_mt5(self):
        """Disconnect from MetaTrader 5"""
        if self.mt5_connected:
            mt5.shutdown()
            self.mt5_connected = False
            logger.info("🔌 Disconnected from MT5")
    
    async def get_scalping_candles(
        self,
        symbol: str,
        timeframe: str = "M15",
        count: int = 30
    ) -> List[Dict]:
        """
        Get 30 candle terakhir untuk scalping analysis
        Format: {time, open, high, low, close, volume}
        """
        try:
            if not self.mt5_connected:
                await self.connect_mt5()
            
            # Convert timeframe string to MT5 constant
            tf_map = {
                "M1": mt5.TIMEFRAME_M1,
                "M5": mt5.TIMEFRAME_M5,
                "M15": mt5.TIMEFRAME_M15,
                "M30": mt5.TIMEFRAME_M30,
                "H1": mt5.TIMEFRAME_H1,
                "H4": mt5.TIMEFRAME_H4,
                "D1": mt5.TIMEFRAME_D1
            }
            
            mt5_timeframe = tf_map.get(timeframe, mt5.TIMEFRAME_M15)
            
            # Get rates from MT5
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
            
            if rates is None or len(rates) == 0:
                logger.error(f"❌ No candle data for {symbol} {timeframe}")
                return []
            
            # Convert to scalping format
            candles = []
            for rate in rates:
                candles.append({
                    'time': datetime.fromtimestamp(rate['time']),
                    'open': float(rate['open']),
                    'high': float(rate['high']),
                    'low': float(rate['low']),
                    'close': float(rate['close']),
                    'volume': int(rate['tick_volume'])
                })
            
            logger.info(f"📊 Fetched {len(candles)} scalping candles for {symbol} {timeframe}")
            return candles
            
        except Exception as e:
            logger.error(f"❌ Error fetching scalping candles for {symbol}: {e}")
            return []
    
    def calculate_rsi(self, prices: List[float], period: int = 14) -> float:
        """Calculate RSI(14) for scalping signals"""
        try:
            if len(prices) < period + 1:
                logger.warning(f"⚠️ Insufficient data for RSI calculation: {len(prices)} < {period + 1}")
                return 50.0  # Neutral RSI
            
            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)
            
            # Simple moving average for gains and losses
            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])
            
            if avg_loss == 0:
                return 100.0
            
            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))
            
            return round(rsi, 2)
        except Exception as e:
            logger.error(f"❌ Error calculating RSI: {e}")
            return 50.0
    
    def calculate_atr(self, candles: List[Dict], period: int = 14) -> float:
        """Calculate ATR(14) for buffer logic"""
        try:
            if len(candles) < period + 1:
                logger.warning(f"⚠️ Insufficient data for ATR calculation: {len(candles)} < {period + 1}")
                return 0.0
            
            true_ranges = []
            for i in range(1, len(candles)):
                current = candles[i]
                previous = candles[i-1]
                
                tr1 = current['high'] - current['low']
                tr2 = abs(current['high'] - previous['close'])
                tr3 = abs(current['low'] - previous['close'])
                
                true_range = max(tr1, tr2, tr3)
                true_ranges.append(true_range)
            
            atr = np.mean(true_ranges[-period:])
            return round(atr, 5)
        except Exception as e:
            logger.error(f"❌ Error calculating ATR: {e}")
            return 0.0
    
    def calculate_volume_metrics(self, candles: List[Dict]) -> Dict:
        """Calculate volume metrics for scalping validation"""
        try:
            if len(candles) < 3:
                return {
                    'last_volume': 0,
                    'avg_volume_3': 0,
                    'volume_increase_percent': 0.0
                }
            
            # Get last 3 candles volume
            last_3_volumes = [candle['volume'] for candle in candles[-3:]]
            last_volume = candles[-1]['volume']
            avg_volume_3 = np.mean(last_3_volumes[:-1])  # Exclude last candle from average
            
            # Calculate volume increase percentage
            volume_increase = 0.0
            if avg_volume_3 > 0:
                volume_increase = ((last_volume - avg_volume_3) / avg_volume_3) * 100
            
            return {
                'last_volume': last_volume,
                'avg_volume_3': round(avg_volume_3, 0),
                'volume_increase_percent': round(volume_increase, 2)
            }
        except Exception as e:
            logger.error(f"❌ Error calculating volume metrics: {e}")
            return {
                'last_volume': 0,
                'avg_volume_3': 0,
                'volume_increase_percent': 0.0
            }
    
    async def get_scalping_data(
        self,
        symbol: str,
        timeframe: str = "M15"
    ) -> Dict:
        """
        Get complete scalping data with indicators
        Returns: {candles, rsi, atr, volume_metrics, last_candle}
        """
        try:
            logger.info(f"🔍 Fetching scalping data for {symbol} {timeframe}")
            
            # Get 30 candles for scalping analysis
            candles = await self.get_scalping_candles(symbol, timeframe, count=30)
            
            if not candles or len(candles) < 15:
                logger.error(f"❌ Insufficient candle data for {symbol}: {len(candles) if candles else 0}")
                return {}
            
            # Extract close prices for RSI
            close_prices = [candle['close'] for candle in candles]
            
            # Calculate indicators
            rsi = self.calculate_rsi(close_prices, period=14)
            atr = self.calculate_atr(candles, period=14)
            volume_metrics = self.calculate_volume_metrics(candles)
            
            # Get last candle for current analysis
            last_candle = candles[-1]
            
            # Check candle structure
            candle_range = last_candle['high'] - last_candle['low']
            upper_wick = last_candle['high'] - max(last_candle['open'], last_candle['close'])
            lower_wick = min(last_candle['open'], last_candle['close']) - last_candle['low']
            body_size = abs(last_candle['close'] - last_candle['open'])
            
            scalping_data = {
                'symbol': symbol,
                'timeframe': timeframe,
                'candles': candles,
                'rsi': rsi,
                'atr': atr,
                'volume_metrics': volume_metrics,
                'last_candle': last_candle,
                'candle_structure': {
                    'range': round(candle_range, 5),
                    'upper_wick': round(upper_wick, 5),
                    'lower_wick': round(lower_wick, 5),
                    'body_size': round(body_size, 5),
                    'is_bullish': last_candle['close'] > last_candle['open']
                },
                'timestamp': datetime.now(),
                'data_quality': 'GOOD' if len(candles) >= 30 else 'LIMITED'
            }
            
            logger.info(f"✅ Scalping data ready for {symbol}: RSI={rsi}, ATR={atr}, Volume+{volume_metrics['volume_increase_percent']}%")
            return scalping_data
            
        except Exception as e:
            logger.error(f"❌ Error getting scalping data for {symbol}: {e}")
            return {}
    
    async def get_current_price(self, symbol: str) -> Optional[float]:
        """Get current market price for trigger monitoring"""
        try:
            if not self.mt5_connected:
                await self.connect_mt5()
            
            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                logger.error(f"❌ No tick data for {symbol}")
                return None
            
            # Use bid price for current price
            current_price = float(tick.bid)
            return current_price
            
        except Exception as e:
            logger.error(f"❌ Error getting current price for {symbol}: {e}")
            return None
