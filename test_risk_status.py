#!/usr/bin/env python3
"""
Test script to check current risk management status
"""
import requests
import json
from datetime import datetime

def test_risk_status():
    """Test risk status API"""
    print("🔍 CHECKING RISK MANAGEMENT STATUS")
    print("=" * 50)
    
    try:
        # Get risk status
        response = requests.get("http://localhost:8001/api/trading/risk-status")
        
        if response.status_code == 200:
            data = response.json()
            
            print("📊 ACCOUNT INFO:")
            account = data.get("account", {})
            print(f"   Balance: ${account.get('balance', 0):,.2f}")
            print(f"   Equity: ${account.get('equity', 0):,.2f}")
            print(f"   Free Margin: ${account.get('free_margin', 0):,.2f}")
            print(f"   Margin Level: {account.get('margin_level', 0):.2f}%")
            print(f"   Profit: ${account.get('profit', 0):,.2f}")
            
            print("\n⚠️ RISK METRICS:")
            risk = data.get("risk_metrics", {})
            print(f"   Current Drawdown: {risk.get('drawdown_percent', 0):.2f}%")
            print(f"   Max Drawdown Limit: {risk.get('max_drawdown_percent', 0):.2f}%")
            print(f"   Daily Loss: ${risk.get('daily_loss', 0):,.2f}")
            print(f"   Max Daily Loss: ${risk.get('max_daily_loss', 0):,.2f}")
            print(f"   Open Positions: {risk.get('open_positions', 0)}")
            print(f"   Max Open Positions: {risk.get('max_open_positions', 0)}")
            print(f"   Unrealized P&L: ${risk.get('unrealized_pnl', 0):,.2f}")
            
            print("\n🚦 TRADING STATUS:")
            trading = data.get("trading_allowed", {})
            print(f"   Drawdown OK: {'✅' if trading.get('drawdown_ok') else '❌'}")
            print(f"   Daily Loss OK: {'✅' if trading.get('daily_loss_ok') else '❌'}")
            print(f"   Positions OK: {'✅' if trading.get('positions_ok') else '❌'}")
            print(f"   Overall Trading: {'✅ ALLOWED' if trading.get('overall') else '❌ BLOCKED'}")
            
            # Calculate actual drawdown manually
            balance = account.get('balance', 0)
            equity = account.get('equity', 0)
            unrealized_pnl = risk.get('unrealized_pnl', 0)
            
            print("\n🧮 MANUAL CALCULATION:")
            print(f"   Balance (Peak): ${balance:,.2f}")
            print(f"   Current Equity: ${equity:,.2f}")
            print(f"   Equity + Unrealized: ${equity + unrealized_pnl:,.2f}")
            
            if balance > 0:
                manual_drawdown = ((balance - equity) / balance) * 100
                print(f"   Manual Drawdown: {manual_drawdown:.2f}%")
                
                # Alternative calculation
                current_total = equity + unrealized_pnl
                alt_drawdown = ((balance - current_total) / balance) * 100
                print(f"   Alternative Drawdown: {alt_drawdown:.2f}%")
            
            print("\n" + "=" * 50)
            
            if not trading.get('overall'):
                print("❌ TRADING IS CURRENTLY BLOCKED!")
                print("💡 POSSIBLE SOLUTIONS:")
                
                if not trading.get('drawdown_ok'):
                    print("   1. Close losing positions to reduce drawdown")
                    print("   2. Increase MAX_DRAWDOWN_PERCENT in .env file")
                    print("   3. Wait for market to recover")
                
                if not trading.get('daily_loss_ok'):
                    print("   1. Wait until tomorrow for daily loss reset")
                    print("   2. Increase MAX_DAILY_LOSS in .env file")
                
                if not trading.get('positions_ok'):
                    print("   1. Close some open positions")
                    print("   2. Increase MAX_OPEN_POSITIONS in .env file")
            else:
                print("✅ TRADING IS ALLOWED!")
                
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server")
        print("💡 Make sure backend is running on port 8001")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_account_info():
    """Test account info API"""
    print("\n🏦 CHECKING ACCOUNT INFO")
    print("=" * 30)
    
    try:
        response = requests.get("http://localhost:8001/api/trading/account")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Login: {data.get('login')}")
            print(f"Balance: ${data.get('balance', 0):,.2f}")
            print(f"Equity: ${data.get('equity', 0):,.2f}")
            print(f"Margin: ${data.get('margin', 0):,.2f}")
            print(f"Free Margin: ${data.get('free_margin', 0):,.2f}")
            print(f"Margin Level: {data.get('margin_level', 0):.2f}%")
            print(f"Profit: ${data.get('profit', 0):,.2f}")
            print(f"Currency: {data.get('currency')}")
            print(f"Leverage: 1:{data.get('leverage')}")
            print(f"Server: {data.get('server')}")
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def test_positions():
    """Test positions API"""
    print("\n📊 CHECKING POSITIONS")
    print("=" * 25)
    
    try:
        response = requests.get("http://localhost:8001/api/trading/positions")
        
        if response.status_code == 200:
            positions = response.json()
            print(f"Total Positions: {len(positions)}")
            
            for pos in positions:
                symbol = pos.get('symbol', 'Unknown')
                status = pos.get('status', 'Unknown')
                profit = pos.get('profit', 0)
                entry_price = pos.get('entry_price', 0)
                current_price = pos.get('current_price', 0)
                
                print(f"   {symbol}: {status}, Entry: ${entry_price:.5f}, Current: ${current_price:.5f}, P&L: ${profit:.2f}")
        else:
            print(f"❌ Error: HTTP {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print(f"🕒 Risk Status Check - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    test_risk_status()
    test_account_info()
    test_positions()
