# Auto Trading System API Documentation

## Base URL
```
http://localhost:8000
```

## Authentication
Currently, the API does not require authentication. In production, implement proper authentication mechanisms.

## WebSocket Connection
```
ws://localhost:8000/ws
```

## API Endpoints

### System Control

#### Start Trading Engine
```http
POST /api/trading/start
```

**Request Body:**
```json
{
  "auto_trading_enabled": true
}
```

**Response:**
```json
{
  "message": "Trading engine started successfully",
  "status": "starting"
}
```

#### Stop Trading Engine
```http
POST /api/trading/stop
```

**Response:**
```json
{
  "message": "Trading engine stopped successfully",
  "status": "stopped"
}
```

#### Get System Status
```http
GET /api/trading/status
```

**Response:**
```json
{
  "is_trading_enabled": true,
  "is_mt5_connected": true,
  "is_gpt_available": true,
  "last_analysis_time": "2024-01-01T12:00:00Z",
  "active_positions": 3,
  "daily_profit": 150.50,
  "system_health": "HEALTHY",
  "error_messages": []
}
```

### Positions Management

#### Get All Positions
```http
GET /api/trading/positions
```

**Response:**
```json
[
  {
    "id": "pos_001",
    "ticket": 12345,
    "symbol": "EURUSD",
    "order_type": "BUY",
    "lot_size": 0.1,
    "entry_price": 1.1000,
    "current_price": 1.1050,
    "stop_loss": 1.0950,
    "take_profit": 1.1100,
    "open_time": "2024-01-01T10:00:00Z",
    "close_time": null,
    "status": "OPEN",
    "profit": 50.00,
    "commission": -0.50,
    "swap": 0.00,
    "comment": "GPT Signal"
  }
]
```

#### Close Position
```http
POST /api/trading/positions/close
```

**Request Body:**
```json
{
  "ticket": 12345,
  "reason": "Manual close"
}
```

**Response:**
```json
{
  "message": "Position 12345 closed successfully",
  "success": true
}
```

### Market Data

#### Get Market Data for Symbol
```http
GET /api/trading/market-data/{symbol}
```

**Parameters:**
- `symbol` (path): Trading symbol (e.g., EURUSD, XAUUSD)

**Response:**
```json
{
  "symbol": "EURUSD",
  "timestamp": "2024-01-01T12:00:00Z",
  "bid": 1.1000,
  "ask": 1.1002,
  "spread": 0.0002,
  "volume": 1000
}
```

### Analysis

#### Force Market Analysis
```http
POST /api/trading/analyze
```

**Request Body:**
```json
{
  "symbol": "EURUSD"
}
```

**Response:**
```json
{
  "message": "Analysis completed for EURUSD",
  "success": true
}
```

#### Get Active Signals
```http
GET /api/trading/signals
```

**Response:**
```json
{
  "signals": [
    {
      "id": "signal_001",
      "symbol": "EURUSD",
      "signal_type": "BUY",
      "timestamp": "2024-01-01T12:00:00Z",
      "entry_price": 1.1000,
      "stop_loss": 1.0950,
      "take_profit": 1.1100,
      "lot_size": 0.1,
      "risk_level": "MEDIUM",
      "confidence": 0.75,
      "reasoning": "Strong bullish momentum with RSI oversold recovery"
    }
  ]
}
```

### Account Information

#### Get Account Info
```http
GET /api/trading/account
```

**Response:**
```json
{
  "login": 12345,
  "balance": 10000.00,
  "equity": 10150.00,
  "margin": 100.00,
  "free_margin": 10050.00,
  "margin_level": 10150.00,
  "profit": 150.00,
  "currency": "USD",
  "leverage": 100,
  "server": "Demo-Server"
}
```

### Configuration

#### Get Trading Symbols
```http
GET /api/symbols
```

**Response:**
```json
{
  "symbols": ["XAUUSD", "BTCUSD", "EURUSD", "ETHUSD"],
  "default_symbols": ["XAUUSD", "BTCUSD", "EURUSD", "ETHUSD"]
}
```

#### Get System Configuration
```http
GET /api/config
```

**Response:**
```json
{
  "trading_symbols": ["XAUUSD", "BTCUSD", "EURUSD", "ETHUSD"],
  "analysis_interval_minutes": 5,
  "max_open_positions": 5,
  "default_lot_size": 0.01,
  "stop_loss_pips": 50,
  "take_profit_pips": 100,
  "max_risk_percent": 2.0
}
```

### Health Check

#### API Health Check
```http
GET /api/health
```

**Response:**
```json
{
  "status": "healthy",
  "service": "auto-trading-system",
  "timestamp": "2024-01-01T12:00:00Z"
}
```

## WebSocket Events

### Connection Events

#### Connected
```json
{
  "type": "connected",
  "status": "connected"
}
```

#### Disconnected
```json
{
  "type": "disconnected",
  "status": "disconnected"
}
```

### Real-time Updates

#### System Update
```json
{
  "type": "system_update",
  "timestamp": "2024-01-01T12:00:00Z",
  "data": {
    "system_status": {
      "is_trading_enabled": true,
      "is_mt5_connected": true,
      "is_gpt_available": true,
      "system_health": "HEALTHY"
    },
    "positions_count": 3,
    "account_balance": 10000.00,
    "account_equity": 10150.00,
    "daily_profit": 150.00
  }
}
```

#### Market Update
```json
{
  "type": "market_update",
  "symbol": "EURUSD",
  "marketData": {
    "symbol": "EURUSD",
    "timestamp": "2024-01-01T12:00:00Z",
    "bid": 1.1000,
    "ask": 1.1002,
    "spread": 0.0002
  }
}
```

#### Position Update
```json
{
  "type": "position_update",
  "positions": [
    {
      "ticket": 12345,
      "symbol": "EURUSD",
      "status": "OPEN",
      "profit": 50.00
    }
  ]
}
```

## Error Responses

### Standard Error Format
```json
{
  "detail": "Error message description"
}
```

### Common HTTP Status Codes
- `200` - Success
- `400` - Bad Request
- `404` - Not Found
- `500` - Internal Server Error
- `503` - Service Unavailable

### Error Examples

#### MT5 Not Connected
```json
{
  "detail": "MetaTrader 5 not connected"
}
```

#### Invalid Symbol
```json
{
  "detail": "Symbol INVALID not found"
}
```

#### Trading Engine Not Running
```json
{
  "detail": "Trading engine not initialized"
}
```

## Rate Limiting

Currently, no rate limiting is implemented. In production, consider implementing:
- API rate limiting per IP
- WebSocket connection limits
- Analysis request throttling

## Data Models

### Position Status
- `OPEN` - Position is currently open
- `CLOSED` - Position has been closed
- `PENDING` - Position is pending execution

### Order Types
- `BUY` - Market buy order
- `SELL` - Market sell order
- `BUY_LIMIT` - Buy limit order
- `SELL_LIMIT` - Sell limit order
- `BUY_STOP` - Buy stop order
- `SELL_STOP` - Sell stop order

### Signal Types
- `BUY` - Bullish signal
- `SELL` - Bearish signal
- `HOLD` - No clear direction

### Risk Levels
- `LOW` - Low risk trade
- `MEDIUM` - Medium risk trade
- `HIGH` - High risk trade

### System Health
- `HEALTHY` - All systems operational
- `WARNING` - Some issues detected
- `ERROR` - Critical issues present

## Integration Examples

### Python Client Example
```python
import requests
import websocket
import json

# REST API
response = requests.get('http://localhost:8000/api/trading/status')
status = response.json()
print(f"System health: {status['system_health']}")

# WebSocket
def on_message(ws, message):
    data = json.loads(message)
    print(f"Received: {data}")

ws = websocket.WebSocketApp("ws://localhost:8000/ws",
                          on_message=on_message)
ws.run_forever()
```

### JavaScript Client Example
```javascript
// REST API
fetch('http://localhost:8000/api/trading/status')
  .then(response => response.json())
  .then(data => console.log('System status:', data));

// WebSocket
const ws = new WebSocket('ws://localhost:8000/ws');
ws.onmessage = function(event) {
  const data = JSON.parse(event.data);
  console.log('Received:', data);
};
```
