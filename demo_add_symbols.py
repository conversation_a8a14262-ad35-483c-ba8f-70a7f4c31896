#!/usr/bin/env python3
"""
Demo: How to add trading symbols to your system
"""

print("🔧 === CARA MENAMBAHKAN TRADING PAIRS ===")
print()

print("📊 SYMBOL YANG TERSEDIA DI BROKER ANDA:")
available_symbols = [
    'MBTUSD', 'MBTUSDm', 'USDAED', 'USDAMD', 'USDARS', 'USDAZN', 
    'USDBDT', 'USDBGN', 'USDBHD', 'USDBND', 'USDBRL', 'USDCLP', 
    'USDCNY', 'USDCOP', 'USDDZD', 'USDEGP', 'USDGEL', 'USDGHS', 
    'USDIDR', 'USDINR', 'USDISK', 'USDJOD', 'USDKES', 'USDKGS', 
    'USDKRW', 'USDKWD', 'USDKZT', 'USDLBP', 'USDLKR', 'USDMAD', 
    'USDMYR', 'USDNGN', 'USDNPR', 'USDOMR', 'USDPHP', 'USDPKR', 
    'USDQAR', 'USDRON', 'USDRUB', 'USDRUR', 'USDSAR', 'USDSYP', 
    'USDTJS', 'USDTMT', 'USDTND', 'USDTWD', 'USDUAH', 'USDUGX', 
    'USDUZS', 'USDVND'
]

# Show first 20 symbols
for i, symbol in enumerate(available_symbols[:20], 1):
    print(f"  {i:2d}. {symbol}")

print(f"  ... dan {len(available_symbols)-20} symbol lainnya")

print()
print("❌ SYMBOL POPULER YANG TIDAK TERSEDIA:")
popular_not_available = [
    'XAUUSD (Gold)', 'BTCUSD (Bitcoin)', 'EURUSD (Euro)', 
    'ETHUSD (Ethereum)', 'GBPUSD (Pound)', 'USDJPY (Yen)',
    'US30 (Dow Jones)', 'US500 (S&P 500)', 'USOIL (Oil)'
]

for symbol in popular_not_available:
    print(f"  ❌ {symbol}")

print()
print("💡 REKOMENDASI SYMBOL UNTUK TRADING:")
recommended = [
    'USDAED (USD/UAE Dirham)', 'USDAMD (USD/Armenian Dram)', 
    'USDARS (USD/Argentine Peso)', 'USDBDT (USD/Bangladesh Taka)',
    'USDBRL (USD/Brazilian Real)', 'USDCNY (USD/Chinese Yuan)',
    'USDINR (USD/Indian Rupee)', 'USDKRW (USD/Korean Won)',
    'USDMYR (USD/Malaysian Ringgit)', 'USDPHP (USD/Philippine Peso)'
]

for i, symbol in enumerate(recommended, 1):
    print(f"  {i:2d}. {symbol}")

print()
print("🔧 CARA MENAMBAHKAN SYMBOL:")
print()

print("1️⃣ EDIT FILE .env:")
print("   Ubah baris: TRADING_SYMBOLS=USDAED,USDAMD,USDARS,USDBDT")
print("   Menjadi:    TRADING_SYMBOLS=USDAED,USDAMD,USDARS,USDBDT,USDBRL,USDCNY")
print()

print("2️⃣ EDIT FILE backend/config.py:")
print("   Tambahkan konfigurasi symbol baru di SYMBOL_CONFIG:")
print('''
   "USDBRL": {
       "pip_value": 0.01,
       "min_lot": 0.01,
       "max_lot": 100.0,
       "lot_step": 0.01,
       "spread_threshold": 15.0
   },
   "USDCNY": {
       "pip_value": 0.01,
       "min_lot": 0.01,
       "max_lot": 100.0,
       "lot_step": 0.01,
       "spread_threshold": 10.0
   }
''')

print("3️⃣ RESTART TRADING ENGINE:")
print("   - Stop trading engine di frontend")
print("   - Restart backend (Ctrl+C lalu python -m backend.main)")
print("   - Start trading engine lagi")
print()

print("📋 CONTOH KONFIGURASI LENGKAP:")
print()
print("# File .env")
print("TRADING_SYMBOLS=USDAED,USDAMD,USDARS,USDBDT,USDBRL,USDCNY,USDINR,USDKRW")
print()

print("# File backend/config.py - SYMBOL_CONFIG")
example_config = {
    "USDAED": {"pip_value": 0.0001, "spread_threshold": 5.0},
    "USDAMD": {"pip_value": 0.01, "spread_threshold": 10.0},
    "USDARS": {"pip_value": 0.01, "spread_threshold": 20.0},
    "USDBDT": {"pip_value": 0.01, "spread_threshold": 15.0},
    "USDBRL": {"pip_value": 0.01, "spread_threshold": 15.0},
    "USDCNY": {"pip_value": 0.01, "spread_threshold": 10.0},
    "USDINR": {"pip_value": 0.01, "spread_threshold": 12.0},
    "USDKRW": {"pip_value": 1.0, "spread_threshold": 20.0}
}

for symbol, config in example_config.items():
    print(f'''    "{symbol}": {{
        "pip_value": {config["pip_value"]},
        "min_lot": 0.01,
        "max_lot": 100.0,
        "lot_step": 0.01,
        "spread_threshold": {config["spread_threshold"]}
    }},''')

print()
print("⚠️  PENTING:")
print("   - Hanya gunakan symbol yang tersedia di broker Anda")
print("   - Test dengan lot kecil (0.01) terlebih dahulu")
print("   - Monitor spread dan volatility setiap symbol")
print("   - Exotic pairs memiliki spread lebih besar")
print()

print("🎯 UNTUK MENDAPATKAN SYMBOL POPULER (XAUUSD, BTCUSD, dll):")
print("   - Upgrade akun MT5 Anda ke akun real")
print("   - Atau ganti broker yang menyediakan symbol populer")
print("   - Hubungi support Exness untuk akses symbol tambahan")
print()

print("✅ SETELAH MENAMBAHKAN SYMBOL:")
print("   1. Restart backend")
print("   2. Start trading engine")
print("   3. Monitor log untuk memastikan symbol terdeteksi")
print("   4. Check frontend untuk melihat symbol baru")
print()

print("📞 BANTUAN LEBIH LANJUT:")
print("   - Gunakan script: python check_available_symbols.py")
print("   - Gunakan script: python update_trading_symbols.py")
print("   - Check API: GET /api/trading/symbols/available")
print("   - Check log backend untuk error symbol")
