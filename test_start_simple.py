#!/usr/bin/env python3
"""
Simple test to start trading engine
"""
import requests
import json

def test_start():
    """Test starting trading engine"""
    url = "http://localhost:8001/api/trading/start"
    data = {"auto_trading_enabled": True}
    
    print("🚀 Testing start trading engine...")
    print(f"URL: {url}")
    print(f"Data: {data}")
    
    try:
        response = requests.post(url, json=data, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ SUCCESS!")
            return True
        else:
            print("❌ FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_status():
    """Test getting status"""
    url = "http://localhost:8001/api/trading/status"
    
    print("\n📊 Testing get status...")
    
    try:
        response = requests.get(url, timeout=10)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"MT5 Connected: {data.get('is_mt5_connected')}")
            print(f"GPT Available: {data.get('is_gpt_available')}")
            print(f"System Health: {data.get('system_health')}")
            return True
        else:
            print("❌ FAILED!")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("=== SIMPLE ENGINE TEST ===")
    
    # Test status first
    test_status()
    
    # Test start
    test_start()
    
    # Test status again
    print("\n" + "="*30)
    test_status()
