version: '3.8'

services:
  # Backend API Service
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
    ports:
      - "8001:8001"
    environment:
      - PYTHONPATH=/app
    env_file:
      - .env
    volumes:
      - ./logs:/app/logs
      - ./trading_data:/app/trading_data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - trading-network

  # Frontend Service
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      - VITE_API_URL=http://localhost:8001
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - trading-network

  # Redis for caching (optional)
  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - trading-network

  # PostgreSQL for data storage (optional)
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_DB: trading_db
      POSTGRES_USER: trading_user
      POSTGRES_PASSWORD: trading_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    networks:
      - trading-network

volumes:
  redis_data:
  postgres_data:

networks:
  trading-network:
    driver: bridge
