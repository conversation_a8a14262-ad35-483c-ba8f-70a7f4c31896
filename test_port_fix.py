#!/usr/bin/env python3
"""
Test port fix - verify all components use port 8001
"""
import requests
import time

def test_port_consistency():
    """Test that all components use consistent port 8001"""
    print("🔧 === TESTING PORT CONSISTENCY FIX ===")
    
    # Test 1: Backend health check
    print("\n1. Testing Backend Health (Port 8001)...")
    try:
        response = requests.get("http://localhost:8001/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running on port 8001")
            print(f"   Response: {response.json()}")
        else:
            print(f"❌ Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend connection failed: {e}")
        return False
    
    # Test 2: Trading status
    print("\n2. Testing Trading Status...")
    try:
        response = requests.get("http://localhost:8001/api/trading/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Trading status endpoint working")
            print(f"   MT5 Connected: {data.get('is_mt5_connected')}")
            print(f"   GPT Available: {data.get('is_gpt_available')}")
            print(f"   System Health: {data.get('system_health')}")
        else:
            print(f"❌ Trading status failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Trading status error: {e}")
    
    # Test 3: Start engine (the main fix)
    print("\n3. Testing Start Engine (Main Fix)...")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        print(f"   Status Code: {response.status_code}")
        print(f"   Response: {response.text}")
        
        if response.status_code == 200:
            print("✅ Start engine request successful!")
            print("✅ No more 'Network Error' or '422 Unprocessable Entity'")
            return True
        elif response.status_code == 422:
            print("❌ Still getting 422 error - request body issue")
            return False
        else:
            print(f"❌ Unexpected status code: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ Start engine error: {e}")
        return False

def test_frontend_connection():
    """Test that frontend can reach backend"""
    print("\n4. Testing Frontend Connection...")
    try:
        # This simulates what frontend does
        response = requests.get("http://localhost:8001/api/trading/symbols/active", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print("✅ Frontend can reach backend API")
            print(f"   Active symbols: {data.get('active_symbols', [])}")
            return True
        else:
            print(f"❌ Frontend connection failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend connection error: {e}")
        return False

if __name__ == "__main__":
    print("🎯 Testing Port Fix - All components should use port 8001")
    print("=" * 60)
    
    success = True
    
    # Run tests
    if not test_port_consistency():
        success = False
    
    if not test_frontend_connection():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 PORT FIX SUCCESSFUL!")
        print("✅ All components now use port 8001")
        print("✅ No more network errors")
        print("✅ Frontend can communicate with backend")
        print("\n📝 Summary of fixes:")
        print("   - Frontend API service: 8000 → 8001")
        print("   - Docker compose: 8000 → 8001") 
        print("   - All test scripts: 8000 → 8001")
        print("   - Documentation: 8000 → 8001")
    else:
        print("❌ PORT FIX INCOMPLETE")
        print("   Some components still have port issues")
    
    print(f"\n🌐 URLs:")
    print(f"   Frontend: http://localhost:3000")
    print(f"   Backend:  http://localhost:8001")
    print(f"   API Docs: http://localhost:8001/docs")
