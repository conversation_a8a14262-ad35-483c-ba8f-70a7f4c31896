#!/usr/bin/env python3
"""
Simple script to test trading API endpoints
"""
import requests
import json

BASE_URL = "http://localhost:8001"

def test_start_engine():
    """Test starting trading engine"""
    url = f"{BASE_URL}/api/trading/start"
    data = {"auto_trading_enabled": True}

    try:
        response = requests.post(url, json=data)
        print(f"Start Engine - Status: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error starting engine: {e}")
        return False

def test_get_status():
    """Test getting system status"""
    url = f"{BASE_URL}/api/trading/status"

    try:
        response = requests.get(url)
        print(f"Get Status - Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"MT5 Connected: {data.get('is_mt5_connected')}")
            print(f"GPT Available: {data.get('is_gpt_available')}")
            print(f"Trading Enabled: {data.get('is_trading_enabled')}")
        else:
            print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error getting status: {e}")
        return False

def test_health():
    """Test health endpoint"""
    url = f"{BASE_URL}/api/health"

    try:
        response = requests.get(url)
        print(f"Health Check - Status: {response.status_code}")
        print(f"Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Error checking health: {e}")
        return False

if __name__ == "__main__":
    print("=== Testing Trading API ===")

    print("\n1. Testing Health Check...")
    test_health()

    print("\n2. Testing Get Status...")
    test_get_status()

    print("\n3. Testing Start Engine...")
    test_start_engine()

    print("\n4. Testing Get Status After Start...")
    test_get_status()
