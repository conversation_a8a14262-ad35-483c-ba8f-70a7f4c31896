# Auto Trading System

Sistem trading otomatis yang menggunakan GPT-4 untuk analisis pasar dan MetaTrader 5 untuk eksekusi trading. Sistem ini menyediakan analisis teknikal real-time, manajemen risiko otomatis, dan antarmuka web untuk monitoring.

## 🚀 Fitur Utama

### Backend (API GPT & MetaTrader Integration)
- **Analisis GPT-4**: Analisis pasar real-time menggunakan OpenAI GPT-4
- **Integrasi MetaTrader 5**: Koneksi langsung dengan MT5 untuk eksekusi trading
- **Analisis Teknikal**: Indikator teknikal otomatis (RSI, MACD, Bollinger Bands, dll)
- **Manajemen Risiko**: Sistem manajemen risiko otomatis dengan stop loss dan take profit
- **WebSocket Real-time**: Update data pasar dan posisi secara real-time

### Frontend (UI Platform)
- **Dashboard Real-time**: Monitoring sistem dan performa trading
- **Panel Trading**: Kontrol manual dan analisis paksa
- **Data Pasar**: Visualisasi harga real-time untuk semua instrumen
- **Manajemen Posisi**: Monitor dan kelola posisi trading

### Eksekusi Market (MetaTrader 5)
- **Auto Execution**: Eksekusi otomatis berdasarkan sinyal GPT
- **Risk Management**: Stop loss, take profit, dan trailing stop otomatis
- **Multi-Symbol**: Support untuk XAUUSD, BTCUSD, EURUSD, ETHUSD
- **Position Monitoring**: Monitoring posisi real-time

## 📋 Persyaratan Sistem

### Software Requirements
- Python 3.8+
- Node.js 16+
- MetaTrader 5 Terminal
- OpenAI API Key

### Hardware Requirements
- RAM: Minimum 4GB, Recommended 8GB+
- Storage: 2GB free space
- Internet: Stable connection untuk API calls

## 🛠️ Instalasi

### 1. Clone Repository
```bash
git clone <repository-url>
cd trdAi
```

### 2. Setup Backend

#### Install Python Dependencies
```bash
pip install -r requirements.txt
```

#### Configure Environment Variables
```bash
cp .env.example .env
```

Edit file `.env` dengan konfigurasi Anda:
```env
# OpenAI API Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-4-turbo-preview

# MetaTrader 5 Configuration
MT5_LOGIN=your_mt5_login
MT5_PASSWORD=your_mt5_password
MT5_SERVER=your_mt5_server

# Trading Configuration
DEFAULT_LOT_SIZE=0.01
MAX_RISK_PERCENT=2.0
STOP_LOSS_PIPS=50
TAKE_PROFIT_PIPS=100

# Trading Symbols
TRADING_SYMBOLS=XAUUSD,BTCUSD,EURUSD,ETHUSD
```

### 3. Setup Frontend

#### Install Node.js Dependencies
```bash
cd frontend
npm install
```

### 4. Setup MetaTrader 5

1. Install MetaTrader 5 Terminal
2. Login ke akun trading Anda
3. Pastikan Python API diaktifkan:
   - Tools → Options → Expert Advisors
   - Centang "Allow automated trading"
   - Centang "Allow DLL imports"

## 🚀 Menjalankan Sistem

### 1. Start Backend Server
```bash
# Dari root directory
python -m backend.main
```
Server akan berjalan di `http://localhost:8001`

### 2. Start Frontend Development Server
```bash
# Dari directory frontend
npm run dev
```
Frontend akan berjalan di `http://localhost:3000`

### 3. Akses Web Interface
Buka browser dan akses `http://localhost:3000`

## 📊 Penggunaan

### 1. Dashboard
- Monitor status sistem (MT5, GPT, kesehatan sistem)
- Lihat statistik akun (balance, equity, P&L harian)
- Kontrol trading engine (start/stop)
- Monitor posisi terbuka

### 2. Trading Panel
- Pilih instrumen untuk analisis
- Jalankan analisis GPT paksa
- Lihat sinyal trading aktif
- Monitor confidence level dan reasoning

### 3. Market Data
- Lihat harga bid/ask real-time
- Monitor spread untuk semua instrumen
- Auto-refresh data pasar
- Tabel ringkasan pasar

### 4. Position Manager
- Lihat semua posisi (terbuka/tertutup)
- Tutup posisi secara manual
- Monitor P&L real-time
- Filter posisi berdasarkan status

## ⚙️ Konfigurasi Trading

### Risk Management
```python
# Di file backend/config.py
MAX_RISK_PERCENT = 2.0          # Maksimal 2% risiko per trade
MAX_OPEN_POSITIONS = 5          # Maksimal 5 posisi terbuka
MAX_DAILY_LOSS = 1000          # Maksimal loss $1000 per hari
STOP_LOSS_PIPS = 50            # Stop loss 50 pips
TAKE_PROFIT_PIPS = 100         # Take profit 100 pips
```

### Symbol Configuration
```python
# Konfigurasi per instrumen
SYMBOL_CONFIG = {
    "XAUUSD": {
        "pip_value": 0.01,
        "min_lot": 0.01,
        "max_lot": 100.0,
        "spread_threshold": 5.0
    },
    # ... konfigurasi lainnya
}
```

## 🔧 API Endpoints

### Trading Control
- `POST /api/trading/start` - Start trading engine
- `POST /api/trading/stop` - Stop trading engine
- `GET /api/trading/status` - Get system status

### Positions
- `GET /api/trading/positions` - Get all positions
- `POST /api/trading/positions/close` - Close specific position

### Market Data
- `GET /api/trading/market-data/{symbol}` - Get market data for symbol
- `POST /api/trading/analyze` - Force analysis for symbol

### Account
- `GET /api/trading/account` - Get account information

## 🔒 Keamanan

### API Security
- Environment variables untuk sensitive data
- Input validation pada semua endpoints
- Error handling yang aman

### Trading Security
- Risk management otomatis
- Position size calculation
- Maximum drawdown protection
- Daily loss limits

## 📝 Logging

Sistem menggunakan structured logging dengan Loguru:
- File log: `trading.log`
- Level: INFO, WARNING, ERROR
- Rotation: Daily dengan retention 30 hari

## 🐛 Troubleshooting

### Common Issues

#### 1. MT5 Connection Failed
```
Error: MT5 initialization failed
```
**Solusi:**
- Pastikan MT5 Terminal terbuka dan login
- Periksa kredensial di file `.env`
- Pastikan automated trading diaktifkan

#### 2. GPT API Error
```
Error: OpenAI API request failed
```
**Solusi:**
- Periksa API key di file `.env`
- Pastikan ada credit di akun OpenAI
- Periksa koneksi internet

#### 3. Frontend Connection Error
```
Error: Cannot connect to backend
```
**Solusi:**
- Pastikan backend server berjalan di port 8000
- Periksa CORS configuration
- Restart kedua server

### Debug Mode
Untuk debugging, set environment variable:
```bash
LOG_LEVEL=DEBUG
```

## 🤝 Kontribusi

1. Fork repository
2. Buat feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push ke branch (`git push origin feature/AmazingFeature`)
5. Buat Pull Request

## 📄 Lisensi

Distributed under the MIT License. See `LICENSE` for more information.

## ⚠️ Disclaimer

**PERINGATAN PENTING:**
- Sistem ini untuk tujuan edukasi dan penelitian
- Trading melibatkan risiko kehilangan modal
- Selalu gunakan akun demo terlebih dahulu
- Tidak ada jaminan profit dalam trading
- Gunakan dengan risiko sendiri

## 📞 Support

Untuk pertanyaan dan support:
- Email: <EMAIL>
- Documentation: [Wiki](link-to-wiki)
- Issues: [GitHub Issues](link-to-issues)

---

**Happy Trading! 🚀📈**
