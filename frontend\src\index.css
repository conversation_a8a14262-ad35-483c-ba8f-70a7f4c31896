@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }
  
  body {
    @apply bg-gray-50 text-gray-900;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-lg font-medium transition-colors duration-200;
  }
  
  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700;
  }
  
  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700;
  }
  
  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700;
  }
  
  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6;
  }
  
  .status-indicator {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .status-healthy {
    @apply bg-success-50 text-success-700;
  }
  
  .status-warning {
    @apply bg-warning-50 text-warning-700;
  }
  
  .status-error {
    @apply bg-danger-50 text-danger-700;
  }
}
