#!/usr/bin/env python3
"""
Test order placement with forced signals
"""
import requests
import time

def test_order_placement():
    """Test order placement functionality"""
    print("🔧 === TESTING ORDER PLACEMENT ===")

    # Test 1: Start trading engine
    print("\n1. Starting trading engine...")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Trading engine started successfully")
        else:
            print(f"❌ Failed to start engine: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error starting engine: {e}")
        return False

    # Test 2: Check account info
    print("\n2. Checking account info...")
    try:
        response = requests.get("http://localhost:8001/api/trading/account", timeout=5)
        if response.status_code == 200:
            data = response.json()
            balance = data.get('balance', 0)
            free_margin = data.get('free_margin', 0)
            print(f"✅ Account info retrieved")
            print(f"   Balance: ${balance:.2f}")
            print(f"   Free Margin: ${free_margin:.2f}")
        else:
            print(f"⚠️  Account info: {response.status_code}")
    except Exception as e:
        print(f"❌ Account info error: {e}")

    # Test 3: Check current positions
    print("\n3. Checking current positions...")
    try:
        response = requests.get("http://localhost:8001/api/trading/positions", timeout=5)
        if response.status_code == 200:
            positions = response.json()
            print(f"✅ Current positions: {len(positions)}")
            for pos in positions:
                print(f"   {pos.get('symbol')} - {pos.get('order_type')} - {pos.get('lot_size')} lots")
        else:
            print(f"❌ Positions check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Positions error: {e}")

    # Test 4: Force analysis for specific symbol
    print("\n4. Testing forced analysis...")
    try:
        data = {"symbol": "BTCUSDm"}
        response = requests.post("http://localhost:8001/api/trading/analyze", json=data, timeout=15)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Forced analysis completed")
            print(f"   Response: {response.json()}")
        else:
            print(f"⚠️  Forced analysis: {response.text}")
    except Exception as e:
        print(f"❌ Forced analysis error: {e}")

    # Test 5: Monitor for order placement attempts
    print("\n5. Monitoring for 30 seconds...")
    print("   Watch backend logs for:")
    print("   - Order details logging")
    print("   - Order request parameters")
    print("   - MT5 error codes if any")
    print("   - Successful order placement")

    time.sleep(30)

    return True

def analyze_order_failure():
    """Analyze why orders might be failing"""
    print("\n📚 === ORDER FAILURE ANALYSIS ===")

    print("🔍 Common reasons for MT5 order_send() returning None:")
    print("1. Invalid symbol or symbol not tradeable")
    print("2. Market is closed")
    print("3. Invalid lot size (too small/large or wrong step)")
    print("4. Invalid price (too far from market)")
    print("5. Insufficient margin")
    print("6. Invalid order type for symbol")
    print("7. Invalid filling type")
    print("8. MT5 connection issues")

    print("\n💡 Solutions implemented:")
    print("✅ Symbol validation and selection")
    print("✅ Lot size validation and rounding")
    print("✅ Improved margin calculation")
    print("✅ Better error logging with MT5 last_error()")
    print("✅ Fallback filling types")
    print("✅ Detailed order request logging")

    print("\n🎯 Next steps if still failing:")
    print("1. Check if market is open for trading")
    print("2. Verify symbol trading permissions")
    print("3. Test with smaller lot sizes")
    print("4. Check broker-specific requirements")

if __name__ == "__main__":
    print("🎯 Testing Order Placement")
    print("=" * 50)

    success = test_order_placement()
    analyze_order_failure()

    print("\n" + "=" * 50)
    if success:
        print("🎉 ORDER PLACEMENT TEST COMPLETED!")
        print("\n📝 Improvements Made:")
        print("✅ Fixed spread calculation (pip values)")
        print("✅ Improved margin calculation")
        print("✅ Better error handling")
        print("✅ Detailed logging")
        print("✅ Symbol validation")
        print("✅ Lot size validation")
        print("✅ Filling type fallbacks")

        print("\n🔍 Check backend logs for:")
        print("   - Order details: lot_size, price, type")
        print("   - Order request parameters")
        print("   - MT5 error codes")
        print("   - Success/failure messages")
    else:
        print("❌ TESTING INCOMPLETE")

    print(f"\n🌐 Monitor backend logs for detailed order placement information")
