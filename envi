# OpenAI API Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
OPENAI_MODEL=gpt-4.1

# MetaTrader 5 Configuration
MT5_LOGIN=186982659
MT5_PASSWORD=J@mzmail641@
MT5_SERVER=Exness-MT5Real26

# Trading Configuration
DEFAULT_LOT_SIZE=0.01
MAX_RISK_PERCENT=2.0
STOP_LOSS_PIPS=50
TAKE_PROFIT_PIPS=100
TRAILING_STOP_PIPS=30

# Symbols to Trade
TRADING_SYMBOLS=XAUUSD,BTCUSD,EURUSD,ETHUSD

# API Configuration
API_HOST=0.0.0.0
API_PORT=8002
API_RELOAD=true

# Database Configuration (Optional)
DATABASE_URL=sqlite:///./trading.db

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=trading.log

# Risk Management
MAX_OPEN_POSITIONS=5
MAX_DAILY_LOSS=1000
MAX_DRAWDOWN_PERCENT=25

# GPT Analysis Configuration
ANALYSIS_INTERVAL_MINUTES=5
MARKET_DATA_LOOKBACK_HOURS=24

# Security
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
