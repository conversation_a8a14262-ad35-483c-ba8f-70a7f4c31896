"""
ORDER MANAGER
- <PERSON>sang pending order jika GPT output valid
- <PERSON>ung buffer trigger (default: 0.5 × ATR)
- Pasang notifikasi jika harga mendekati buffer
- Simpan order + trigger zone di logs/pending_orders.json
"""

import json
import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
import MetaTrader5 as mt5
from backend.services.data_fetcher import DataFetcher

logger = logging.getLogger(__name__)

class OrderManager:
    """
    Manage pending orders for scalping system
    """
    
    def __init__(self):
        self.data_fetcher = DataFetcher()
        self.pending_orders = {}  # order_id -> order_data
        self.buffer_zones = {}    # order_id -> buffer_data
        
        # Setup logging directories
        self.logs_dir = Path("logs")
        self.logs_dir.mkdir(exist_ok=True)
        
        self.pending_orders_file = self.logs_dir / "pending_orders.json"
        self.expired_orders_file = self.logs_dir / "expired_orders.json"
        
        # Load existing orders
        self._load_pending_orders()
        
        # Buffer settings
        self.default_buffer_multiplier = 0.5  # 0.5 × ATR
        
    async def place_pending_order(
        self,
        gpt_analysis: Dict[str, Any],
        scalping_data: Dict[str, Any]
    ) -> Optional[str]:
        """
        Place pending order based on GPT analysis
        Returns order_id if successful
        """
        try:
            analysis_type = gpt_analysis.get('analisis', '')
            
            if analysis_type == "TIDAK ADA ENTRY VALID":
                logger.info("⏸️ No valid entry - skipping order placement")
                return None
            
            symbol = gpt_analysis.get('symbol', scalping_data.get('symbol', 'UNKNOWN'))
            entry_price = gpt_analysis.get('entry_price', 0)
            stop_loss = gpt_analysis.get('stop_loss', 0)
            take_profit = gpt_analysis.get('take_profit', 0)
            
            if not all([entry_price, stop_loss, take_profit]):
                logger.error("❌ Missing required price levels for order")
                return None
            
            # Calculate buffer zone
            atr = scalping_data.get('atr', 0)
            buffer_distance = atr * self.default_buffer_multiplier if atr > 0 else 0
            
            # Determine order type
            order_type = self._determine_order_type(analysis_type)
            if not order_type:
                logger.error(f"❌ Unknown analysis type: {analysis_type}")
                return None
            
            # Create order data
            order_id = f"{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            
            order_data = {
                "order_id": order_id,
                "symbol": symbol,
                "analysis_type": analysis_type,
                "order_type": order_type,
                "entry_price": entry_price,
                "stop_loss": stop_loss,
                "take_profit": take_profit,
                "buffer_distance": buffer_distance,
                "buffer_zone": {
                    "upper": entry_price + buffer_distance,
                    "lower": entry_price - buffer_distance
                },
                "gpt_analysis": gpt_analysis,
                "scalping_data": {
                    "rsi": scalping_data.get('rsi'),
                    "atr": scalping_data.get('atr'),
                    "volume_metrics": scalping_data.get('volume_metrics')
                },
                "created_at": datetime.now().isoformat(),
                "valid_until": gpt_analysis.get('valid_until', ''),
                "status": "PENDING",
                "mt5_ticket": None
            }
            
            # Place order in MT5
            mt5_ticket = await self._place_mt5_order(order_data)
            
            if mt5_ticket:
                order_data["mt5_ticket"] = mt5_ticket
                order_data["status"] = "ACTIVE"
                
                # Store order
                self.pending_orders[order_id] = order_data
                self.buffer_zones[order_id] = order_data["buffer_zone"]
                
                # Save to file
                self._save_pending_orders()
                
                logger.info(f"✅ Pending order placed: {order_id}")
                logger.info(f"   📊 Type: {analysis_type}")
                logger.info(f"   💰 Entry: ${entry_price}")
                logger.info(f"   🛡️ Stop: ${stop_loss}")
                logger.info(f"   🎯 Target: ${take_profit}")
                logger.info(f"   📏 Buffer: ±${buffer_distance:.5f}")
                logger.info(f"   🎫 MT5 Ticket: {mt5_ticket}")
                
                return order_id
            else:
                logger.error(f"❌ Failed to place MT5 order for {symbol}")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error placing pending order: {e}")
            return None
    
    def _determine_order_type(self, analysis_type: str) -> Optional[str]:
        """Determine MT5 order type from GPT analysis"""
        order_type_map = {
            "BUY STOP": "ORDER_TYPE_BUY_STOP",
            "BUY LIMIT": "ORDER_TYPE_BUY_LIMIT", 
            "SELL STOP": "ORDER_TYPE_SELL_STOP",
            "SELL LIMIT": "ORDER_TYPE_SELL_LIMIT"
        }
        return order_type_map.get(analysis_type)
    
    async def _place_mt5_order(self, order_data: Dict[str, Any]) -> Optional[int]:
        """Place pending order in MT5"""
        try:
            # Connect to MT5 if needed
            if not await self.data_fetcher.connect_mt5():
                return None
            
            symbol = order_data["symbol"]
            order_type_str = order_data["order_type"]
            entry_price = order_data["entry_price"]
            stop_loss = order_data["stop_loss"]
            take_profit = order_data["take_profit"]
            
            # Convert order type string to MT5 constant
            order_type_map = {
                "ORDER_TYPE_BUY_STOP": mt5.ORDER_TYPE_BUY_STOP,
                "ORDER_TYPE_BUY_LIMIT": mt5.ORDER_TYPE_BUY_LIMIT,
                "ORDER_TYPE_SELL_STOP": mt5.ORDER_TYPE_SELL_STOP,
                "ORDER_TYPE_SELL_LIMIT": mt5.ORDER_TYPE_SELL_LIMIT
            }
            
            mt5_order_type = order_type_map.get(order_type_str)
            if mt5_order_type is None:
                logger.error(f"❌ Invalid order type: {order_type_str}")
                return None
            
            # Calculate lot size (default small for scalping)
            lot_size = 0.01  # Small lot for scalping
            
            # Create order request
            request = {
                "action": mt5.TRADE_ACTION_PENDING,
                "symbol": symbol,
                "volume": lot_size,
                "type": mt5_order_type,
                "price": entry_price,
                "sl": stop_loss,
                "tp": take_profit,
                "deviation": 20,
                "magic": 234000,  # Magic number for scalping orders
                "comment": f"Scalping_{order_data['order_id'][:8]}",
                "type_time": mt5.ORDER_TIME_SPECIFIED,
                "expiration": int((datetime.now() + timedelta(hours=2)).timestamp())
            }
            
            # Send order
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"❌ MT5 order failed: {result.retcode} - {result.comment}")
                return None
            
            logger.info(f"✅ MT5 order placed: Ticket {result.order}")
            return result.order
            
        except Exception as e:
            logger.error(f"❌ Error placing MT5 order: {e}")
            return None
    
    async def check_buffer_zones(self) -> List[str]:
        """
        Check if current prices are approaching buffer zones
        Returns list of order_ids that need re-evaluation
        """
        orders_to_reevaluate = []
        
        try:
            for order_id, order_data in self.pending_orders.items():
                if order_data["status"] != "ACTIVE":
                    continue
                
                symbol = order_data["symbol"]
                buffer_zone = order_data["buffer_zone"]
                
                # Get current price
                current_price = await self.data_fetcher.get_current_price(symbol)
                if not current_price:
                    continue
                
                # Check if price is in buffer zone
                in_buffer = (buffer_zone["lower"] <= current_price <= buffer_zone["upper"])
                
                if in_buffer:
                    logger.info(f"🚨 Price approaching buffer zone for {order_id}")
                    logger.info(f"   💰 Current: ${current_price:.5f}")
                    logger.info(f"   📏 Buffer: ${buffer_zone['lower']:.5f} - ${buffer_zone['upper']:.5f}")
                    
                    orders_to_reevaluate.append(order_id)
            
            return orders_to_reevaluate
            
        except Exception as e:
            logger.error(f"❌ Error checking buffer zones: {e}")
            return []
    
    async def cancel_order(self, order_id: str, reason: str = "Manual cancel") -> bool:
        """Cancel pending order"""
        try:
            if order_id not in self.pending_orders:
                logger.warning(f"⚠️ Order {order_id} not found")
                return False
            
            order_data = self.pending_orders[order_id]
            mt5_ticket = order_data.get("mt5_ticket")
            
            # Cancel in MT5 if ticket exists
            if mt5_ticket:
                success = await self._cancel_mt5_order(mt5_ticket)
                if not success:
                    logger.error(f"❌ Failed to cancel MT5 order {mt5_ticket}")
                    return False
            
            # Update order status
            order_data["status"] = "CANCELLED"
            order_data["cancelled_at"] = datetime.now().isoformat()
            order_data["cancel_reason"] = reason
            
            # Move to expired orders
            self._move_to_expired(order_id, order_data)
            
            # Remove from active orders
            del self.pending_orders[order_id]
            if order_id in self.buffer_zones:
                del self.buffer_zones[order_id]
            
            # Save changes
            self._save_pending_orders()
            
            logger.info(f"✅ Order cancelled: {order_id} - {reason}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error cancelling order {order_id}: {e}")
            return False
    
    async def _cancel_mt5_order(self, ticket: int) -> bool:
        """Cancel order in MT5"""
        try:
            request = {
                "action": mt5.TRADE_ACTION_REMOVE,
                "order": ticket
            }
            
            result = mt5.order_send(request)
            
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"❌ MT5 cancel failed: {result.retcode} - {result.comment}")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"❌ Error cancelling MT5 order {ticket}: {e}")
            return False
    
    def _load_pending_orders(self):
        """Load pending orders from file"""
        try:
            if self.pending_orders_file.exists():
                with open(self.pending_orders_file, 'r') as f:
                    data = json.load(f)
                    self.pending_orders = data.get('orders', {})
                    self.buffer_zones = data.get('buffer_zones', {})
                logger.info(f"📂 Loaded {len(self.pending_orders)} pending orders")
            else:
                logger.info("📂 No existing pending orders file")
        except Exception as e:
            logger.error(f"❌ Error loading pending orders: {e}")
    
    def _save_pending_orders(self):
        """Save pending orders to file"""
        try:
            data = {
                "orders": self.pending_orders,
                "buffer_zones": self.buffer_zones,
                "last_updated": datetime.now().isoformat()
            }
            
            with open(self.pending_orders_file, 'w') as f:
                json.dump(data, f, indent=2)
                
        except Exception as e:
            logger.error(f"❌ Error saving pending orders: {e}")
    
    def _move_to_expired(self, order_id: str, order_data: Dict[str, Any]):
        """Move order to expired orders log"""
        try:
            expired_orders = []
            
            # Load existing expired orders
            if self.expired_orders_file.exists():
                with open(self.expired_orders_file, 'r') as f:
                    expired_orders = json.load(f)
            
            # Add current order
            expired_orders.append(order_data)
            
            # Save updated expired orders
            with open(self.expired_orders_file, 'w') as f:
                json.dump(expired_orders, f, indent=2)
                
        except Exception as e:
            logger.error(f"❌ Error moving order to expired: {e}")
    
    def get_active_orders(self) -> Dict[str, Any]:
        """Get all active pending orders"""
        active_orders = {
            order_id: order_data 
            for order_id, order_data in self.pending_orders.items()
            if order_data["status"] == "ACTIVE"
        }
        
        return {
            "active_orders": active_orders,
            "count": len(active_orders),
            "timestamp": datetime.now().isoformat()
        }
    
    async def cleanup_expired_orders(self):
        """Clean up expired orders (older than 2 hours)"""
        try:
            current_time = datetime.now()
            expired_order_ids = []
            
            for order_id, order_data in self.pending_orders.items():
                if order_data["status"] != "ACTIVE":
                    continue
                
                # Check if order is expired (2 hours old)
                created_at = datetime.fromisoformat(order_data["created_at"])
                age_hours = (current_time - created_at).total_seconds() / 3600
                
                if age_hours >= 2:
                    expired_order_ids.append(order_id)
            
            # Cancel expired orders
            for order_id in expired_order_ids:
                await self.cancel_order(order_id, "Expired (2 hours)")
            
            if expired_order_ids:
                logger.info(f"🧹 Cleaned up {len(expired_order_ids)} expired orders")
            
        except Exception as e:
            logger.error(f"❌ Error cleaning up expired orders: {e}")
