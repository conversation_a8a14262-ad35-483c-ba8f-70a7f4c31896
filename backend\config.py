"""
Configuration module for the Auto Trading System
"""
import os
from typing import List
from pydantic import Field
from pydantic_settings import BaseSettings
from dotenv import load_dotenv

load_dotenv()


class Settings(BaseSettings):
    """Application settings"""

    # OpenAI Configuration
    openai_api_key: str = Field(..., env="OPENAI_API_KEY")
    openai_model: str = Field(default="gpt-4o-mini", env="OPENAI_MODEL")

    # MetaTrader 5 Configuration
    mt5_login: int = Field(..., env="MT5_LOGIN")
    mt5_password: str = Field(..., env="MT5_PASSWORD")
    mt5_server: str = Field(..., env="MT5_SERVER")

    # Trading Configuration
    default_lot_size: float = Field(default=0.01, env="DEFAULT_LOT_SIZE")
    max_risk_percent: float = Field(default=2.0, env="MAX_RISK_PERCENT")
    stop_loss_pips: int = Field(default=50, env="STOP_LOSS_PIPS")
    take_profit_pips: int = Field(default=100, env="TAKE_PROFIT_PIPS")
    trailing_stop_pips: int = Field(default=30, env="TRAILING_STOP_PIPS")

    # Trading Symbols
    trading_symbols: str = Field(default="XAUUSDm,BTCUSDm,EURUSDm,ETHUSDm", env="TRADING_SYMBOLS")
    active_symbols: str = Field(default="", env="ACTIVE_SYMBOLS")  # User-selected active symbols

    @property
    def symbols_list(self) -> List[str]:
        """Get all available trading symbols"""
        return [symbol.strip() for symbol in self.trading_symbols.split(",")]

    @property
    def active_symbols_list(self) -> List[str]:
        """Get user-selected active symbols for analysis"""
        if not self.active_symbols.strip():
            # If no active symbols selected, return first 4 as default
            return self.symbols_list[:4]
        return [symbol.strip() for symbol in self.active_symbols.split(",")]

    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    api_reload: bool = Field(default=True, env="API_RELOAD")

    # Database Configuration
    database_url: str = Field(default="sqlite:///./trading.db", env="DATABASE_URL")

    # Logging Configuration
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    log_file: str = Field(default="trading.log", env="LOG_FILE")

    # Risk Management
    max_open_positions: int = Field(default=5, env="MAX_OPEN_POSITIONS")
    max_daily_loss: float = Field(default=1000, env="MAX_DAILY_LOSS")
    max_drawdown_percent: float = Field(default=10, env="MAX_DRAWDOWN_PERCENT")

    # GPT Analysis Configuration
    analysis_interval_minutes: int = Field(default=5, env="ANALYSIS_INTERVAL_MINUTES")
    market_data_lookback_hours: int = Field(default=24, env="MARKET_DATA_LOOKBACK_HOURS")

    # Security
    secret_key: str = Field(..., env="SECRET_KEY")
    algorithm: str = Field(default="HS256", env="ALGORITHM")
    access_token_expire_minutes: int = Field(default=30, env="ACCESS_TOKEN_EXPIRE_MINUTES")

    class Config:
        env_file = ".env"
        case_sensitive = False


# Global settings instance
settings = Settings()


# Trading Constants
class TradingConstants:
    """Trading related constants"""

    # Order Types
    ORDER_BUY = "BUY"
    ORDER_SELL = "SELL"
    ORDER_BUY_LIMIT = "BUY_LIMIT"
    ORDER_SELL_LIMIT = "SELL_LIMIT"
    ORDER_BUY_STOP = "BUY_STOP"
    ORDER_SELL_STOP = "SELL_STOP"

    # Position Status
    POSITION_OPEN = "OPEN"
    POSITION_CLOSED = "CLOSED"
    POSITION_PENDING = "PENDING"

    # Signal Types
    SIGNAL_BUY = "BUY"
    SIGNAL_SELL = "SELL"
    SIGNAL_HOLD = "HOLD"

    # Risk Levels
    RISK_LOW = "LOW"
    RISK_MEDIUM = "MEDIUM"
    RISK_HIGH = "HIGH"

    # Timeframes
    TIMEFRAME_M1 = "M1"
    TIMEFRAME_M5 = "M5"
    TIMEFRAME_M15 = "M15"
    TIMEFRAME_M30 = "M30"
    TIMEFRAME_H1 = "H1"
    TIMEFRAME_H4 = "H4"
    TIMEFRAME_D1 = "D1"


# Symbol Configuration
SYMBOL_CONFIG = {
    "XAUUSDm": {
        "pip_value": 0.01,
        "min_lot": 0.01,
        "max_lot": 100.0,
        "lot_step": 0.01,
        "spread_threshold": 100.0
    },
    "BTCUSDm": {
        "pip_value": 1.0,  # Bitcoin pip value is 1.0, not 0.01
        "min_lot": 0.01,
        "max_lot": 100.0,
        "lot_step": 0.01,
        "spread_threshold": 50.0  # Reasonable for crypto
    },
    "ETHUSDm": {
        "pip_value": 0.1,  # Ethereum pip value is 0.1
        "min_lot": 0.1,
        "max_lot": 100.0,
        "lot_step": 0.1,
        "spread_threshold": 30.0  # Reasonable for crypto
    },
    "EURUSDm": {
        "pip_value": 0.01,
        "min_lot": 0.01,
        "max_lot": 100.0,
        "lot_step": 0.01,
        "spread_threshold": 100.0
    }
}
