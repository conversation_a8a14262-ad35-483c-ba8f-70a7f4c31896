#!/usr/bin/env python3
"""
Test the fixes for spread calculation and margin issues
"""
import requests
import time

def test_fixes():
    """Test all the fixes"""
    print("🔧 === TESTING ALL FIXES ===")
    
    # Test 1: Start trading engine
    print("\n1. Testing trading engine start...")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Trading engine started successfully")
        else:
            print(f"❌ Failed to start engine: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error starting engine: {e}")
        return False
    
    # Test 2: Check system status
    print("\n2. Checking system status...")
    try:
        response = requests.get("http://localhost:8001/api/trading/status", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ System status retrieved")
            print(f"   MT5 Connected: {data.get('is_mt5_connected')}")
            print(f"   GPT Available: {data.get('is_gpt_available')}")
            print(f"   Trading Enabled: {data.get('is_trading_enabled')}")
            print(f"   System Health: {data.get('system_health')}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Status check error: {e}")
    
    # Test 3: Check account info
    print("\n3. Checking account info...")
    try:
        response = requests.get("http://localhost:8001/api/trading/account", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Account info retrieved")
            print(f"   Balance: ${data.get('balance', 0):.2f}")
            print(f"   Equity: ${data.get('equity', 0):.2f}")
            print(f"   Free Margin: ${data.get('free_margin', 0):.2f}")
        else:
            print(f"⚠️  Account info: {response.status_code} - {response.text}")
    except Exception as e:
        print(f"❌ Account info error: {e}")
    
    # Test 4: Check market data for BTCUSDm
    print("\n4. Checking BTCUSDm market data...")
    try:
        response = requests.get("http://localhost:8001/api/trading/market-data/BTCUSDm", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ BTCUSDm market data retrieved")
            print(f"   Bid: {data.get('bid')}")
            print(f"   Ask: {data.get('ask')}")
            print(f"   Spread: {data.get('spread')}")
            
            # Calculate spread in pips with corrected pip value
            spread_raw = data.get('spread', 0)
            pip_value = 1.0  # Corrected BTCUSDm pip value
            spread_pips = spread_raw / pip_value
            print(f"   Spread (pips): {spread_pips:.2f} (with corrected pip value {pip_value})")
            print(f"   Threshold: 50.0 pips")
            print(f"   Result: {'✅ ACCEPTABLE' if spread_pips <= 50.0 else '❌ TOO WIDE'}")
        else:
            print(f"❌ Market data failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Market data error: {e}")
    
    # Test 5: Wait and monitor logs
    print("\n5. Monitoring for 30 seconds...")
    print("   Watch for:")
    print("   ✅ No more 'Spread too wide' errors")
    print("   ✅ Better margin calculations")
    print("   ✅ No more NoneType retcode errors")
    print("   ✅ Proper position size calculations")
    
    time.sleep(30)
    
    return True

if __name__ == "__main__":
    print("🎯 Testing All Fixes")
    print("=" * 50)
    
    success = test_fixes()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 TESTING COMPLETED!")
        print("\n📝 Fixes Applied:")
        print("✅ BTCUSDm pip value: 0.01 → 1.0")
        print("✅ ETHUSDm pip value: 0.01 → 0.1") 
        print("✅ Improved margin calculation")
        print("✅ Better error handling for order placement")
        print("✅ Enhanced position size calculation")
        print("✅ More detailed error logging")
        
        print("\n🔍 Check backend logs for:")
        print("   - No more 'Spread too wide for trading' for reasonable spreads")
        print("   - Better margin requirement calculations")
        print("   - No more NoneType retcode errors")
        print("   - Detailed position size calculation logs")
    else:
        print("❌ TESTING INCOMPLETE")
        print("   Some issues may still exist")
    
    print(f"\n🌐 Monitor backend logs at terminal for real-time updates")
