#!/usr/bin/env python3
"""
Test Error Fix for GPT Analysis
Simple test to verify the error has been resolved
"""
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_error_fix():
    """Test that the error has been fixed"""
    print("🔧 Testing Error Fix for GPT Analysis")
    print("=" * 50)
    
    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.services.gpt_analyzer import GPTAnalyzer
        from backend.utils.technical_analysis import TechnicalAnalyzer
        
        # Connect to MT5
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ Cannot connect to MT5")
            return False
        
        print("✅ MT5 connected")
        
        # Test symbol
        symbol = "BTCUSDm"
        
        # Get market data
        market_data = await connector.get_market_data(symbol)
        if not market_data:
            print(f"❌ No market data for {symbol}")
            await connector.disconnect()
            return False
        
        print(f"✅ Market data: ${market_data.mid_price:,.5f}")
        
        # Get multi-timeframe data
        timeframe_data = await connector.get_multi_timeframe_data(
            symbol, 
            timeframes=["15m", "30m", "1h", "4h"]
        )
        
        print(f"✅ Timeframe data: {[f'{tf}:{len(data)}' for tf, data in timeframe_data.items()]}")
        
        if not any(timeframe_data.values()):
            print("❌ No timeframe data available")
            await connector.disconnect()
            return False
        
        # Calculate technical indicators safely
        technical_analyzer = TechnicalAnalyzer()
        technical_indicators = {}
        
        for tf, ohlcv_data in timeframe_data.items():
            if ohlcv_data:
                try:
                    indicators = technical_analyzer.calculate_indicators(ohlcv_data)
                    if indicators:
                        technical_indicators[tf] = indicators
                        print(f"✅ {tf}: Technical indicators calculated")
                    else:
                        print(f"⚠️ {tf}: No indicators (insufficient data)")
                except Exception as e:
                    print(f"⚠️ {tf}: Indicator error - {e}")
        
        # Initialize GPT analyzer
        gpt_analyzer = GPTAnalyzer()
        print("✅ GPT Analyzer initialized")
        
        # Test the fixed multi-timeframe analysis
        print("\n🧠 Testing Fixed Multi-Timeframe Analysis...")
        
        try:
            analysis = await gpt_analyzer.analyze_market_multi_timeframe(
                symbol=symbol,
                market_data=market_data,
                timeframe_data=timeframe_data,
                technical_indicators=technical_indicators
            )
            
            # Check if analysis is valid
            if analysis and isinstance(analysis, dict):
                print("✅ Multi-timeframe analysis completed successfully!")
                print(f"📊 Decision: {analysis.get('decision', 'UNKNOWN')}")
                print(f"🎯 Confidence: {analysis.get('confidence', 0):.2f}")
                print(f"💡 Reasoning: {analysis.get('reasoning', 'No reasoning')[:100]}...")
                
                # Test that all required fields are present
                required_fields = ['decision', 'confidence', 'reasoning', 'symbol', 'timestamp']
                missing_fields = [field for field in required_fields if field not in analysis]
                
                if missing_fields:
                    print(f"⚠️ Missing fields: {missing_fields}")
                else:
                    print("✅ All required fields present")
                
                await connector.disconnect()
                return True
                
            elif analysis is None:
                print("❌ Analysis returned None")
                await connector.disconnect()
                return False
            else:
                print(f"❌ Analysis returned unexpected type: {type(analysis)}")
                await connector.disconnect()
                return False
                
        except Exception as analysis_error:
            print(f"❌ Analysis error: {analysis_error}")
            import traceback
            traceback.print_exc()
            await connector.disconnect()
            return False
        
    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_safe_indicators():
    """Test the safe indicators function"""
    print("\n🔧 Testing Safe Indicators Function")
    print("=" * 40)
    
    try:
        from backend.services.gpt_analyzer import GPTAnalyzer
        
        gpt_analyzer = GPTAnalyzer()
        
        # Test with None
        result1 = gpt_analyzer._safe_get_indicators(None, "15m")
        print(f"✅ None input: {result1}")
        
        # Test with empty dict
        result2 = gpt_analyzer._safe_get_indicators({}, "15m")
        print(f"✅ Empty dict: {result2}")
        
        # Test with missing timeframe
        result3 = gpt_analyzer._safe_get_indicators({"1h": {}}, "15m")
        print(f"✅ Missing timeframe: {result3}")
        
        # Test with valid dict
        result4 = gpt_analyzer._safe_get_indicators({"15m": {"rsi": 50, "sma_20": 100}}, "15m")
        print(f"✅ Valid dict: {result4}")
        
        return True
        
    except Exception as e:
        print(f"❌ Safe indicators test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 ERROR FIX VERIFICATION TEST")
    print("=" * 60)
    
    # Test 1: Safe indicators function
    success1 = await test_safe_indicators()
    
    # Test 2: Full error fix test
    success2 = await test_error_fix()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ ERROR FIX SUCCESSFUL!")
        print("\n🎉 FIXES IMPLEMENTED:")
        print("   ✅ Added validation for None/invalid GPT analysis")
        print("   ✅ Safe technical indicators handling")
        print("   ✅ Better error handling and logging")
        print("   ✅ Fallback responses for failed analysis")
        print("   ✅ Type checking and validation")
    else:
        print("❌ Some tests failed - error may still exist")

if __name__ == "__main__":
    asyncio.run(main())
