import React from 'react';

export const Checkbox = ({
  checked,
  onChange,
  disabled = false,
  className = '',
  id,
  ...props
}) => {
  return (
    <input
      type="checkbox"
      id={id}
      checked={checked}
      onChange={onChange}
      disabled={disabled}
      className={`h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded cursor-pointer ${disabled ? 'cursor-not-allowed' : ''} ${className}`}
      {...props}
    />
  );
};
