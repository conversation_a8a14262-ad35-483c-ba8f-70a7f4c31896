#!/usr/bin/env python3
"""
Test the fixes for MT5 disconnection and GPT analysis issues
"""
import requests
import time

BASE_URL = "http://localhost:8001"

def test_system_status():
    """Test system status after fixes"""
    print("🔧 === TESTING FIXES ===")

    # Test current symbols configuration
    print("\n1. Testing Symbol Configuration...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/current")
        if response.status_code == 200:
            data = response.json()
            symbols = data.get('symbols', [])
            print(f"✅ Found {len(symbols)} configured symbols:")
            for i, symbol in enumerate(symbols, 1):
                print(f"  {i:2d}. {symbol}")

            if len(symbols) == 12:
                print("✅ All 12 symbols configured correctly!")
            else:
                print(f"⚠️  Expected 12 symbols, found {len(symbols)}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Start trading engine
    print("\n2. Starting Trading Engine...")
    try:
        response = requests.post(f"{BASE_URL}/api/trading/start", json={"auto_trading_enabled": True})
        if response.status_code == 200:
            print("✅ Trading engine start request sent")
            print("⏳ Waiting for initialization...")
            time.sleep(10)  # Wait for full initialization
        else:
            print(f"❌ Error starting engine: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error: {e}")
        return

    # Test system status
    print("\n3. Testing System Status...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/status")
        if response.status_code == 200:
            status = response.json()
            print(f"📊 System Status:")
            print(f"  MT5 Connected: {'✅' if status.get('is_mt5_connected') else '❌'}")
            print(f"  GPT Available: {'✅' if status.get('is_gpt_available') else '❌'}")
            print(f"  Trading Enabled: {'✅' if status.get('is_trading_enabled') else '❌'}")
            print(f"  System Health: {status.get('system_health', 'UNKNOWN')}")

            # Check if issues are resolved
            mt5_ok = status.get('is_mt5_connected', False)
            gpt_ok = status.get('is_gpt_available', False)

            if mt5_ok and gpt_ok:
                print(f"\n🎉 SUCCESS! All issues resolved!")
            else:
                print(f"\n⚠️  Some issues remain:")
                if not mt5_ok:
                    print(f"   - MT5 still disconnected")
                if not gpt_ok:
                    print(f"   - GPT still unavailable")
        else:
            print(f"❌ Error getting status: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test account API
    print("\n4. Testing Account API...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/account")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Account API working:")
            print(f"  Login: {data.get('login')}")
            print(f"  Balance: ${data.get('balance', 0):,.2f}")
            print(f"  Equity: ${data.get('equity', 0):,.2f}")
        elif response.status_code == 503:
            print(f"⚠️  Account API: Service Unavailable (MT5 not connected)")
        else:
            print(f"❌ Account API error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

    # Test positions API
    print("\n5. Testing Positions API...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/positions")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Positions API working: {len(data)} positions")
        else:
            print(f"❌ Positions API error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

def check_backend_logs():
    """Instructions for checking backend logs"""
    print(f"\n📋 === NEXT STEPS ===")
    print(f"1. Check backend terminal for:")
    print(f"   - No more JSON serialization errors")
    print(f"   - All 12 symbols being processed")
    print(f"   - MT5 connection successful")
    print(f"   - GPT analysis working")
    print(f"")
    print(f"2. Expected log messages:")
    print(f"   ✅ 'Connected to MT5 account: **********'")
    print(f"   ✅ 'Symbol USDPHP: Found, Selected: True'")
    print(f"   ✅ 'Symbol USDMYR: Found, Selected: True'")
    print(f"   ✅ 'Symbol USDTWD: Found, Selected: True'")
    print(f"   ✅ 'Symbol USDRUB: Found, Selected: True'")
    print(f"   ✅ 'Processing symbol: USDPHP'")
    print(f"   ✅ 'Skipping USDPHP - Signal: HOLD, Confidence: X.X'")
    print(f"")
    print(f"3. If still having issues:")
    print(f"   - Restart backend completely (Ctrl+C, then python -m backend.main)")
    print(f"   - Check .env file for correct symbol list")
    print(f"   - Verify MT5 login credentials")
    print(f"   - Check OpenAI API key")

if __name__ == "__main__":
    test_system_status()
    check_backend_logs()
