#!/usr/bin/env python3
"""
Test Symbol Selection Feature
"""
import requests
import time

BASE_URL = "http://localhost:8000"

def test_symbol_selection():
    """Test the new symbol selection feature"""
    print("🎯 === TESTING SYMBOL SELECTION FEATURE ===")
    
    # Test 1: Get all available symbols
    print("\n1. Testing Get Available Symbols...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/current")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Available symbols: {data.get('symbols', [])}")
            print(f"✅ Active symbols: {data.get('active_symbols', [])}")
            print(f"✅ Total available: {data.get('total', 0)}")
            print(f"✅ Total active: {data.get('active_total', 0)}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Get active symbols endpoint
    print("\n2. Testing Get Active Symbols Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/active")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Active symbols: {data.get('active_symbols', [])}")
            print(f"✅ All symbols: {data.get('all_symbols', [])}")
            print(f"✅ Active count: {data.get('total_active', 0)}")
            print(f"✅ Available count: {data.get('total_available', 0)}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Update active symbols
    print("\n3. Testing Update Active Symbols...")
    try:
        # Select first 3 symbols for testing
        test_symbols = ["USDAED", "USDAMD", "USDARS"]
        
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": test_symbols}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Update successful: {data.get('message')}")
            print(f"✅ Active symbols set to: {data.get('active_symbols')}")
            print(f"✅ Note: {data.get('note')}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 4: Verify update
    print("\n4. Verifying Update...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/active")
        if response.status_code == 200:
            data = response.json()
            active_symbols = data.get('active_symbols', [])
            print(f"✅ Current active symbols: {active_symbols}")
            
            if set(active_symbols) == set(test_symbols):
                print("✅ Update verified successfully!")
            else:
                print(f"⚠️  Update not reflected yet. Expected: {test_symbols}, Got: {active_symbols}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 5: Test with invalid symbols
    print("\n5. Testing Invalid Symbols...")
    try:
        invalid_symbols = ["INVALID1", "INVALID2"]
        
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": invalid_symbols}
        )
        
        if response.status_code == 400:
            print("✅ Invalid symbols correctly rejected")
            print(f"✅ Error message: {response.json().get('detail')}")
        else:
            print(f"⚠️  Expected 400 error, got: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 6: Test mixed valid/invalid symbols
    print("\n6. Testing Mixed Valid/Invalid Symbols...")
    try:
        mixed_symbols = ["USDAED", "INVALID", "USDAMD"]
        
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": mixed_symbols}
        )
        
        if response.status_code == 400:
            print("✅ Mixed symbols correctly rejected")
            print(f"✅ Error message: {response.json().get('detail')}")
        else:
            print(f"⚠️  Expected 400 error, got: {response.status_code}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 7: Reset to default selection
    print("\n7. Resetting to Default Selection...")
    try:
        default_symbols = ["USDAED", "USDAMD", "USDARS", "USDBDT"]
        
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": default_symbols}
        )
        
        if response.status_code == 200:
            print("✅ Reset to default selection successful")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_trading_engine_with_selection():
    """Test if trading engine uses selected symbols"""
    print("\n🤖 === TESTING TRADING ENGINE WITH SYMBOL SELECTION ===")
    
    # Set specific symbols
    print("\n1. Setting specific symbols for testing...")
    try:
        test_symbols = ["USDAED", "USDAMD"]  # Only 2 symbols
        
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": test_symbols}
        )
        
        if response.status_code == 200:
            print(f"✅ Set active symbols to: {test_symbols}")
        else:
            print(f"❌ Error setting symbols: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    # Start trading engine
    print("\n2. Starting trading engine...")
    try:
        response = requests.post(f"{BASE_URL}/api/trading/start", json={"auto_trading_enabled": True})
        if response.status_code == 200:
            print("✅ Trading engine start request sent")
            print("⏳ Waiting for engine to process symbols...")
            time.sleep(15)  # Wait for processing
        else:
            print(f"❌ Error starting engine: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    # Check if only selected symbols are being processed
    print("\n3. Checking if only selected symbols are processed...")
    print("📋 Check backend logs for:")
    print(f"   ✅ 'Processing symbol: USDAED'")
    print(f"   ✅ 'Processing symbol: USDAMD'")
    print(f"   ❌ Should NOT see: 'Processing symbol: USDARS'")
    print(f"   ❌ Should NOT see: 'Processing symbol: USDBDT'")
    print(f"   ❌ Should NOT see other symbols being processed")

def show_usage_instructions():
    """Show how to use the new feature"""
    print("\n📋 === HOW TO USE SYMBOL SELECTION ===")
    print("\n🖥️  Frontend Usage:")
    print("1. Open http://localhost:3000")
    print("2. Look for 'Symbol Selection for AI Analysis' card")
    print("3. Check/uncheck symbols you want AI to analyze")
    print("4. Click 'Save Changes'")
    print("5. Changes take effect immediately")
    
    print("\n🔧 API Usage:")
    print("1. GET /api/trading/symbols/active - Get current active symbols")
    print("2. POST /api/trading/symbols/active/update - Update active symbols")
    print("   Body: {\"active_symbols\": [\"USDAED\", \"USDAMD\"]}")
    
    print("\n⚙️  Configuration:")
    print("1. Active symbols stored in .env file as ACTIVE_SYMBOLS")
    print("2. If ACTIVE_SYMBOLS is empty, uses first 4 symbols as default")
    print("3. Trading engine only processes active symbols")
    
    print("\n🎯 Benefits:")
    print("✅ Save API costs - only analyze selected pairs")
    print("✅ Focus on preferred markets")
    print("✅ Reduce system load")
    print("✅ Better risk management")
    print("✅ Real-time control without restart")

if __name__ == "__main__":
    test_symbol_selection()
    test_trading_engine_with_selection()
    show_usage_instructions()
