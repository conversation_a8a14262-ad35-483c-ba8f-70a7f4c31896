#!/usr/bin/env python3
"""
Test spread calculation fix
"""

def test_spread_calculation():
    """Test the corrected spread calculation"""
    print("🔧 === TESTING SPREAD CALCULATION FIX ===")
    
    # Data from your screenshot
    bid = 108442.87
    ask = 108471.88
    spread_raw = ask - bid
    
    print(f"📊 Market Data:")
    print(f"   Bid: {bid}")
    print(f"   Ask: {ask}")
    print(f"   Spread (raw): {spread_raw}")
    
    # OLD (WRONG) calculation
    old_pip_value = 0.01
    old_spread_pips = spread_raw / old_pip_value
    old_threshold = 10.0
    
    print(f"\n❌ OLD (WRONG) Calculation:")
    print(f"   Pip value: {old_pip_value}")
    print(f"   Spread (pips): {old_spread_pips:.2f}")
    print(f"   Threshold: {old_threshold} pips")
    print(f"   Result: {'REJECTED' if old_spread_pips > old_threshold else 'ACCEPTED'}")
    print(f"   Problem: {old_spread_pips:.2f} pips is way too high!")
    
    # NEW (CORRECT) calculation
    new_pip_value = 1.0
    new_spread_pips = spread_raw / new_pip_value
    new_threshold = 50.0
    
    print(f"\n✅ NEW (CORRECT) Calculation:")
    print(f"   Pip value: {new_pip_value}")
    print(f"   Spread (pips): {new_spread_pips:.2f}")
    print(f"   Threshold: {new_threshold} pips")
    print(f"   Result: {'REJECTED' if new_spread_pips > new_threshold else 'ACCEPTED'}")
    print(f"   This makes sense: {new_spread_pips:.2f} pips is reasonable for Bitcoin")
    
    # Explanation
    print(f"\n📚 EXPLANATION:")
    print(f"   Bitcoin (BTCUSDm) trades in whole dollar increments")
    print(f"   A spread of ${spread_raw:.2f} = {new_spread_pips:.2f} pips")
    print(f"   This is normal for crypto markets")
    
    # Test other examples
    print(f"\n🧪 Testing other examples:")
    
    examples = [
        ("Small spread", 108442.87, 108445.87),  # $3 spread
        ("Medium spread", 108442.87, 108452.87), # $10 spread  
        ("Large spread", 108442.87, 108492.87),  # $50 spread
    ]
    
    for name, test_bid, test_ask in examples:
        test_spread = test_ask - test_bid
        test_pips = test_spread / new_pip_value
        result = "ACCEPTED" if test_pips <= new_threshold else "REJECTED"
        print(f"   {name}: ${test_spread:.2f} = {test_pips:.2f} pips → {result}")

def test_other_symbols():
    """Test pip values for other symbols"""
    print(f"\n🔍 TESTING OTHER SYMBOL PIP VALUES:")
    
    symbols = {
        "BTCUSDm": {"pip_value": 1.0, "example_spread": 29.01},
        "ETHUSDm": {"pip_value": 0.1, "example_spread": 2.5},
        "XAUUSD": {"pip_value": 0.01, "example_spread": 0.35},
        "EURUSD": {"pip_value": 0.0001, "example_spread": 0.0015},
    }
    
    for symbol, config in symbols.items():
        pip_value = config["pip_value"]
        example_spread = config["example_spread"]
        spread_pips = example_spread / pip_value
        
        print(f"   {symbol}:")
        print(f"     Pip value: {pip_value}")
        print(f"     Example spread: {example_spread}")
        print(f"     Spread in pips: {spread_pips:.2f}")

if __name__ == "__main__":
    test_spread_calculation()
    test_other_symbols()
    
    print(f"\n🎯 SUMMARY:")
    print(f"✅ Fixed BTCUSDm pip value: 0.01 → 1.0")
    print(f"✅ Fixed ETHUSDm pip value: 0.01 → 0.1") 
    print(f"✅ Spread calculation now correct")
    print(f"✅ BTCUSDm should now pass validation")
    print(f"\n🔧 Next step: Restart backend to apply changes")
