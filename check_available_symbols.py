#!/usr/bin/env python3
"""
Tool to check available trading symbols in MetaTrader 5
"""
import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def connect_mt5():
    """Connect to MetaTrader 5"""
    if not mt5.initialize():
        print("❌ Failed to initialize MT5")
        return False
    
    # Get connection parameters from .env
    login = int(os.getenv('MT5_LOGIN'))
    password = os.getenv('MT5_PASSWORD')
    server = os.getenv('MT5_SERVER')
    
    # Connect to account
    if not mt5.login(login, password=password, server=server):
        print(f"❌ Failed to connect to account {login}")
        print(f"Error: {mt5.last_error()}")
        return False
    
    print(f"✅ Connected to MT5 account: {login}")
    return True

def get_all_symbols():
    """Get all available symbols"""
    symbols = mt5.symbols_get()
    if symbols is None:
        print("❌ Failed to get symbols")
        return []
    
    return symbols

def filter_symbols_by_type(symbols, symbol_type="forex"):
    """Filter symbols by type"""
    filtered = []
    
    for symbol in symbols:
        symbol_name = symbol.name.upper()
        
        if symbol_type == "forex":
            # Major and minor forex pairs
            forex_pairs = [
                'EURUSD', 'GBPUSD', 'USDJPY', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD',
                'EURJPY', 'GBPJPY', 'EURGBP', 'AUDJPY', 'EURAUD', 'EURCHF', 'AUDCAD',
                'GBPCHF', 'GBPAUD', 'GBPCAD', 'AUDCHF', 'CADJPY', 'CHFJPY', 'NZDJPY'
            ]
            if symbol_name in forex_pairs:
                filtered.append(symbol)
                
        elif symbol_type == "metals":
            # Precious metals
            if any(metal in symbol_name for metal in ['XAU', 'XAG', 'GOLD', 'SILVER']):
                filtered.append(symbol)
                
        elif symbol_type == "crypto":
            # Cryptocurrencies
            crypto_symbols = ['BTC', 'ETH', 'LTC', 'XRP', 'ADA', 'DOT', 'LINK']
            if any(crypto in symbol_name for crypto in crypto_symbols):
                filtered.append(symbol)
                
        elif symbol_type == "indices":
            # Stock indices
            indices = ['US30', 'US500', 'NAS100', 'UK100', 'GER30', 'FRA40', 'JPN225']
            if any(index in symbol_name for index in indices):
                filtered.append(symbol)
                
        elif symbol_type == "commodities":
            # Commodities
            commodities = ['OIL', 'BRENT', 'NGAS', 'WHEAT', 'CORN', 'SUGAR']
            if any(commodity in symbol_name for commodity in commodities):
                filtered.append(symbol)
    
    return filtered

def display_symbols(symbols, title):
    """Display symbols in a formatted table"""
    if not symbols:
        print(f"\n❌ No {title} found")
        return
    
    print(f"\n📊 === {title.upper()} ===")
    print(f"{'Symbol':<15} {'Description':<30} {'Spread':<10} {'Selected':<10}")
    print("-" * 70)
    
    for symbol in symbols:
        # Get current tick to check spread
        tick = mt5.symbol_info_tick(symbol.name)
        spread = "N/A"
        if tick:
            spread = f"{(tick.ask - tick.bid):.5f}"
        
        selected = "✅" if symbol.select else "❌"
        description = symbol.description[:28] + "..." if len(symbol.description) > 28 else symbol.description
        
        print(f"{symbol.name:<15} {description:<30} {spread:<10} {selected:<10}")

def search_symbols(symbols, search_term):
    """Search for symbols containing specific term"""
    found = []
    search_term = search_term.upper()
    
    for symbol in symbols:
        if search_term in symbol.name.upper() or search_term in symbol.description.upper():
            found.append(symbol)
    
    return found

def generate_config_template(symbols, symbol_type):
    """Generate configuration template for selected symbols"""
    if not symbols:
        return
    
    print(f"\n🔧 === CONFIGURATION TEMPLATE FOR {symbol_type.upper()} ===")
    
    # .env format
    symbol_names = [s.name for s in symbols[:10]]  # Limit to 10 symbols
    print(f"\n# Add to .env file:")
    print(f"TRADING_SYMBOLS={','.join(symbol_names)}")
    
    # config.py format
    print(f"\n# Add to backend/config.py SYMBOL_CONFIG:")
    for symbol in symbols[:5]:  # Show first 5 as example
        pip_value = 0.0001 if 'USD' in symbol.name else 0.01
        spread_threshold = 3.0 if symbol_type == "forex" else 10.0
        
        print(f'''    "{symbol.name}": {{
        "pip_value": {pip_value},
        "min_lot": 0.01,
        "max_lot": 100.0,
        "lot_step": 0.01,
        "spread_threshold": {spread_threshold}
    }},''')

def main():
    """Main function"""
    print("🔍 === MetaTrader 5 Symbol Checker ===")
    
    if not connect_mt5():
        return
    
    print("\n📡 Getting all available symbols...")
    all_symbols = get_all_symbols()
    print(f"✅ Found {len(all_symbols)} total symbols")
    
    while True:
        print(f"\n🎯 === SYMBOL CATEGORIES ===")
        print("1. Forex Pairs (Major/Minor)")
        print("2. Precious Metals (Gold/Silver)")
        print("3. Cryptocurrencies")
        print("4. Stock Indices")
        print("5. Commodities")
        print("6. Search by name")
        print("7. Show all symbols")
        print("0. Exit")
        
        choice = input("\nSelect option (0-7): ").strip()
        
        if choice == "0":
            break
        elif choice == "1":
            forex_symbols = filter_symbols_by_type(all_symbols, "forex")
            display_symbols(forex_symbols, "Forex Pairs")
            generate_config_template(forex_symbols, "forex")
        elif choice == "2":
            metal_symbols = filter_symbols_by_type(all_symbols, "metals")
            display_symbols(metal_symbols, "Precious Metals")
            generate_config_template(metal_symbols, "metals")
        elif choice == "3":
            crypto_symbols = filter_symbols_by_type(all_symbols, "crypto")
            display_symbols(crypto_symbols, "Cryptocurrencies")
            generate_config_template(crypto_symbols, "crypto")
        elif choice == "4":
            index_symbols = filter_symbols_by_type(all_symbols, "indices")
            display_symbols(index_symbols, "Stock Indices")
            generate_config_template(index_symbols, "indices")
        elif choice == "5":
            commodity_symbols = filter_symbols_by_type(all_symbols, "commodities")
            display_symbols(commodity_symbols, "Commodities")
            generate_config_template(commodity_symbols, "commodities")
        elif choice == "6":
            search_term = input("Enter search term: ").strip()
            if search_term:
                found_symbols = search_symbols(all_symbols, search_term)
                display_symbols(found_symbols, f"Search Results for '{search_term}'")
                generate_config_template(found_symbols, "search")
        elif choice == "7":
            # Show first 50 symbols
            display_symbols(all_symbols[:50], "All Symbols (First 50)")
        else:
            print("❌ Invalid option")
    
    mt5.shutdown()
    print("\n👋 Disconnected from MT5")

if __name__ == "__main__":
    main()
