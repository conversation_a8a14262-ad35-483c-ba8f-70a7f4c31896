#!/usr/bin/env python3
"""
Demo: Symbol Selection Feature
Shows how to control which pairs AI analyzes
"""
import requests
import time
import json

BASE_URL = "http://localhost:8001"

def demo_symbol_selection():
    """Complete demo of symbol selection feature"""
    print("🎯 === DEMO: AI SYMBOL SELECTION FEATURE ===")
    print("This demo shows how to control which trading pairs AI analyzes")
    
    # Step 1: Show current configuration
    print("\n📊 STEP 1: Current Symbol Configuration")
    print("=" * 50)
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/active")
        if response.status_code == 200:
            data = response.json()
            all_symbols = data.get('all_symbols', [])
            active_symbols = data.get('active_symbols', [])
            
            print(f"📋 Available Symbols ({len(all_symbols)}):")
            for i, symbol in enumerate(all_symbols, 1):
                status = "🟢 ACTIVE" if symbol in active_symbols else "⚪ INACTIVE"
                print(f"   {i:2d}. {symbol:10s} - {status}")
            
            print(f"\n✅ Currently Active: {len(active_symbols)} symbols")
            print(f"   {', '.join(active_symbols)}")
            
        else:
            print(f"❌ Error getting symbols: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    # Step 2: Demo - Select specific symbols
    print(f"\n🎯 STEP 2: Selecting Specific Symbols for AI Analysis")
    print("=" * 50)
    print("Let's select only 2 symbols for AI to analyze (save API costs)")
    
    # Select first 2 symbols
    selected_symbols = all_symbols[:2]
    print(f"🔧 Selecting: {', '.join(selected_symbols)}")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": selected_symbols}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ {data.get('message')}")
            print(f"📝 Note: {data.get('note')}")
        else:
            print(f"❌ Error: {response.text}")
            return
    except Exception as e:
        print(f"❌ Error: {e}")
        return
    
    # Step 3: Verify the change
    print(f"\n🔍 STEP 3: Verifying Changes")
    print("=" * 50)
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/active")
        if response.status_code == 200:
            data = response.json()
            new_active = data.get('active_symbols', [])
            
            print(f"📊 Updated Configuration:")
            for symbol in all_symbols:
                if symbol in new_active:
                    print(f"   🟢 {symbol} - ACTIVE (AI will analyze)")
                else:
                    print(f"   ⚪ {symbol} - INACTIVE (AI will skip)")
            
            print(f"\n✅ AI will now analyze only: {', '.join(new_active)}")
            
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Step 4: Demo - Start trading engine
    print(f"\n🚀 STEP 4: Testing with Trading Engine")
    print("=" * 50)
    print("Starting trading engine to see AI analyze only selected symbols...")
    
    try:
        response = requests.post(f"{BASE_URL}/api/trading/start", json={"auto_trading_enabled": True})
        if response.status_code == 200:
            print("✅ Trading engine started")
            print("⏳ AI will analyze symbols in next cycle...")
            print("\n📋 Check backend logs for:")
            for symbol in selected_symbols:
                print(f"   ✅ 'Processing symbol: {symbol}'")
            
            print(f"\n📋 You should NOT see in logs:")
            for symbol in all_symbols:
                if symbol not in selected_symbols:
                    print(f"   ❌ 'Processing symbol: {symbol}'")
                    
        else:
            print(f"❌ Error starting engine: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Step 5: Demo - Reset to all symbols
    print(f"\n🔄 STEP 5: Reset to All Symbols")
    print("=" * 50)
    print("Resetting to analyze all available symbols...")
    
    try:
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": all_symbols}
        )
        
        if response.status_code == 200:
            print("✅ Reset successful - AI will now analyze all symbols")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

def show_benefits():
    """Show benefits of symbol selection"""
    print(f"\n🎁 === BENEFITS OF SYMBOL SELECTION ===")
    print("=" * 50)
    
    benefits = [
        "💰 Cost Savings: Reduce OpenAI API calls by 50-75%",
        "🎯 Focus Trading: Analyze only your preferred markets",
        "⚡ Performance: Faster analysis cycles with fewer symbols", 
        "🛡️ Risk Control: Limit exposure to specific currency pairs",
        "🔧 Flexibility: Change selection anytime without restart",
        "📊 Testing: Easy A/B testing with different symbol sets",
        "🌍 Market Hours: Select symbols based on trading sessions",
        "📈 Strategy: Focus on high-volatility or stable pairs"
    ]
    
    for benefit in benefits:
        print(f"   {benefit}")

def show_usage_scenarios():
    """Show real-world usage scenarios"""
    print(f"\n🌟 === REAL-WORLD USAGE SCENARIOS ===")
    print("=" * 50)
    
    scenarios = [
        {
            "name": "🌅 Asian Session Trading",
            "symbols": ["USDCNY", "USDKRW", "USDINR"],
            "reason": "Focus on Asian currencies during Asian market hours"
        },
        {
            "name": "💰 Major Pairs Only", 
            "symbols": ["XAUUSDm", "BTCUSDm"],
            "reason": "Trade only high-liquidity, well-known instruments"
        },
        {
            "name": "🧪 Testing Strategy",
            "symbols": ["XAUUSDm"],
            "reason": "Test new AI strategy on single pair first"
        },
        {
            "name": "💸 Budget Trading",
            "symbols": ["XAUUSDm", "BTCUSDm"],
            "reason": "Limit API costs to $10/month with 2 symbols"
        }
    ]
    
    for scenario in scenarios:
        print(f"\n📋 {scenario['name']}")
        print(f"   Symbols: {', '.join(scenario['symbols'])}")
        print(f"   Reason: {scenario['reason']}")

def show_api_reference():
    """Show API reference for developers"""
    print(f"\n🔧 === API REFERENCE ===")
    print("=" * 50)
    
    print("📡 GET /api/trading/symbols/active")
    print("   Returns current active symbols configuration")
    print("   Response: {active_symbols: [...], all_symbols: [...]}")
    
    print(f"\n📡 POST /api/trading/symbols/active/update")
    print("   Updates active symbols for AI analysis")
    print("   Body: {\"active_symbols\": [\"SYMBOL1\", \"SYMBOL2\"]}")
    print("   Response: {message: \"...\", active_symbols: [...]}")
    
    print(f"\n📋 Frontend Integration:")
    print("   - SymbolSelector component with checkboxes")
    print("   - Real-time updates without restart")
    print("   - Visual feedback for active/inactive symbols")

if __name__ == "__main__":
    demo_symbol_selection()
    show_benefits()
    show_usage_scenarios()
    show_api_reference()
    
    print(f"\n🎉 === DEMO COMPLETE ===")
    print("Symbol Selection feature is now ready!")
    print("✅ Backend API working")
    print("✅ Frontend component ready") 
    print("✅ Real-time symbol control")
    print("✅ Cost optimization enabled")
    print("\n🚀 Start frontend with: npm run dev")
    print("🌐 Open: http://localhost:3000")
