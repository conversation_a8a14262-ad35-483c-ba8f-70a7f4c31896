"""
GPT-4 Market Analysis Service
Provides intelligent market analysis using OpenAI GPT-4
"""
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
from openai import AsyncOpenAI
from loguru import logger

from ..models.trading_models import (
    MarketData, OHLCV, TechnicalIndicators, GPTAnalysis,
    SignalType, RiskLevel
)
from ..config import settings
import json
import os
from pathlib import Path


class GPTAnalyzer:
    """GPT-4 powered market analysis"""

    def __init__(self):
        self.client = AsyncOpenAI(api_key=settings.openai_api_key)
        self.model = settings.openai_model

        # Initialize evaluation storage
        self.evaluation_dir = Path("data/gpt_evaluations")
        self.evaluation_dir.mkdir(parents=True, exist_ok=True)

        # Load historical evaluations for learning
        self.historical_evaluations = self._load_historical_evaluations()

    async def analyze_market_multi_timeframe(
        self,
        symbol: str,
        market_data: MarketData,
        timeframe_data: Dict[str, List[OHLCV]],  # {"15m": [...], "1h": [...], "4h": [...]}
        technical_indicators: Optional[Dict[str, TechnicalIndicators]] = None,
        context: Optional[str] = None
    ) -> Optional[Dict[str, Any]]:
        """
        Perform comprehensive multi-timeframe market analysis using GPT-4
        """
        try:
            # Prepare multi-timeframe analysis data
            analysis_data = self._prepare_multi_timeframe_data(
                symbol, market_data, timeframe_data, technical_indicators
            )

            # Create enhanced analysis prompt with context
            prompt = self._create_multi_timeframe_prompt(analysis_data, context)

            # Debug logging
            logger.debug(f"🔍 Sending multi-timeframe analysis request for {symbol}")
            logger.debug(f"📊 Timeframes available: {list(timeframe_data.keys())}")
            logger.debug(f"🔧 Technical indicators: {list(technical_indicators.keys()) if technical_indicators else 'None'}")

            # Get GPT analysis
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_multi_timeframe_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=2000  # Increased for multi-timeframe analysis
            )

            logger.debug(f"✅ Received GPT response for {symbol}, length: {len(response.choices[0].message.content)}")

            # Parse response for multi-timeframe analysis
            analysis_result = self._parse_multi_timeframe_response(
                response.choices[0].message.content,
                symbol,
                market_data
            )

            return analysis_result

        except Exception as e:
            logger.error(f"Multi-timeframe GPT analysis error for {symbol}: {e}")
            import traceback
            logger.debug(f"Full traceback: {traceback.format_exc()}")

            # Return a safe default response
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'decision': 'HOLD',
                'confidence': 0.0,
                'entry_price': market_data.mid_price,
                'stop_loss': None,
                'take_profit': None,
                'risk_level': 'HIGH',
                'reasoning': f'Analysis failed due to error: {str(e)}',
                'timeframe_analysis': {},
                'risk_reward_ratio': 0,
                'market_structure': 'unknown',
                'key_levels': {}
            }

    async def analyze_market(
        self,
        symbol: str,
        market_data: MarketData,
        ohlcv_data: List[OHLCV],
        technical_indicators: Optional[TechnicalIndicators] = None
    ) -> Optional[GPTAnalysis]:
        """
        Perform comprehensive market analysis using GPT-4
        """
        try:
            # Prepare market data for analysis
            analysis_data = self._prepare_analysis_data(
                symbol, market_data, ohlcv_data, technical_indicators
            )

            # Create analysis prompt
            prompt = self._create_analysis_prompt(analysis_data)

            # Get GPT analysis
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,
                max_tokens=1500
            )

            # Parse response
            analysis_result = self._parse_gpt_response(
                response.choices[0].message.content,
                symbol,
                market_data
            )

            return analysis_result

        except Exception as e:
            logger.error(f"GPT analysis error for {symbol}: {e}")
            return None

    def _prepare_analysis_data(
        self,
        symbol: str,
        market_data: MarketData,
        ohlcv_data: List[OHLCV],
        technical_indicators: Optional[TechnicalIndicators]
    ) -> Dict[str, Any]:
        """Prepare market data for GPT analysis"""

        # Get recent price data
        recent_candles = ohlcv_data[-20:] if len(ohlcv_data) >= 20 else ohlcv_data

        # Calculate basic statistics
        if recent_candles:
            prices = [candle.close for candle in recent_candles]
            price_change = ((prices[-1] - prices[0]) / prices[0]) * 100 if len(prices) > 1 else 0
            volatility = self._calculate_volatility(prices)
        else:
            price_change = 0
            volatility = 0

        return {
            "symbol": symbol,
            "current_price": market_data.mid_price,
            "bid": market_data.bid,
            "ask": market_data.ask,
            "spread": market_data.spread,
            "timestamp": market_data.timestamp.isoformat(),
            "recent_candles": [
                {
                    "timestamp": candle.timestamp.isoformat(),
                    "open": candle.open,
                    "high": candle.high,
                    "low": candle.low,
                    "close": candle.close,
                    "volume": candle.volume
                }
                for candle in recent_candles
            ],
            "price_change_percent": round(price_change, 2),
            "volatility": round(volatility, 4),
            "technical_indicators": technical_indicators.dict() if technical_indicators and hasattr(technical_indicators, 'dict') else None
        }

    def _calculate_volatility(self, prices: List[float]) -> float:
        """Calculate price volatility"""
        if len(prices) < 2:
            return 0

        returns = []
        for i in range(1, len(prices)):
            returns.append((prices[i] - prices[i-1]) / prices[i-1])

        if not returns:
            return 0

        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        return variance ** 0.5

    def _prepare_multi_timeframe_data(
        self,
        symbol: str,
        market_data: MarketData,
        timeframe_data: Dict[str, List[OHLCV]],
        technical_indicators: Optional[Dict[str, TechnicalIndicators]]
    ) -> Dict[str, Any]:
        """Prepare multi-timeframe market data for GPT analysis"""

        analysis_data = {
            "symbol": symbol,
            "current_price": market_data.mid_price,
            "bid": market_data.bid,
            "ask": market_data.ask,
            "spread": market_data.spread,
            "timestamp": market_data.timestamp.isoformat(),
            "timeframes": {}
        }

        # Process each timeframe
        for timeframe, ohlcv_data in timeframe_data.items():
            if not ohlcv_data:
                continue

            # Get appropriate number of candles based on timeframe
            candle_count = self._get_candle_count_for_timeframe(timeframe)
            recent_candles = ohlcv_data[-candle_count:] if len(ohlcv_data) >= candle_count else ohlcv_data

            # Calculate timeframe-specific statistics
            if recent_candles:
                prices = [candle.close for candle in recent_candles]
                price_change = ((prices[-1] - prices[0]) / prices[0]) * 100 if len(prices) > 1 else 0
                volatility = self._calculate_volatility(prices)

                # Calculate support/resistance levels
                highs = [candle.high for candle in recent_candles]
                lows = [candle.low for candle in recent_candles]
                resistance = max(highs) if highs else 0
                support = min(lows) if lows else 0
            else:
                price_change = 0
                volatility = 0
                resistance = 0
                support = 0

            analysis_data["timeframes"][timeframe] = {
                "candles": [
                    {
                        "timestamp": candle.timestamp.isoformat(),
                        "open": candle.open,
                        "high": candle.high,
                        "low": candle.low,
                        "close": candle.close,
                        "volume": candle.volume
                    }
                    for candle in recent_candles[-10:]  # Last 10 candles for display
                ],
                "price_change_percent": round(price_change, 2),
                "volatility": round(volatility, 4),
                "resistance_level": resistance,
                "support_level": support,
                "trend_direction": self._determine_trend_direction(recent_candles),
                "technical_indicators": self._safe_get_indicators(technical_indicators, timeframe)
            }

        return analysis_data

    def _safe_get_indicators(self, technical_indicators: Optional[Dict], timeframe: str) -> Optional[Dict]:
        """Safely get technical indicators for a timeframe"""
        try:
            if not technical_indicators or timeframe not in technical_indicators:
                return None

            indicators = technical_indicators[timeframe]

            # Handle both dict and object types
            if hasattr(indicators, 'dict'):
                return indicators.dict()
            elif isinstance(indicators, dict):
                return indicators
            else:
                logger.warning(f"Unknown indicator type for {timeframe}: {type(indicators)}")
                return None

        except Exception as e:
            logger.warning(f"Error getting indicators for {timeframe}: {e}")
            return None

    def _get_candle_count_for_timeframe(self, timeframe: str) -> int:
        """Get appropriate number of candles to analyze for each timeframe"""
        timeframe_counts = {
            "15m": 96,   # 24 hours of 15m candles
            "30m": 48,   # 24 hours of 30m candles
            "1h": 48,    # 48 hours of 1h candles
            "4h": 42,    # 7 days of 4h candles
            "1d": 30     # 30 days of daily candles
        }
        return timeframe_counts.get(timeframe, 20)

    def _determine_trend_direction(self, candles: List[OHLCV]) -> str:
        """Determine trend direction from candle data"""
        if len(candles) < 5:
            return "neutral"

        # Simple trend determination using closing prices
        recent_closes = [candle.close for candle in candles[-5:]]

        # Count upward vs downward movements
        up_moves = sum(1 for i in range(1, len(recent_closes)) if recent_closes[i] > recent_closes[i-1])
        down_moves = sum(1 for i in range(1, len(recent_closes)) if recent_closes[i] < recent_closes[i-1])

        if up_moves > down_moves:
            return "bullish"
        elif down_moves > up_moves:
            return "bearish"
        else:
            return "neutral"

    def _get_multi_timeframe_system_prompt(self) -> str:
        """Get enhanced system prompt for multi-timeframe analysis"""
        return """You are an expert forex and cryptocurrency trading analyst with deep knowledge of multi-timeframe technical analysis, market psychology, and risk management.

Your task is to analyze market data across multiple timeframes (4h, 1h, 30m, 15m) and make a SINGLE trading decision based on the confluence of all timeframes.

ANALYSIS FLOW:
1. TIMEFRAME ANALYSIS (NO individual signals per timeframe):
   - 4h: Primary trend, major structure, key levels
   - 1h: Medium-term trend, intermediate levels
   - 30m: Intraday momentum, trend confirmation
   - 15m: Entry timing, short-term momentum

2. CONFLUENCE DECISION:
   - Analyze ALL timeframes first (no individual signals), check MCP for reference
   - Look for confluence (at least 3/4 timeframes must agree)
   - Make ONE final decision: ENTRY_BUY, ENTRY_SELL, or HOLD
   After analyzing ALL timeframes, make ONE decision:
   - ENTRY: Strong confluence across timeframes supports position
   - HOLD: Conflicting signals or unclear market direction

3. ENTRY REQUIREMENTS:
   - Multi-timeframe alignment (at least 3/4 timeframes agree)
   - Clear risk/reward setup
   - Proper entry timing from lower timeframes

4. STOP LOSS & TAKE PROFIT PLACEMENT:
   - Stop Loss: Place 5-10 pips BEFORE key support/resistance (not ON the level)
   - Take Profit: Place 5-10 pips BEFORE target resistance/support (not ON the level)
   - This accounts for spread, slippage, and false breakouts
6. **SAVE ALL YOUR ANALYSIS ON YOUR MCP FOR EVALUATION AND DECISION ON FURTHER TRADE MANAGEMENT**
5. RESPONSE FORMAT:
   Return a JSON object with these exact fields:
   {
     "decision": "ENTRY_BUY|ENTRY_SELL|HOLD",
     "confidence": 0.0-1.0,
     "entry_price": number,
     "stop_loss": number,
     "take_profit": number,
     "risk_level": "LOW|MEDIUM|HIGH",
     "reasoning": "detailed explanation of why this decision was made based on multi-timeframe confluence",
     "timeframe_analysis": {
       "4h": "trend, structure, key levels",
       "1h": "trend, momentum, levels",
       "30m": "confirmation, momentum",
       "15m": "entry timing, signals"
     },
     "risk_reward_ratio": number,
     "market_structure": "bullish|bearish|neutral|consolidation",
     "key_levels": {
       "support": number,
       "resistance": number,
       "stop_buffer_pips": number,
       "target_buffer_pips": number
     }
   }

CRITICAL: Only recommend ENTRY when there is strong multi-timeframe confluence. Be conservative and prioritize capital preservation."""

    def _get_system_prompt(self) -> str:
        """Get system prompt for GPT analysis"""
        return """You are an expert forex and cryptocurrency trading analyst with deep knowledge of technical analysis, market psychology, and risk management.

Your task is to analyze market data and provide trading recommendations with the following guidelines:

1. SIGNAL TYPES:
   - BUY: Strong bullish signal with clear entry opportunity
   - SELL: Strong bearish signal with clear entry opportunity
   - HOLD: Unclear market direction or high risk conditions

2. RISK LEVELS:
   - LOW: High confidence, clear trend, good risk/reward
   - MEDIUM: Moderate confidence, some uncertainty
   - HIGH: Low confidence, volatile conditions, unclear signals

3. ANALYSIS REQUIREMENTS:
   - Analyze the market data provided with your MCP for reference
   - Consider price action, volume, technical indicators
   - Identify support/resistance levels
   - Assess market sentiment and momentum
   - Provide specific entry, stop loss, and take profit levels
   - Explain reasoning clearly and concisely

4. RESPONSE FORMAT:
   Return a JSON object with these exact fields:
   {
     "signal": "BUY|SELL|HOLD",
     "confidence": 0.0-1.0,
     "entry_price": number,
     "stop_loss": number,
     "take_profit": number,
     "risk_level": "LOW|MEDIUM|HIGH",
     "reasoning": "detailed explanation",
     "technical_summary": "key technical points",
     "market_sentiment": "bullish|bearish|neutral"
   }

Be conservative with your recommendations and prioritize capital preservation."""

    def _create_analysis_prompt(self, analysis_data: Dict[str, Any]) -> str:
        """Create analysis prompt for GPT"""

        prompt = f"""
Analyze the following market data for {analysis_data['symbol']}:

CURRENT MARKET DATA:
- Symbol: {analysis_data['symbol']}
- Current Price: {analysis_data['current_price']}
- Bid: {analysis_data['bid']}
- Ask: {analysis_data['ask']}
- Spread: {analysis_data['spread']}
- Price Change: {analysis_data['price_change_percent']}%
- Volatility: {analysis_data['volatility']}

RECENT PRICE ACTION (Last 20 candles):
"""

        # Add recent candles data
        for i, candle in enumerate(analysis_data['recent_candles'][-10:]):
            prompt += f"Candle {i+1}: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']} V:{candle['volume']}\n"

        # Add technical indicators if available
        if analysis_data['technical_indicators']:
            indicators = analysis_data['technical_indicators']
            prompt += f"""
TECHNICAL INDICATORS:
- SMA 20: {indicators.get('sma_20', 'N/A')}
- SMA 50: {indicators.get('sma_50', 'N/A')}
- EMA 12: {indicators.get('ema_12', 'N/A')}
- EMA 26: {indicators.get('ema_26', 'N/A')}
- RSI: {indicators.get('rsi', 'N/A')}
- MACD: {indicators.get('macd', 'N/A')}
- MACD Signal: {indicators.get('macd_signal', 'N/A')}
- Bollinger Upper: {indicators.get('bollinger_upper', 'N/A')}
- Bollinger Lower: {indicators.get('bollinger_lower', 'N/A')}
"""

        prompt += """
Based on this data, provide a comprehensive trading analysis with specific entry, stop loss, and take profit recommendations. Consider:

1. Price action and trend direction
2. Support and resistance levels
3. Technical indicator signals
4. Market volatility and risk factors
5. Optimal entry timing
6. Risk/reward ratio
7. Market sentiment and potential reversals
8. Your MCP data for reference

Provide your analysis in the specified JSON format."""

        return prompt

    def _create_multi_timeframe_prompt(self, analysis_data: Dict[str, Any], context: Optional[str] = None) -> str:
        """Create multi-timeframe analysis prompt for GPT"""

        # Add context-specific header
        context_header = ""
        if context == "immediate_reentry_after_close":
            context_header = """
🚀 IMMEDIATE RE-ENTRY ANALYSIS AFTER TRAILING STOP CLOSE:
This analysis is triggered immediately after a profitable position was closed via GPT trailing stop.
Focus on determining if market conditions support an immediate re-entry in the same or opposite direction.
Consider momentum continuation vs reversal patterns.
"""
        elif context == "entry_decision":
            context_header = """
📊 ENTRY DECISION ANALYSIS:
No position currently exists for this symbol. Analyze market conditions to determine if a new position should be opened.
Focus on identifying clear entry opportunities with good risk/reward ratios.
Consider trend direction, momentum, and key support/resistance levels.
"""
        elif context == "trailing_stop_decision":
            context_header = """
💰 TRAILING STOP DECISION ANALYSIS:
Current position is profitable. Analyze if price will continue in favorable direction or if profit should be secured.
CONFIDENCE INTERPRETATION:
- Confidence ≥ 0.6: Price likely to continue → HOLD position
- Confidence < 0.6: Uncertain about continuation → TRAIL STOP (secure profit)
Focus on momentum sustainability, reversal signals, and profit protection.
"""

        prompt = f"""
{context_header}
MULTI-TIMEFRAME MARKET ANALYSIS for {analysis_data['symbol']}:

CURRENT MARKET DATA:
- Symbol: {analysis_data['symbol']}
- Current Price: ${analysis_data['current_price']:,.5f}
- Bid: ${analysis_data['bid']:,.5f}
- Ask: ${analysis_data['ask']:,.5f}
- Spread: {analysis_data['spread']}

TIMEFRAME ANALYSIS:
"""

        # Add analysis for each timeframe
        timeframe_order = ["4h", "1h", "30m", "15m"]  # Start with higher timeframes

        for timeframe in timeframe_order:
            if timeframe not in analysis_data["timeframes"]:
                continue

            tf_data = analysis_data["timeframes"][timeframe]

            prompt += f"""
{timeframe.upper()} TIMEFRAME:
- Trend Direction: {tf_data['trend_direction']}
- Price Change: {tf_data['price_change_percent']}%
- Volatility: {tf_data['volatility']}
- Support Level: ${tf_data['support_level']:,.5f}
- Resistance Level: ${tf_data['resistance_level']:,.5f}

Recent {timeframe} Candles (Last 10):
"""

            # Add recent candles for this timeframe
            for i, candle in enumerate(tf_data['candles']):
                prompt += f"  {i+1}: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']} V:{candle['volume']}\n"

            # Add technical indicators if available
            if tf_data.get('technical_indicators'):
                try:
                    indicators = tf_data['technical_indicators']
                    # Handle both dict and object types
                    if hasattr(indicators, 'dict'):
                        indicators = indicators.dict()

                    prompt += f"""
{timeframe} Technical Indicators:
- SMA 20: {indicators.get('sma_20', 'N/A')}
- SMA 50: {indicators.get('sma_50', 'N/A')}
- RSI: {indicators.get('rsi', 'N/A')}
- MACD: {indicators.get('macd', 'N/A')}
- Bollinger Upper: {indicators.get('bollinger_upper', 'N/A')}
- Bollinger Lower: {indicators.get('bollinger_lower', 'N/A')}
"""
                except Exception as indicator_error:
                    logger.warning(f"Error processing technical indicators for {timeframe}: {indicator_error}")
                    prompt += f"\n{timeframe} Technical Indicators: Error processing indicators\n"

        # Add learning context
        learning_context = self._get_learning_context()

        prompt += f"""

{learning_context}

MULTI-TIMEFRAME ANALYSIS REQUIREMENTS:

1. TIMEFRAME CONFLUENCE ANALYSIS:
   - 4h: Primary trend and major structure (most important)
   - 1h: Medium-term trend and key levels
   - 30m: Intraday momentum and confirmation
   - 15m: Entry timing and short-term signals

2. DECISION MAKING PROCESS:
   - Analyze ALL timeframes first (no individual signals)
   - Look for confluence (at least 3/4 timeframes must agree)
   - Make ONE final decision: ENTRY_BUY, ENTRY_SELL, or HOLD

3. STOP LOSS & TAKE PROFIT STRATEGY:
   - Place stops 5-10 pips BEFORE key levels (not ON them)
   - Account for spread, slippage, and false breakouts
   - Use higher timeframe structure for stop placement

4. LEARNING FROM HISTORY:
   - Consider patterns from recent trade outcomes
   - Adjust confidence based on similar market conditions
   - Learn from previous successful/failed setups

Provide a comprehensive analysis with detailed reasoning for your final decision.
Be conservative and only recommend ENTRY when there is strong multi-timeframe confluence.
"""

        return prompt

    def _parse_multi_timeframe_response(
        self,
        response_text: str,
        symbol: str,
        market_data: MarketData
    ) -> Optional[Dict[str, Any]]:
        """Parse GPT multi-timeframe analysis response"""
        try:
            # Extract JSON from response
            response_text = response_text.strip()
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]

            # Parse JSON
            analysis_data = json.loads(response_text)

            # Validate required fields
            required_fields = ['decision', 'confidence', 'reasoning']
            for field in required_fields:
                if field not in analysis_data:
                    logger.warning(f"Missing required field: {field}")
                    return None

            # Validate decision
            valid_decisions = ['ENTRY_BUY', 'ENTRY_SELL', 'HOLD']
            decision = analysis_data.get('decision', 'HOLD')
            if decision not in valid_decisions:
                logger.warning(f"Invalid decision: {decision}")
                decision = 'HOLD'

            # Validate confidence
            confidence = float(analysis_data.get('confidence', 0.5))
            confidence = max(0.0, min(1.0, confidence))

            # Validate risk level
            valid_risk_levels = ['LOW', 'MEDIUM', 'HIGH']
            risk_level = analysis_data.get('risk_level', 'MEDIUM')
            if risk_level not in valid_risk_levels:
                risk_level = 'MEDIUM'

            # Return structured dictionary
            result = {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'decision': decision,
                'confidence': confidence,
                'entry_price': analysis_data.get('entry_price', market_data.mid_price),
                'stop_loss': analysis_data.get('stop_loss'),
                'take_profit': analysis_data.get('take_profit'),
                'risk_level': risk_level,
                'reasoning': analysis_data.get('reasoning', ''),
                'timeframe_analysis': analysis_data.get('timeframe_analysis', {}),
                'risk_reward_ratio': analysis_data.get('risk_reward_ratio', 0),
                'market_structure': analysis_data.get('market_structure', 'neutral'),
                'key_levels': analysis_data.get('key_levels', {})
            }

            logger.info(f"✅ Multi-timeframe analysis parsed successfully for {symbol}")
            return result

        except Exception as e:
            logger.error(f"Error parsing multi-timeframe GPT response: {e}")
            logger.debug(f"Response text: {response_text}")

            # Return a safe default response instead of None
            return {
                'symbol': symbol,
                'timestamp': datetime.now().isoformat(),
                'decision': 'HOLD',
                'confidence': 0.0,
                'entry_price': market_data.mid_price,
                'stop_loss': None,
                'take_profit': None,
                'risk_level': 'HIGH',
                'reasoning': f'Failed to parse GPT response: {str(e)}',
                'timeframe_analysis': {},
                'risk_reward_ratio': 0,
                'market_structure': 'unknown',
                'key_levels': {}
            }

    def _parse_gpt_response(
        self,
        response_text: str,
        symbol: str,
        market_data: MarketData
    ) -> Optional[GPTAnalysis]:
        """Parse GPT response into GPTAnalysis object"""
        try:
            # Extract JSON from response
            response_text = response_text.strip()
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]

            # Parse JSON
            analysis_data = json.loads(response_text)

            # Validate and convert signal type
            signal_str = analysis_data.get('signal', 'HOLD').upper()
            if signal_str not in ['BUY', 'SELL', 'HOLD']:
                signal_str = 'HOLD'
            signal = SignalType(signal_str)

            # Validate and convert risk level
            risk_str = analysis_data.get('risk_level', 'MEDIUM').upper()
            if risk_str not in ['LOW', 'MEDIUM', 'HIGH']:
                risk_str = 'MEDIUM'
            risk_level = RiskLevel(risk_str)

            # Validate confidence
            confidence = float(analysis_data.get('confidence', 0.5))
            confidence = max(0.0, min(1.0, confidence))

            # Create GPTAnalysis object
            return GPTAnalysis(
                symbol=symbol,
                timestamp=datetime.now(),
                signal=signal,
                confidence=confidence,
                entry_price=analysis_data.get('entry_price'),
                stop_loss=analysis_data.get('stop_loss'),
                take_profit=analysis_data.get('take_profit'),
                risk_level=risk_level,
                reasoning=analysis_data.get('reasoning', ''),
                technical_summary=analysis_data.get('technical_summary', ''),
                market_sentiment=analysis_data.get('market_sentiment', 'neutral')
            )

        except Exception as e:
            logger.error(f"Error parsing GPT response: {e}")
            logger.debug(f"Response text: {response_text}")
            return None

    async def get_market_sentiment(self, symbols: List[str]) -> Dict[str, str]:
        """Get overall market sentiment for multiple symbols"""
        try:
            prompt = f"""
Analyze the overall market sentiment for these trading instruments: {', '.join(symbols)}

Consider:
1. Global economic conditions
2. Market trends and momentum
3. Risk-on vs risk-off sentiment
4. Correlation between assets

Provide a brief sentiment analysis for each symbol and overall market outlook.
Format as JSON: {{"symbol1": "sentiment", "symbol2": "sentiment", "overall": "sentiment"}}
Sentiment values: "bullish", "bearish", "neutral"
"""

            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": "You are a market sentiment analyst. Always respond with valid JSON only."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=500
            )

            content = response.choices[0].message.content.strip()

            # Try to extract JSON from response
            try:
                # Look for JSON content between curly braces
                start = content.find('{')
                end = content.rfind('}') + 1
                if start >= 0 and end > start:
                    json_content = content[start:end]
                    sentiment_data = json.loads(json_content)
                    return sentiment_data
                else:
                    logger.warning(f"No JSON found in GPT response: {content}")
                    return {symbol: "neutral" for symbol in symbols}
            except json.JSONDecodeError as je:
                logger.warning(f"JSON decode error: {je}, Content: {content}")
                return {symbol: "neutral" for symbol in symbols}

        except Exception as e:
            logger.error(f"Error getting market sentiment: {e}")
            return {symbol: "neutral" for symbol in symbols}

    async def analyze_position_for_trailing_stop(
        self,
        position: Dict[str, Any],
        market_data: MarketData,
        mt5_connector
    ) -> Dict[str, Any]:
        """
        Analyze open position using 30m, 15m, 5m timeframes to determine trailing stop
        Returns: {"should_trail": bool, "reasoning": str, "confidence": float}
        """
        try:
            symbol = position.get('symbol')

            # Get multi-timeframe data for position analysis (30m, 15m, 5m)
            position_timeframes = ["30m", "15m", "5m"]
            timeframe_data = await mt5_connector.get_multi_timeframe_data(
                symbol,
                timeframes=position_timeframes
            )

            # Prepare position analysis data with multi-timeframe
            analysis_data = self._prepare_position_multi_timeframe_data(
                position, market_data, timeframe_data
            )

            # Create position analysis prompt
            prompt = self._create_position_multi_timeframe_prompt(analysis_data)

            # Get GPT analysis
            response = await self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {
                        "role": "system",
                        "content": self._get_position_analysis_system_prompt()
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.2,  # Lower temperature for more consistent decisions
                max_tokens=1000
            )

            # Parse response
            result = self._parse_position_analysis_response(
                response.choices[0].message.content
            )

            # Log with detailed reasoning
            logger.info(f"🧠 GPT Position Analysis for {symbol}:")
            logger.info(f"   Decision: {'TRAIL STOP' if result['should_trail'] else 'CONTINUE'}")
            logger.info(f"   Confidence: {result['confidence']:.2f}")
            logger.info(f"   Reasoning: {result['reasoning']}")

            return result

        except Exception as e:
            logger.error(f"Error in position analysis: {e}")
            return {
                "should_trail": False,  # Conservative default
                "reasoning": f"Analysis failed: {e}",
                "confidence": 0.0
            }

    def _prepare_position_analysis_data(
        self,
        position: Dict[str, Any],
        market_data: MarketData,
        ohlcv_data: List[OHLCV]
    ) -> Dict[str, Any]:
        """Prepare position data for GPT analysis"""

        # Calculate position metrics
        entry_price = position.get('entry_price', 0)
        current_price = market_data.mid_price
        position_type = position.get('order_type', 'BUY')

        # Calculate P&L and percentage
        if position_type.upper() in ['BUY', 'BUY_LIMIT']:
            pnl_pips = current_price - entry_price
            pnl_percent = ((current_price - entry_price) / entry_price) * 100
        else:
            pnl_pips = entry_price - current_price
            pnl_percent = ((entry_price - current_price) / entry_price) * 100

        # Get recent price movement
        recent_candles = ohlcv_data[-10:] if len(ohlcv_data) >= 10 else ohlcv_data

        return {
            "position": {
                "symbol": position.get('symbol'),
                "ticket": position.get('ticket'),
                "type": position_type,
                "entry_price": entry_price,
                "current_price": current_price,
                "lot_size": position.get('lot_size'),
                "profit": position.get('profit', 0),
                "pnl_pips": pnl_pips,
                "pnl_percent": round(pnl_percent, 2),
                "stop_loss": position.get('stop_loss'),
                "take_profit": position.get('take_profit'),
                "open_time": position.get('open_time')
            },
            "market": {
                "current_price": current_price,
                "bid": market_data.bid,
                "ask": market_data.ask,
                "spread": market_data.spread
            },
            "recent_candles": [
                {
                    "timestamp": candle.timestamp.isoformat(),
                    "open": candle.open,
                    "high": candle.high,
                    "low": candle.low,
                    "close": candle.close,
                    "volume": candle.volume
                }
                for candle in recent_candles
            ]
        }

    def _get_position_analysis_system_prompt(self) -> str:
        """Get system prompt for position trailing stop analysis"""
        return """You are an expert trading analyst specializing in position management and trailing stop decisions using multi-timeframe analysis.

Your task is to analyze an open profitable position using 30m, 15m, and 5m timeframes to determine whether to:
1. CONTINUE the position (let it run to take profit)
2. TRAIL STOP (secure current profits)

ANALYSIS FRAMEWORK:

1. MULTI-TIMEFRAME MOMENTUM:
   - 30m: Medium-term momentum and trend strength
   - 15m: Short-term momentum and entry/exit signals
   - 5m: Immediate price action and momentum shifts

2. DECISION CRITERIA:
   - CONTINUE if: Strong momentum across timeframes, trend continuation signals, no exhaustion signs
   - TRAIL STOP if: Momentum weakening, divergence between timeframes, exhaustion/reversal signals

3. RISK CONSIDERATIONS:
   - Position is already profitable - protect gains vs maximize profits
   - Consider volatility and market structure
   - Look for momentum exhaustion or reversal patterns

4. RESPONSE FORMAT:
   Return a JSON object with these exact fields:
   {
     "should_trail": true/false,
     "confidence": 0.6-1.0,
     "reasoning": "detailed explanation of decision based on multi-timeframe analysis",
     "market_outlook": "bullish|bearish|neutral|exhausted",
     "risk_assessment": "low|medium|high",
     "timeframe_signals": {
       "30m": "momentum assessment",
       "15m": "trend signals",
       "5m": "immediate action"
     }
   }

CRITICAL: Be conservative with profitable positions. Only recommend trailing stop if there are clear signs of momentum exhaustion or reversal across multiple timeframes."""

    def _create_position_analysis_prompt(self, analysis_data: Dict[str, Any]) -> str:
        """Create position analysis prompt for GPT"""

        position = analysis_data['position']
        market = analysis_data['market']

        prompt = f"""
POSITION ANALYSIS REQUEST:

CURRENT POSITION:
- Symbol: {position['symbol']}
- Ticket: {position['ticket']}
- Type: {position['type']}
- Entry Price: ${position['entry_price']:,.5f}
- Current Price: ${position['current_price']:,.5f}
- Lot Size: {position['lot_size']}
- Current P&L: ${position['profit']:,.2f}
- P&L Percentage: {position['pnl_percent']:.2f}%
- Stop Loss: {position['stop_loss']}
- Take Profit: {position['take_profit']}

MARKET DATA:
- Bid: ${market['bid']:,.5f}
- Ask: ${market['ask']:,.5f}
- Spread: {market['spread']}

RECENT PRICE ACTION (Last 10 candles):
"""

        # Add recent candles
        for i, candle in enumerate(analysis_data['recent_candles']):
            prompt += f"Candle {i+1}: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']}\n"

        prompt += f"""

TRAILING STOP DECISION NEEDED:
The system is considering executing a trailing stop for this position.

Please analyze:
1. Is the current trend likely to continue in favor of this position?
2. Are there technical levels that suggest the move might be exhausted?
3. What is the risk/reward of holding vs. securing current profits?
4. Given the current market conditions, what is the optimal action?

Consider that this position is currently profitable ({position['pnl_percent']:.2f}%) and the system wants to know whether to:
- CONTINUE: Let the position run for more profit potential (should_trail: false)
- TRAIL STOP: Execute trailing stop to secure current profits (should_trail: true)

Provide your analysis in the specified JSON format with detailed reasoning."""

        return prompt

    def _parse_position_analysis_response(self, response_text: str) -> Dict[str, Any]:
        """Parse GPT position analysis response"""
        try:
            # Extract JSON from response
            response_text = response_text.strip()
            if response_text.startswith("```json"):
                response_text = response_text[7:]
            if response_text.endswith("```"):
                response_text = response_text[:-3]

            # Parse JSON
            analysis_data = json.loads(response_text)

            # Validate and return
            return {
                "should_trail": bool(analysis_data.get('should_trail', False)),
                "reasoning": analysis_data.get('reasoning', 'No reasoning provided'),
                "confidence": max(0.0, min(1.0, float(analysis_data.get('confidence', 0.5)))),
                "market_outlook": analysis_data.get('market_outlook', 'neutral'),
                "risk_assessment": analysis_data.get('risk_assessment', 'medium')
            }

        except Exception as e:
            logger.error(f"Error parsing position analysis response: {e}")
            logger.debug(f"Response text: {response_text}")
            return {
                "should_trail": False,  # Conservative default
                "reasoning": f"Failed to parse response: {e}",
                "confidence": 0.0,
                "market_outlook": "neutral",
                "risk_assessment": "high"
            }

    def _load_historical_evaluations(self) -> List[Dict[str, Any]]:
        """Load historical trading evaluations for learning"""
        try:
            evaluation_file = self.evaluation_dir / "trading_evaluations.json"
            if evaluation_file.exists():
                with open(evaluation_file, 'r') as f:
                    return json.load(f)
            return []
        except Exception as e:
            logger.error(f"Error loading historical evaluations: {e}")
            return []

    async def save_trading_evaluation(
        self,
        analysis_id: str,
        symbol: str,
        analysis_result: Dict[str, Any],
        market_data: MarketData,
        timeframe_data: Dict[str, List[OHLCV]],
        trade_outcome: Optional[Dict[str, Any]] = None
    ):
        """Save trading analysis and outcome for evaluation"""
        try:
            evaluation = {
                "id": analysis_id,
                "timestamp": datetime.now().isoformat(),
                "symbol": symbol,
                "analysis": analysis_result,
                "market_conditions": {
                    "price": market_data.mid_price,
                    "spread": market_data.spread,
                    "timeframe_trends": {
                        tf: self._determine_trend_direction(data[-20:]) if data else "neutral"
                        for tf, data in timeframe_data.items()
                    }
                },
                "trade_outcome": trade_outcome,
                "evaluation_status": "pending" if trade_outcome is None else "completed"
            }

            # Add to historical evaluations
            self.historical_evaluations.append(evaluation)

            # Save to file
            evaluation_file = self.evaluation_dir / "trading_evaluations.json"
            with open(evaluation_file, 'w') as f:
                json.dump(self.historical_evaluations, f, indent=2)

            logger.info(f"💾 Saved trading evaluation for {symbol} - ID: {analysis_id}")

        except Exception as e:
            logger.error(f"Error saving trading evaluation: {e}")

    async def update_trade_outcome(
        self,
        analysis_id: str,
        outcome: Dict[str, Any]
    ):
        """Update trade outcome for evaluation"""
        try:
            for evaluation in self.historical_evaluations:
                if evaluation["id"] == analysis_id:
                    evaluation["trade_outcome"] = outcome
                    evaluation["evaluation_status"] = "completed"
                    evaluation["outcome_timestamp"] = datetime.now().isoformat()
                    break

            # Save updated evaluations
            evaluation_file = self.evaluation_dir / "trading_evaluations.json"
            with open(evaluation_file, 'w') as f:
                json.dump(self.historical_evaluations, f, indent=2)

            logger.info(f"📊 Updated trade outcome for analysis ID: {analysis_id}")

        except Exception as e:
            logger.error(f"Error updating trade outcome: {e}")

    def _get_learning_context(self) -> str:
        """Get learning context from historical evaluations"""
        if not self.historical_evaluations:
            return "No historical data available."

        # Analyze recent performance
        recent_evaluations = self.historical_evaluations[-10:]  # Last 10 trades
        completed_trades = [e for e in recent_evaluations if e["evaluation_status"] == "completed"]

        if not completed_trades:
            return "No completed trades for learning context."

        # Calculate success metrics
        successful_trades = [
            t for t in completed_trades
            if t["trade_outcome"] and t["trade_outcome"].get("profit", 0) > 0
        ]

        success_rate = len(successful_trades) / len(completed_trades) * 100

        # Identify patterns
        context = f"""
LEARNING CONTEXT FROM RECENT TRADES:
- Total Recent Trades: {len(completed_trades)}
- Success Rate: {success_rate:.1f}%
- Successful Trades: {len(successful_trades)}

RECENT PATTERNS:
"""

        for trade in completed_trades[-5:]:  # Last 5 trades
            outcome = trade["trade_outcome"]
            profit = outcome.get("profit", 0) if outcome else 0
            decision = trade["analysis"].get("decision", "UNKNOWN")

            context += f"- {trade['symbol']}: {decision} -> {'✅ Profit' if profit > 0 else '❌ Loss'} (${profit:.2f})\n"

        return context

    def _prepare_position_multi_timeframe_data(
        self,
        position: Dict[str, Any],
        market_data: MarketData,
        timeframe_data: Dict[str, List[OHLCV]]
    ) -> Dict[str, Any]:
        """Prepare position data with multi-timeframe analysis for GPT"""

        # Calculate position metrics
        entry_price = position.get('entry_price', 0)
        current_price = market_data.mid_price
        position_type = position.get('order_type', 'BUY')

        # Calculate P&L and percentage
        if position_type.upper() in ['BUY', 'BUY_LIMIT']:
            pnl_pips = current_price - entry_price
            pnl_percent = ((current_price - entry_price) / entry_price) * 100
        else:
            pnl_pips = entry_price - current_price
            pnl_percent = ((entry_price - current_price) / entry_price) * 100

        analysis_data = {
            "position": {
                "symbol": position.get('symbol'),
                "ticket": position.get('ticket'),
                "type": position_type,
                "entry_price": entry_price,
                "current_price": current_price,
                "lot_size": position.get('lot_size'),
                "profit": position.get('profit', 0),
                "pnl_pips": pnl_pips,
                "pnl_percent": round(pnl_percent, 2),
                "stop_loss": position.get('stop_loss'),
                "take_profit": position.get('take_profit'),
                "open_time": position.get('open_time')
            },
            "market": {
                "current_price": current_price,
                "bid": market_data.bid,
                "ask": market_data.ask,
                "spread": market_data.spread
            },
            "timeframes": {}
        }

        # Process each timeframe data
        for timeframe, ohlcv_data in timeframe_data.items():
            if not ohlcv_data:
                continue

            # Get recent candles for this timeframe
            recent_candles = ohlcv_data[-10:] if len(ohlcv_data) >= 10 else ohlcv_data

            analysis_data["timeframes"][timeframe] = {
                "candles": [
                    {
                        "timestamp": candle.timestamp.isoformat(),
                        "open": candle.open,
                        "high": candle.high,
                        "low": candle.low,
                        "close": candle.close,
                        "volume": candle.volume
                    }
                    for candle in recent_candles
                ],
                "trend_direction": self._determine_trend_direction(recent_candles)
            }

        return analysis_data

    def _create_position_multi_timeframe_prompt(self, analysis_data: Dict[str, Any]) -> str:
        """Create multi-timeframe position analysis prompt for GPT"""

        position = analysis_data['position']
        market = analysis_data['market']

        prompt = f"""
POSITION TRAILING STOP ANALYSIS REQUEST:

CURRENT POSITION:
- Symbol: {position['symbol']}
- Ticket: {position['ticket']}
- Type: {position['type']}
- Entry Price: ${position['entry_price']:,.5f}
- Current Price: ${position['current_price']:,.5f}
- Lot Size: {position['lot_size']}
- Current P&L: ${position['profit']:,.2f}
- P&L Percentage: {position['pnl_percent']:.2f}%
- Stop Loss: {position['stop_loss']}
- Take Profit: {position['take_profit']}

MARKET DATA:
- Bid: ${market['bid']:,.5f}
- Ask: ${market['ask']:,.5f}
- Spread: {market['spread']}

MULTI-TIMEFRAME ANALYSIS (30m, 15m, 5m):
"""

        # Add timeframe analysis
        timeframe_order = ["30m", "15m", "5m"]
        for timeframe in timeframe_order:
            if timeframe not in analysis_data["timeframes"]:
                continue

            tf_data = analysis_data["timeframes"][timeframe]

            prompt += f"""
{timeframe.upper()} TIMEFRAME:
- Trend Direction: {tf_data['trend_direction']}
Recent {timeframe} Candles (Last 10):
"""

            # Add recent candles for this timeframe
            for i, candle in enumerate(tf_data['candles']):
                prompt += f"  {i+1}: O:{candle['open']} H:{candle['high']} L:{candle['low']} C:{candle['close']}\n"

        prompt += f"""

TRAILING STOP DECISION NEEDED:
This position is currently profitable ({position['pnl_percent']:.2f}%) and the system needs to decide:

ANALYSIS CRITERIA:
1. MOMENTUM CONTINUATION: Will the trend continue in favor of this position?
2. TIMEFRAME ALIGNMENT: Do 30m, 15m, and 5m timeframes support continuation?
3. PROFIT PROTECTION: Is it better to secure current profits or let it run?
4. MARKET STRUCTURE: Are there signs of exhaustion or reversal?

DECISION OPTIONS:
- CONTINUE (should_trail: false): Strong momentum continues, let position run to take profit
- TRAIL STOP (should_trail: true): Momentum weakening, secure current profits

Consider that this position is profitable and analyze whether the multi-timeframe momentum
supports continuation or suggests it's time to secure profits.

Provide your analysis in the specified JSON format with detailed reasoning.
"""

        return prompt
