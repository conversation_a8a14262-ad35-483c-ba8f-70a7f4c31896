#!/usr/bin/env python3
"""
Test Multi-Timeframe GPT Analysis
"""
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def test_multi_timeframe_analysis():
    """Test multi-timeframe GPT analysis"""
    print("🧠 Testing Multi-Timeframe GPT Analysis...")
    
    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.services.gpt_analyzer import GPTAnalyzer
        
        # Connect to MT5
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ Cannot connect to MT5")
            return False
        
        # Initialize GPT analyzer
        gpt_analyzer = GPTAnalyzer()
        
        # Test symbol
        symbol = "BTCUSDm"
        
        print(f"📊 Getting multi-timeframe data for {symbol}...")
        
        # Get multi-timeframe data
        timeframe_data = await connector.get_multi_timeframe_data(
            symbol, 
            timeframes=["15m", "30m", "1h", "4h"]
        )
        
        # Check if we got data for all timeframes
        for tf, data in timeframe_data.items():
            print(f"  {tf}: {len(data)} candles")
        
        if not any(timeframe_data.values()):
            print("❌ No timeframe data available")
            await connector.disconnect()
            return False
        
        # Get current market data
        market_data = await connector.get_market_data(symbol)
        if not market_data:
            print("❌ No market data available")
            await connector.disconnect()
            return False
        
        print(f"💰 Current price: ${market_data.mid_price:,.5f}")
        
        # Perform multi-timeframe GPT analysis
        print("🧠 Performing multi-timeframe GPT analysis...")
        
        analysis = await gpt_analyzer.analyze_market_multi_timeframe(
            symbol=symbol,
            market_data=market_data,
            timeframe_data=timeframe_data
        )
        
        if analysis:
            print("✅ Multi-timeframe GPT analysis completed!")
            print(f"📈 Signal: {analysis.signal}")
            print(f"🎯 Confidence: {analysis.confidence:.2f}")
            print(f"💡 Entry Price: ${analysis.entry_price:,.5f}")
            print(f"🛡️ Stop Loss: ${analysis.stop_loss:,.5f}")
            print(f"🎯 Take Profit: ${analysis.take_profit:,.5f}")
            print(f"⚠️ Risk Level: {analysis.risk_level}")
            print(f"📝 Reasoning: {analysis.reasoning}")
            print(f"📊 Technical Summary: {analysis.technical_summary}")
            print(f"💭 Market Sentiment: {analysis.market_sentiment}")
            
            # Check if timeframe analysis is included
            if hasattr(analysis, 'timeframe_analysis'):
                print("📊 Timeframe Analysis:")
                for tf, tf_analysis in analysis.timeframe_analysis.items():
                    print(f"  {tf}: {tf_analysis}")
            
            await connector.disconnect()
            return True
        else:
            print("❌ Multi-timeframe GPT analysis failed")
            await connector.disconnect()
            return False
            
    except Exception as e:
        print(f"❌ Multi-timeframe analysis error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def compare_single_vs_multi_timeframe():
    """Compare single timeframe vs multi-timeframe analysis"""
    print("\n🔄 Comparing Single vs Multi-Timeframe Analysis...")
    
    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.services.gpt_analyzer import GPTAnalyzer
        
        # Connect to MT5
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ Cannot connect to MT5")
            return False
        
        # Initialize GPT analyzer
        gpt_analyzer = GPTAnalyzer()
        symbol = "BTCUSDm"
        
        # Get market data
        market_data = await connector.get_market_data(symbol)
        if not market_data:
            print("❌ No market data available")
            await connector.disconnect()
            return False
        
        print(f"📊 Analyzing {symbol} at ${market_data.mid_price:,.5f}")
        
        # 1. Single timeframe analysis (old method)
        print("\n1️⃣ Single Timeframe Analysis (15m only):")
        ohlcv_data = await connector.get_ohlcv_data(symbol, "15m", 100)
        
        if ohlcv_data:
            single_analysis = await gpt_analyzer.analyze_market(
                symbol=symbol,
                market_data=market_data,
                ohlcv_data=ohlcv_data
            )
            
            if single_analysis:
                print(f"   Signal: {single_analysis.signal}")
                print(f"   Confidence: {single_analysis.confidence:.2f}")
                print(f"   Reasoning: {single_analysis.reasoning[:100]}...")
            else:
                print("   ❌ Single timeframe analysis failed")
        
        # 2. Multi-timeframe analysis (new method)
        print("\n2️⃣ Multi-Timeframe Analysis (15m, 30m, 1h, 4h):")
        timeframe_data = await connector.get_multi_timeframe_data(
            symbol, 
            timeframes=["15m", "30m", "1h", "4h"]
        )
        
        if any(timeframe_data.values()):
            multi_analysis = await gpt_analyzer.analyze_market_multi_timeframe(
                symbol=symbol,
                market_data=market_data,
                timeframe_data=timeframe_data
            )
            
            if multi_analysis:
                print(f"   Signal: {multi_analysis.signal}")
                print(f"   Confidence: {multi_analysis.confidence:.2f}")
                print(f"   Reasoning: {multi_analysis.reasoning[:100]}...")
            else:
                print("   ❌ Multi-timeframe analysis failed")
        
        await connector.disconnect()
        return True
        
    except Exception as e:
        print(f"❌ Comparison error: {e}")
        return False

async def main():
    """Main test function"""
    print("🚀 Multi-Timeframe GPT Analysis Test")
    print("=" * 50)
    
    # Test 1: Multi-timeframe analysis
    success1 = await test_multi_timeframe_analysis()
    
    # Test 2: Compare methods
    success2 = await compare_single_vs_multi_timeframe()
    
    print("\n" + "=" * 50)
    if success1 and success2:
        print("✅ All multi-timeframe tests passed!")
    else:
        print("❌ Some tests failed")

if __name__ == "__main__":
    asyncio.run(main())
