"""
Risk Management Utilities
Handles position sizing, risk calculation, and portfolio management
"""
from typing import List, Optional, Dict, Tuple
from datetime import datetime, timedelta
from loguru import logger

from ..models.trading_models import (
    Position, AccountInfo, OrderRequest, RiskManagement,
    OrderType, PositionStatus, MarketData
)
from ..config import settings, SYMBOL_CONFIG


class RiskManager:
    """Risk management and position sizing calculator"""

    def __init__(self, risk_params: Optional[RiskManagement] = None):
        # Use settings values if no custom risk_params provided
        if risk_params is None:
            self.risk_params = RiskManagement(
                max_risk_percent=settings.max_risk_percent,
                max_drawdown_percent=settings.max_drawdown_percent,
                max_daily_loss=settings.max_daily_loss,
                max_open_positions=settings.max_open_positions,
                stop_loss_pips=settings.stop_loss_pips,
                take_profit_pips=settings.take_profit_pips,
                trailing_stop_pips=getattr(settings, 'trailing_stop_pips', 20)
            )
        else:
            self.risk_params = risk_params

    def calculate_position_size(
        self,
        symbol: str,
        account_info: AccountInfo,
        entry_price: float,
        stop_loss: float,
        risk_percent: Optional[float] = None
    ) -> float:
        """Calculate optimal position size based on risk management"""
        try:
            risk_percent = risk_percent or self.risk_params.max_risk_percent

            # Get symbol configuration
            symbol_config = SYMBOL_CONFIG.get(symbol, {})
            pip_value = symbol_config.get('pip_value', 0.0001)
            min_lot = symbol_config.get('min_lot', 0.01)
            max_lot = symbol_config.get('max_lot', 100.0)
            lot_step = symbol_config.get('lot_step', 0.01)

            # Calculate risk amount in account currency
            risk_amount = account_info.balance * (risk_percent / 100)

            # Calculate stop loss distance in pips
            stop_loss_distance = abs(entry_price - stop_loss)
            stop_loss_pips = stop_loss_distance / pip_value

            if stop_loss_pips == 0:
                return min_lot

            # Calculate position size
            # Risk Amount = Position Size * Stop Loss Pips * Pip Value * Contract Size

            if symbol.startswith('BTC'):
                # Bitcoin: 1 lot = 1 BTC, high value
                contract_size = 1
                pip_value_per_lot = pip_value  # 1.0
            elif symbol.startswith('ETH'):
                # Ethereum: 1 lot = 1 ETH, medium value
                contract_size = 1
                pip_value_per_lot = pip_value  # 0.1
            elif symbol.startswith('XAU'):
                # Gold: 1 lot = 100 oz
                contract_size = 100
                pip_value_per_lot = pip_value * contract_size
            elif symbol.endswith('USD') and len(symbol) == 6:
                # Forex pairs: 1 lot = 100,000 units
                contract_size = 100000
                pip_value_per_lot = pip_value * contract_size
            else:
                # Default: treat as crypto/CFD
                contract_size = 1
                pip_value_per_lot = pip_value

            # Calculate position size with safety check
            if stop_loss_pips > 0 and pip_value_per_lot > 0:
                position_size = risk_amount / (stop_loss_pips * pip_value_per_lot)
            else:
                logger.warning(f"Invalid calculation parameters for {symbol}: stop_loss_pips={stop_loss_pips}, pip_value_per_lot={pip_value_per_lot}")
                return min_lot

            # Round to lot step
            position_size = round(position_size / lot_step) * lot_step

            # Apply limits
            position_size = max(min_lot, min(position_size, max_lot))

            logger.info(f"Position size calculated for {symbol}: {position_size} lots (risk: {risk_amount:.2f}, stop_loss_pips: {stop_loss_pips:.2f})")
            return position_size

        except Exception as e:
            logger.error(f"Position size calculation failed for {symbol}: {e}")
            logger.error(f"  Account balance: {account_info.balance if account_info else 'N/A'}")
            logger.error(f"  Entry price: {entry_price}")
            logger.error(f"  Stop loss: {stop_loss}")
            logger.error(f"  Risk percent: {risk_percent}")
            return settings.default_lot_size

    def validate_order(
        self,
        order_request: OrderRequest,
        account_info: AccountInfo,
        current_positions: List[Position],
        market_data: MarketData
    ) -> Tuple[bool, str]:
        """Validate order against risk management rules"""
        try:
            logger.info(f"🔍 Validating order for {order_request.symbol}:")
            logger.info(f"   Order Type: {order_request.order_type}")
            logger.info(f"   Lot Size: {order_request.lot_size}")
            logger.info(f"   Price: {order_request.price}")

            # Check if trading is enabled
            if not self._is_trading_allowed():
                logger.warning(f"❌ Trading time restriction for {order_request.symbol}")
                return False, "Trading is currently disabled"

            # Check maximum open positions
            open_positions = [p for p in current_positions if p.status == PositionStatus.OPEN]
            logger.info(f"   Open Positions: {len(open_positions)}/{self.risk_params.max_open_positions}")
            if len(open_positions) >= self.risk_params.max_open_positions:
                return False, f"Maximum open positions ({self.risk_params.max_open_positions}) reached"

            # Check daily loss limit
            daily_loss = self._calculate_daily_loss(current_positions)
            logger.info(f"   Daily Loss: ${daily_loss:.2f}/${self.risk_params.max_daily_loss:.2f}")
            if daily_loss >= self.risk_params.max_daily_loss:
                return False, f"Daily loss limit ({self.risk_params.max_daily_loss}) reached"

            # Check account drawdown
            drawdown_percent = self._calculate_drawdown_percent(account_info, current_positions)
            logger.info(f"   Drawdown: {drawdown_percent:.2f}%/{self.risk_params.max_drawdown_percent:.2f}%")
            if drawdown_percent >= self.risk_params.max_drawdown_percent:
                return False, f"Maximum drawdown ({self.risk_params.max_drawdown_percent}%) reached"

            # Check margin requirements
            logger.info(f"   Free Margin: ${account_info.free_margin:.2f}")
            if not self._check_margin_requirements(order_request, account_info):
                return False, "Insufficient margin for order"

            # Check spread conditions
            spread_pips = market_data.spread / SYMBOL_CONFIG.get(order_request.symbol, {}).get('pip_value', 0.0001)
            logger.info(f"   Spread: {spread_pips:.1f} pips")
            if not self._check_spread_conditions(order_request.symbol, market_data):
                return False, "Spread too wide for trading"

            # Check position size limits
            symbol_config = SYMBOL_CONFIG.get(order_request.symbol, {})
            min_lot = symbol_config.get('min_lot', 0.01)
            max_lot = symbol_config.get('max_lot', 100.0)
            logger.info(f"   Lot Size Limits: {min_lot} - {max_lot}")
            if not self._check_position_size_limits(order_request):
                return False, "Position size outside allowed limits"

            logger.info(f"✅ Order validation passed for {order_request.symbol}")
            return True, "Order validation passed"

        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return False, f"Validation error: {e}"

    def calculate_stop_loss_take_profit(
        self,
        symbol: str,
        entry_price: float,
        order_type: OrderType,
        risk_reward_ratio: float = 2.0
    ) -> Tuple[Optional[float], Optional[float]]:
        """
        Calculate stop loss and take profit levels as FALLBACK
        NOTE: This is only used when GPT doesn't provide stop levels.
        Primary stop levels should come from GPT analysis.
        """
        try:
            symbol_config = SYMBOL_CONFIG.get(symbol, {})
            pip_value = symbol_config.get('pip_value', 0.0001)

            # Symbol-specific stop loss and take profit distances
            if symbol.startswith('BTC'):
                # Bitcoin needs larger stops due to volatility
                stop_loss_pips = 500   # 500 points = $500 for BTCUSDm
                take_profit_pips = 1000 # 1000 points = $1000 for BTCUSDm
            elif symbol.startswith('ETH'):
                # Ethereum moderate stops
                stop_loss_pips = 200   # 200 points = $20 for ETHUSDm (pip_value=0.1)
                take_profit_pips = 400 # 400 points = $40 for ETHUSDm
            else:
                # Use default for other symbols
                stop_loss_pips = self.risk_params.stop_loss_pips
                take_profit_pips = self.risk_params.take_profit_pips

            # Convert pips to price
            stop_loss_distance = stop_loss_pips * pip_value
            take_profit_distance = take_profit_pips * pip_value

            if order_type in [OrderType.BUY, OrderType.BUY_LIMIT, OrderType.BUY_STOP]:
                # Long position
                stop_loss = entry_price - stop_loss_distance
                take_profit = entry_price + take_profit_distance
            else:
                # Short position
                stop_loss = entry_price + stop_loss_distance
                take_profit = entry_price - take_profit_distance

            logger.info(f"SL/TP calculated for {symbol}: SL={stop_loss:.2f}, TP={take_profit:.2f} (distance: {stop_loss_distance:.2f})")
            return stop_loss, take_profit

        except Exception as e:
            logger.error(f"Error calculating SL/TP for {symbol}: {e}")
            return None, None

    async def should_close_position(
        self,
        position: Position,
        market_data: MarketData,
        account_info: AccountInfo,
        gpt_analyzer=None,
        mt5_connector=None
    ) -> Tuple[bool, str]:
        """Determine if position should be closed based on risk rules"""
        try:
            # Check if position is profitable enough to trail stop
            if position.profit and position.profit > 0:
                # Check if GPT multi-timeframe analysis is available for intelligent trailing stop
                if gpt_analyzer and mt5_connector:
                    should_trail = await self._should_trail_stop_with_gpt_multi_timeframe(
                        position, market_data, gpt_analyzer, mt5_connector
                    )
                    if should_trail:
                        return True, "GPT multi-timeframe analysis recommends trailing stop"
                else:
                    # Fallback to traditional trailing stop logic
                    if self._should_trail_stop(position, market_data):
                        return True, "Traditional trailing stop triggered"

            # Check maximum loss per position
            if position.profit and position.profit < -self.risk_params.max_daily_loss * 0.2:
                return True, "Maximum position loss reached"

            # Check time-based exit (if position is open too long)
            if self._is_position_too_old(position):
                return True, "Position held too long"

            return False, "Position within risk parameters"

        except Exception as e:
            logger.error(f"Error checking position close conditions: {e}")
            return False, f"Error: {e}"

    def _is_trading_allowed(self) -> bool:
        """Check if trading is currently allowed"""
        # Add time-based restrictions, market hours, etc.
        from datetime import datetime
        current_hour = datetime.now().hour

        # More flexible trading hours - only avoid extreme low liquidity periods
        # Crypto markets are 24/7, so we'll be more permissive
        # Only block during server maintenance hours (typically Sunday 22:00-23:00 UTC)
        current_day = datetime.now().weekday()  # 0=Monday, 6=Sunday

        # Only restrict on Sunday evening (server maintenance)
        if current_day == 6 and current_hour in [22, 23]:  # Sunday 22-23 UTC
            logger.info(f"Trading disabled during server maintenance: Sunday {current_hour}:00 UTC")
            return False

        # Allow trading 24/7 for crypto and other markets
        logger.debug(f"Trading allowed at {current_hour}:00 UTC on day {current_day}")
        return True

    def _calculate_daily_loss(self, positions: List[Position]) -> float:
        """Calculate total loss for today"""
        today = datetime.now().date()
        daily_loss = 0.0

        for position in positions:
            if position.close_time and position.close_time.date() == today:
                if position.profit and position.profit < 0:
                    daily_loss += abs(position.profit)

        return daily_loss

    def _calculate_drawdown_percent(
        self,
        account_info: AccountInfo,
        positions: List[Position]
    ) -> float:
        """Calculate current drawdown percentage"""
        try:
            # Calculate unrealized P&L from open positions
            unrealized_pnl = sum(
                position.profit or 0
                for position in positions
                if position.status == PositionStatus.OPEN
            )

            # Current equity including unrealized P&L
            current_equity = account_info.equity

            # Use the higher of balance or equity as peak equity
            # This prevents false drawdown alerts when account is profitable
            peak_equity = max(account_info.balance, account_info.equity)

            # If we have unrealized losses, use balance as peak
            if unrealized_pnl < 0:
                peak_equity = account_info.balance

            if peak_equity <= 0:
                return 0.0

            # Calculate drawdown based on current equity vs peak
            drawdown = ((peak_equity - current_equity) / peak_equity) * 100

            # Only consider it drawdown if we're actually losing money
            if current_equity >= peak_equity:
                return 0.0

            return max(0.0, drawdown)

        except Exception as e:
            logger.error(f"Error calculating drawdown: {e}")
            return 0.0

    def _check_margin_requirements(
        self,
        order_request: OrderRequest,
        account_info: AccountInfo
    ) -> bool:
        """Check if there's sufficient margin for the order"""
        try:
            # Improved margin calculation based on symbol type
            symbol = order_request.symbol
            lot_size = order_request.lot_size

            # Get symbol configuration
            symbol_config = SYMBOL_CONFIG.get(symbol, {})

            # Calculate required margin based on symbol type
            # Using more realistic leverage ratios
            if symbol.startswith('BTC'):
                # Bitcoin: Use 10:1 leverage (typical for crypto)
                # 0.01 BTC ≈ $1,085, margin ≈ $108.50
                required_margin = lot_size * 100  # 1000:1 leverage
            elif symbol.startswith('ETH'):
                # Ethereum: Use 10:1 leverage
                # 0.01 ETH ≈ $40, margin ≈ $4
                required_margin = lot_size * 40    # 100:1 leverage
            elif symbol.startswith('XAU'):
                # Gold: Use 20:1 leverage (typical for metals)
                required_margin = lot_size * 100    # 20:1 leverage for 100oz
            elif symbol.endswith('USD') and len(symbol) == 6:
                # Forex pairs: Use 100:1 leverage (standard)
                required_margin = lot_size * 1000   # 100:1 leverage
            else:
                # Default calculation
                required_margin = lot_size * 1000

            # Add safety buffer (10% instead of 20%)
            required_margin *= 1.1

            # Check if we have sufficient free margin
            has_margin = account_info.free_margin > required_margin

            if not has_margin:
                logger.warning(f"Insufficient margin for {symbol}: Required {required_margin:.2f}, Available {account_info.free_margin:.2f}")

            return has_margin

        except Exception as e:
            logger.error(f"Error checking margin requirements: {e}")
            return False  # Conservative approach

    def _check_spread_conditions(self, symbol: str, market_data: MarketData) -> bool:
        """Check if spread is acceptable for trading"""
        try:
            symbol_config = SYMBOL_CONFIG.get(symbol, {})
            max_spread = symbol_config.get('spread_threshold', 5.0)

            # Convert spread to pips
            pip_value = symbol_config.get('pip_value', 0.0001)
            spread_pips = market_data.spread / pip_value

            return spread_pips <= max_spread

        except Exception:
            return True  # Allow trading if can't check spread

    def _check_position_size_limits(self, order_request: OrderRequest) -> bool:
        """Check if position size is within limits"""
        try:
            symbol_config = SYMBOL_CONFIG.get(order_request.symbol, {})
            min_lot = symbol_config.get('min_lot', 0.01)
            max_lot = symbol_config.get('max_lot', 100.0)

            return min_lot <= order_request.lot_size <= max_lot

        except Exception:
            return True

    def _should_trail_stop(self, position: Position, market_data: MarketData) -> bool:
        """Check if trailing stop should be triggered"""
        try:
            if not position.stop_loss or not position.profit:
                return False

            # Implement trailing stop logic
            trailing_distance = self.risk_params.trailing_stop_pips
            symbol_config = SYMBOL_CONFIG.get(position.symbol, {})
            pip_value = symbol_config.get('pip_value', 0.0001)

            trailing_distance_price = trailing_distance * pip_value

            if position.order_type in [OrderType.BUY, OrderType.BUY_LIMIT]:
                # Long position - trail stop up
                new_stop = market_data.bid - trailing_distance_price
                return new_stop > position.stop_loss
            else:
                # Short position - trail stop down
                new_stop = market_data.ask + trailing_distance_price
                return new_stop < position.stop_loss

        except Exception:
            return False

    def _is_position_too_old(self, position: Position, max_hours: int = 24) -> bool:
        """Check if position has been open too long"""
        try:
            if not position.open_time:
                return False

            time_open = datetime.now() - position.open_time
            return time_open > timedelta(hours=max_hours)

        except Exception:
            return False

    async def _should_trail_stop_with_gpt(
        self,
        position: Position,
        market_data: MarketData,
        gpt_analyzer,
        ohlcv_data
    ) -> bool:
        """Use GPT analysis to determine if trailing stop should be executed"""
        try:
            # Convert position to dict for GPT analysis
            position_dict = {
                'symbol': position.symbol,
                'ticket': position.ticket,
                'order_type': position.order_type.value if hasattr(position.order_type, 'value') else str(position.order_type),
                'entry_price': position.entry_price,
                'lot_size': position.lot_size,
                'profit': position.profit,
                'stop_loss': position.stop_loss,
                'take_profit': position.take_profit,
                'open_time': position.open_time
            }

            # Get GPT analysis for this position
            logger.info(f"🧠 Requesting GPT analysis for trailing stop decision on {position.symbol}")

            gpt_result = await gpt_analyzer.analyze_position_for_trailing_stop(
                position_dict, market_data, ohlcv_data
            )

            should_trail = gpt_result.get('should_trail', False)
            reasoning = gpt_result.get('reasoning', 'No reasoning provided')
            confidence = gpt_result.get('confidence', 0.0)

            logger.info(f"🧠 GPT trailing stop decision for {position.symbol}:")
            logger.info(f"   Decision: {'TRAIL STOP' if should_trail else 'CONTINUE'}")
            logger.info(f"   Confidence: {confidence:.2f}")
            logger.info(f"   Reasoning: {reasoning}")

            # Only execute trailing stop if GPT is confident (>0.6) and recommends it
            if should_trail and confidence > 0.6:
                logger.info(f"🎯 GPT recommends trailing stop for {position.symbol} with high confidence ({confidence:.2f})")
                return True
            elif should_trail and confidence <= 0.6:
                logger.info(f"⚠️ GPT recommends trailing stop for {position.symbol} but confidence is low ({confidence:.2f}), continuing position")
                return False
            else:
                logger.info(f"✅ GPT recommends continuing position {position.symbol} for more profit potential")
                return False

        except Exception as e:
            logger.error(f"Error in GPT trailing stop analysis: {e}")
            # Fallback to traditional trailing stop if GPT analysis fails
            logger.info(f"🔄 Falling back to traditional trailing stop for {position.symbol}")
            return self._should_trail_stop(position, market_data)

    async def _should_trail_stop_with_gpt_multi_timeframe(
        self,
        position: Position,
        market_data: MarketData,
        gpt_analyzer,
        mt5_connector
    ) -> bool:
        """Use GPT multi-timeframe analysis (30m, 15m, 5m) to determine trailing stop"""
        try:
            # Convert position to dict for GPT analysis
            position_dict = {
                'symbol': position.symbol,
                'ticket': position.ticket,
                'order_type': position.order_type.value if hasattr(position.order_type, 'value') else str(position.order_type),
                'entry_price': position.entry_price,
                'lot_size': position.lot_size,
                'profit': position.profit,
                'stop_loss': position.stop_loss,
                'take_profit': position.take_profit,
                'open_time': position.open_time
            }

            # Get GPT multi-timeframe analysis for this position
            logger.info(f"🧠 Requesting GPT multi-timeframe analysis for trailing stop decision on {position.symbol}")

            gpt_result = await gpt_analyzer.analyze_position_for_trailing_stop(
                position_dict, market_data, mt5_connector
            )

            should_trail = gpt_result.get('should_trail', False)
            reasoning = gpt_result.get('reasoning', 'No reasoning provided')
            confidence = gpt_result.get('confidence', 0.0)
            market_outlook = gpt_result.get('market_outlook', 'neutral')

            logger.info(f"🧠 GPT Multi-Timeframe Trailing Stop Analysis for {position.symbol}:")
            logger.info(f"   Decision: {'TRAIL STOP' if should_trail else 'CONTINUE'}")
            logger.info(f"   Confidence: {confidence:.2f}")
            logger.info(f"   Market Outlook: {market_outlook}")
            logger.info(f"   Reasoning: {reasoning}")

            # Only execute trailing stop if GPT is confident (>0.6) and recommends it
            if should_trail and confidence > 0.6:
                logger.info(f"🎯 GPT recommends trailing stop for {position.symbol} with high confidence ({confidence:.2f})")
                return True
            elif should_trail and confidence <= 0.6:
                logger.info(f"⚠️ GPT recommends trailing stop for {position.symbol} but confidence is low ({confidence:.2f}), continuing position")
                return False
            else:
                logger.info(f"✅ GPT recommends continuing position {position.symbol} for more profit potential")
                return False

        except Exception as e:
            logger.error(f"Error in GPT multi-timeframe trailing stop analysis: {e}")
            # Fallback to traditional trailing stop if GPT analysis fails
            logger.info(f"🔄 Falling back to traditional trailing stop for {position.symbol}")
            return self._should_trail_stop(position, market_data)