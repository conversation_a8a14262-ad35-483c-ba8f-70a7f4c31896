# 🚀 SYSTEM STARTUP GUIDE

## 📋 OVERVIEW

Sistem trading ini sekarang mendukung **2 mode operasi**:

1. **🎯 SCALPING SYSTEM** - Sistem scalping baru dengan GPT-4o validator
2. **🚀 ORIGINAL SYSTEM** - Sistem trading original dengan GPT-4 analysis

## 🎯 **SCALPING SYSTEM** (RECOMMENDED)

### **Cara Start:**

#### **Opsi 1: Menggunakan Start Script (Recommended)**
```bash
python start_scalping.py
```

#### **Opsi 2: Menggunakan Environment Variable**
```bash
# Set environment variable
export SCALPING_MODE=true

# Start system
python -m backend.main
```

#### **Opsi 3: Menggunakan Dedicated Main**
```bash
python scalping_main.py
```

### **Features Scalping System:**
- ✅ **GPT-4o Validator** - Struktur candle validation (bukan prediktor)
- ✅ **Pending Orders** - BUY/SELL STOP/LIMIT dengan buffer zones
- ✅ **Real-time Monitoring** - Price movement triggers & cooldown
- ✅ **Auto Re-evaluation** - Buffer zone analysis & order validation
- ✅ **Post-trade Evaluation** - Learning insights & pattern recognition
- ✅ **Symbol-specific Thresholds** - BTCUSDm, ETHUSDm, EURUSD, XAUUSD

### **API Endpoints:**
```
Status:     GET  http://localhost:8001/api/scalping/status
Analyze:    POST http://localhost:8001/api/scalping/analyze/{symbol}
Orders:     GET  http://localhost:8001/api/scalping/orders
Cancel:     DEL  http://localhost:8001/api/scalping/orders/{order_id}
Health:     GET  http://localhost:8001/api/scalping/health
```

---

## 🚀 **ORIGINAL SYSTEM**

### **Cara Start:**

#### **Opsi 1: Menggunakan Start Script**
```bash
python start_original.py
```

#### **Opsi 2: Menggunakan Backend Main (Default)**
```bash
python -m backend.main
```

#### **Opsi 3: Explicit Environment Variable**
```bash
# Set environment variable
export SCALPING_MODE=false

# Start system
python -m backend.main
```

### **Features Original System:**
- ✅ **GPT-4 Analysis** - Multi-timeframe decision making
- ✅ **Position Management** - Automatic trailing stops
- ✅ **WebSocket Updates** - Real-time P&L & position updates
- ✅ **Risk Management** - Advanced account protection
- ✅ **Technical Analysis** - Multiple indicators & timeframes

### **API Endpoints:**
```
Status:     GET  http://localhost:8001/api/trading/status
Positions:  GET  http://localhost:8001/api/trading/positions
Analyze:    POST http://localhost:8001/api/trading/analyze
WebSocket:  ws://localhost:8001/ws
```

---

## 🔧 **QUICK COMPARISON**

| Feature | Scalping System | Original System |
|---------|----------------|-----------------|
| **GPT Model** | GPT-4o | GPT-4 |
| **Strategy** | Scalping + Pending Orders | Position Trading |
| **Order Type** | BUY/SELL STOP/LIMIT | Market Orders |
| **Re-evaluation** | Buffer Zone Analysis | Trailing Stop Analysis |
| **Timeframe** | M15 (30 candles) | Multi-timeframe |
| **Evaluation** | Post-trade Learning | Real-time Analysis |
| **WebSocket** | ❌ | ✅ |
| **Frontend** | API Only | Full Frontend |

---

## 📊 **TESTING SYSTEMS**

### **Test Scalping System:**
```bash
python test_scalping_system.py
```

### **Test Original System:**
```bash
# Start original system first, then test via API
curl http://localhost:8001/api/trading/status
```

---

## 🎯 **RECOMMENDED USAGE**

### **For Scalping Trading:**
```bash
# 1. Test system
python test_scalping_system.py

# 2. Start scalping system
python start_scalping.py

# 3. Monitor via API
curl http://localhost:8001/api/scalping/status
```

### **For Position Trading:**
```bash
# 1. Start original system
python start_original.py

# 2. Access frontend
# Open browser: http://localhost:8000

# 3. Monitor via WebSocket
# Connect to: ws://localhost:8001/ws
```

---

## 🔄 **SWITCHING BETWEEN SYSTEMS**

### **From Original to Scalping:**
1. Stop original system (Ctrl+C)
2. Start scalping system: `python start_scalping.py`

### **From Scalping to Original:**
1. Stop scalping system (Ctrl+C)
2. Start original system: `python start_original.py`

---

## 📂 **LOG FILES**

### **Scalping System:**
```
logs/scalping_system.log       # Main system logs
logs/pending_orders.json       # Active pending orders
logs/expired_orders.json       # Cancelled/expired orders
logs/evaluations/              # Trade evaluations
```

### **Original System:**
```
logs/trading.log               # Main system logs
logs/positions.log             # Position management
logs/analysis.log              # GPT analysis logs
```

---

## ⚙️ **ENVIRONMENT VARIABLES**

### **Required for Both Systems:**
```env
OPENAI_API_KEY=your_openai_api_key
MT5_LOGIN=your_mt5_login
MT5_PASSWORD=your_mt5_password
MT5_SERVER=your_mt5_server
```

### **System Mode Control:**
```env
SCALPING_MODE=true    # For scalping system
SCALPING_MODE=false   # For original system (default)
```

---

## 🚨 **TROUBLESHOOTING**

### **Common Issues:**

#### **1. Port Already in Use:**
```bash
# Check what's using port 8001
netstat -tulpn | grep 8001

# Kill process if needed
sudo kill -9 <PID>
```

#### **2. MT5 Connection Failed:**
- Ensure MT5 is running
- Check login credentials in .env
- Verify server name

#### **3. OpenAI API Error:**
- Check API key validity
- Verify account has credits
- Check rate limits

#### **4. Import Errors:**
```bash
# Install missing dependencies
pip install -r requirements.txt

# Or install individually
pip install fastapi uvicorn openai MetaTrader5 numpy pandas loguru
```

---

## 📈 **MONITORING & MAINTENANCE**

### **Health Checks:**
```bash
# Scalping system
curl http://localhost:8001/api/scalping/health

# Original system
curl http://localhost:8001/api/health
```

### **Log Monitoring:**
```bash
# Real-time log monitoring
tail -f logs/scalping_system.log    # For scalping
tail -f logs/trading.log            # For original
```

### **System Status:**
```bash
# Check system status
curl http://localhost:8001/api/scalping/status  # Scalping
curl http://localhost:8001/api/trading/status   # Original
```

---

## 🎉 **SUMMARY**

### **Start Scalping System:**
```bash
python start_scalping.py
```

### **Start Original System:**
```bash
python start_original.py
```

### **Default (Original) System:**
```bash
python -m backend.main
```

**Choose the system that best fits your trading strategy!** 🎯
