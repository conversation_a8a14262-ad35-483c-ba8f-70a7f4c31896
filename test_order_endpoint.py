#!/usr/bin/env python3
"""
Test the new test-order endpoint
"""
import requests
import time

def test_order_endpoint():
    """Test the new test-order endpoint"""
    print("🔧 === TESTING ORDER ENDPOINT ===")
    
    # Start trading engine first
    print("\n1. Starting trading engine...")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Trading engine started")
        else:
            print(f"❌ Failed: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Wait a bit for engine to initialize
    time.sleep(3)
    
    # Test the new endpoint
    print("\n2. Testing order placement endpoint...")
    print("   ⚠️  This will attempt to place a REAL order with BTCUSDm!")
    print("   📊 Order details:")
    print("   - Symbol: BTCUSDm (cheapest available)")
    print("   - Type: BUY")
    print("   - Lot Size: 0.01")
    print("   - Comment: Test")
    print("   - Deviation: 100 points")
    
    try:
        response = requests.post("http://localhost:8001/api/trading/test-order", timeout=15)
        print(f"\n   Status: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Test order endpoint response:")
            print(f"   Message: {result.get('message')}")
            print(f"   Ticket: {result.get('ticket')}")
            print(f"   Symbol: {result.get('symbol')}")
            print(f"   Lot Size: {result.get('lot_size')}")
            print(f"   Price: {result.get('price')}")
            
            if result.get('ticket'):
                print("\n🎉 ORDER PLACED SUCCESSFULLY!")
                print("   Check MT5 terminal for the order")
                
                # Get positions to confirm
                print("\n3. Checking positions...")
                pos_response = requests.get("http://localhost:8001/api/trading/positions", timeout=5)
                if pos_response.status_code == 200:
                    positions = pos_response.json()
                    print(f"   Current positions: {len(positions)}")
                    for pos in positions:
                        print(f"   - {pos.get('symbol')} {pos.get('order_type')} {pos.get('lot_size')} lots")
                
                return True
            else:
                print("\n❌ ORDER FAILED")
                print(f"   Error: {result.get('error', 'Unknown error')}")
                return False
                
        else:
            print(f"❌ Endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing endpoint: {e}")
        return False

def monitor_backend_logs():
    """Instructions for monitoring backend logs"""
    print("\n📋 === MONITORING BACKEND LOGS ===")
    print("\n🔍 Watch for these log entries:")
    print("1. '🧪 Testing order placement for BTCUSDm'")
    print("2. 'Order details for BTCUSDm: lot_size=0.01, price=...'")
    print("3. 'Sending order request for BTCUSDm: {...}'")
    print("4. Either:")
    print("   ✅ 'Order placed successfully for BTCUSDm: Ticket XXXXX'")
    print("   ❌ 'Order send failed: MT5 returned None result'")
    print("   ❌ 'MT5 last error: (...)'")
    
    print("\n💡 If order fails, check:")
    print("- MT5 terminal for error messages")
    print("- Account permissions for BTCUSDm trading")
    print("- Market hours for BTCUSDm")
    print("- Minimum lot size requirements")
    print("- Broker-specific restrictions")

if __name__ == "__main__":
    print("🎯 Testing Direct Order Placement Endpoint")
    print("=" * 60)
    print("This will test actual order placement with MT5")
    print("Using BTCUSDm symbol (most affordable)")
    print("=" * 60)
    
    success = test_order_endpoint()
    monitor_backend_logs()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 ORDER PLACEMENT TEST SUCCESSFUL!")
        print("\n📊 Results:")
        print("✅ Order endpoint working")
        print("✅ MT5 order placement functional")
        print("✅ System ready for live trading")
        
        print("\n⚠️  IMPORTANT:")
        print("- A real order was placed in your MT5 account")
        print("- Check MT5 terminal to manage the position")
        print("- Consider closing the test position")
        print("- Monitor for any unexpected behavior")
    else:
        print("❌ ORDER PLACEMENT TEST FAILED")
        print("\n🔍 Troubleshooting:")
        print("- Check backend logs for detailed error messages")
        print("- Verify MT5 connection and permissions")
        print("- Ensure BTCUSDm is tradeable on your broker")
        print("- Check account balance and margin requirements")
        print("- Verify market is open for BTCUSDm trading")
    
    print(f"\n🌐 Monitor backend terminal for detailed order placement logs")
