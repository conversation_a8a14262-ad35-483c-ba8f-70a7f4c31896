import React, { useState, useEffect, useCallback } from 'react';
import {
  RefreshCw,
  X,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import { tradingApi, formatCurrency, formatDateTime, wsService } from '../services/api';

const PositionManager = () => {
  const [positions, setPositions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [closingPosition, setClosingPosition] = useState(null);
  const [filter, setFilter] = useState('all'); // all, open, closed
  const [wsConnected, setWsConnected] = useState(false);
  const [lastUpdate, setLastUpdate] = useState(null);
  const [forceRender, setForceRender] = useState(0);

  useEffect(() => {
    console.log('🔧 PositionManager: Initializing WebSocket listeners');
    loadPositions();

    // Connect WebSocket if not connected
    if (!wsService.isConnected()) {
      console.log('🔌 PositionManager: Connecting to WebSocket...');
      wsService.connect();
    } else {
      console.log('✅ PositionManager: WebSocket already connected');
    }

    // Define event handlers
    const onConnected = () => {
      console.log('✅ PositionManager: WebSocket connected');
      setWsConnected(true);
    };

    const onDisconnected = () => {
      console.log('❌ PositionManager: WebSocket disconnected');
      setWsConnected(false);
    };

    // Listen for real-time position updates
    wsService.addListener('position_update', handlePositionUpdate);
    wsService.addListener('position_change', handlePositionChange);
    wsService.addListener('connected', onConnected);
    wsService.addListener('disconnected', onDisconnected);

    // Set initial connection status
    const initialStatus = wsService.isConnected();
    console.log('🔍 PositionManager: Initial WebSocket status:', initialStatus);
    setWsConnected(initialStatus);

    // Reduce auto-refresh to 30 seconds since we have real-time updates
    const interval = setInterval(() => {
      console.log('🔄 PositionManager: Auto-refresh positions (fallback)');
      loadPositions();
    }, 30000);

    return () => {
      console.log('🧹 PositionManager: Cleaning up WebSocket listeners');
      wsService.removeListener('position_update', handlePositionUpdate);
      wsService.removeListener('position_change', handlePositionChange);
      wsService.removeListener('connected', onConnected);
      wsService.removeListener('disconnected', onDisconnected);
      clearInterval(interval);
    };
  }, []);

  // Debug effect to track position state changes
  useEffect(() => {
    console.log('🔄 REACT STATE CHANGED - Positions updated:', {
      count: positions.length,
      timestamp: new Date().toLocaleTimeString(),
      positions: positions.map(p => ({
        symbol: p.symbol,
        profit: p.profit,
        current_price: p.current_price,
        ticket: p.ticket
      }))
    });
  }, [positions]);

  // Debug effect to track force render changes
  useEffect(() => {
    console.log('🎨 Force render changed:', forceRender);
  }, [forceRender]);

  const handlePositionUpdate = useCallback((data) => {
    console.log('📊 Real-time position update received:', data);
    console.log('📊 Message type:', data.type);
    console.log('📊 Raw data:', data);

    // Handle both data.positions (old format) and direct positions array (new format)
    const positions = data.positions || data.data?.positions || [];
    console.log('📊 Extracted positions:', positions);
    console.log('📊 Number of positions:', positions?.length || 0);

    if (positions && Array.isArray(positions)) {
      console.log('✅ Updating positions state with real-time data');

      // Force React to re-render using functional updates
      setPositions(prevPositions => {
        console.log('🔄 Previous positions:', prevPositions.length);
        console.log('🔄 New positions:', positions.length);
        return [...positions];
      });

      setLastUpdate(prev => {
        const newDate = new Date();
        console.log('🕒 Last update changed:', newDate.toLocaleTimeString());
        return newDate;
      });

      setForceRender(prev => {
        const newCount = prev + 1;
        console.log('🎨 Force render incremented to:', newCount);
        return newCount;
      });

      console.log('🔄 All state updates triggered');

      // Log P&L details for debugging
      positions.forEach((pos, index) => {
        console.log(`📊 Position ${index + 1}:`, {
          symbol: pos.symbol,
          entry_price: pos.entry_price,
          current_price: pos.current_price,
          profit: pos.profit,
          order_type: pos.order_type,
          ticket: pos.ticket
        });
      });
    } else {
      console.log('⚠️ No valid positions data in update');
    }
  }, []);

  const handlePositionChange = useCallback((data) => {
    console.log('🔄 Position change received:', data);
    if (data.action === 'closed' && data.position) {
      // Remove closed position from list or reload
      loadPositions();
    } else if (data.action === 'opened' && data.position) {
      // Add new position or reload
      loadPositions();
    } else if (data.action === 'updated' && data.position) {
      // Update specific position
      setPositions(prev =>
        prev.map(pos =>
          pos.ticket === data.position.ticket ? { ...pos, ...data.position } : pos
        )
      );
    }
  }, []);

  const loadPositions = async (useRealtimePnL = true) => {
    try {
      setLoading(true);
      console.log(`📊 Loading positions with real-time P&L: ${useRealtimePnL}`);

      // Use real-time P&L parameter
      const url = `/api/trading/positions?realtime_pnl=${useRealtimePnL}`;
      const response = await fetch(`http://localhost:8001${url}`);

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const positions = await response.json();
      console.log('📊 Positions loaded:', positions);
      setPositions(positions || []);
    } catch (error) {
      console.error('Error loading positions:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleClosePosition = async (ticket) => {
    if (!window.confirm('Are you sure you want to close this position?')) {
      return;
    }

    try {
      setClosingPosition(ticket);
      await tradingApi.closePosition(ticket, 'Manual close from UI');

      // Reload positions after closing
      setTimeout(loadPositions, 1000);

    } catch (error) {
      console.error('Error closing position:', error);
      alert(`Failed to close position: ${error.message}`);
    } finally {
      setClosingPosition(null);
    }
  };

  const filteredPositions = positions.filter(position => {
    if (filter === 'open') return position.status === 'OPEN';
    if (filter === 'closed') return position.status === 'CLOSED';
    return true;
  });

  // Debug filtered positions
  console.log('🔍 Filtered positions:', {
    total: positions.length,
    filtered: filteredPositions.length,
    filter,
    forceRender
  });

  const openPositions = positions.filter(p => p.status === 'OPEN');
  const totalProfit = positions.reduce((sum, p) => sum + (p.profit || 0), 0);
  const totalUnrealizedPnL = openPositions.reduce((sum, p) => sum + (p.profit || 0), 0);

  const getPositionIcon = (orderType) => {
    if (orderType === 'BUY' || orderType === 'BUY_LIMIT' || orderType === 'BUY_STOP') {
      return <TrendingUp className="w-4 h-4 text-green-600" />;
    } else {
      return <TrendingDown className="w-4 h-4 text-red-600" />;
    }
  };

  const getStatusIcon = (status) => {
    switch (status) {
      case 'OPEN':
        return <CheckCircle className="w-4 h-4 text-green-600" />;
      case 'CLOSED':
        return <X className="w-4 h-4 text-gray-600" />;
      case 'PENDING':
        return <Clock className="w-4 h-4 text-yellow-600" />;
      default:
        return <AlertTriangle className="w-4 h-4 text-gray-600" />;
    }
  };

  const calculateDuration = (openTime, closeTime) => {
    const start = new Date(openTime);
    const end = closeTime ? new Date(closeTime) : new Date();
    const duration = end - start;

    const hours = Math.floor(duration / (1000 * 60 * 60));
    const minutes = Math.floor((duration % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 24) {
      const days = Math.floor(hours / 24);
      return `${days}d ${hours % 24}h`;
    } else if (hours > 0) {
      return `${hours}h ${minutes}m`;
    } else {
      return `${minutes}m`;
    }
  };

  // Log render
  console.log('🎨 RENDERING PositionManager:', {
    positionsCount: positions.length,
    forceRender,
    wsConnected,
    timestamp: new Date().toLocaleTimeString(),
    positionsPreview: positions.slice(0, 2).map(p => ({
      symbol: p.symbol,
      profit: p.profit,
      current_price: p.current_price
    }))
  });

  return (
    <div className="space-y-6" key={`position-manager-${forceRender}-${positions.length}`}>
      {/* Header with Stats */}
      <div className="card">
        <div className="flex items-center justify-between mb-4">
          <div>
            <div className="flex items-center space-x-3">
              <h2 className="text-lg font-medium text-gray-900">Position Manager</h2>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${wsConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
                <span className={`text-xs font-medium ${wsConnected ? 'text-green-600' : 'text-red-600'}`}>
                  {wsConnected ? 'Live' : 'Offline'}
                </span>
              </div>
            </div>
            <p className="text-sm text-gray-600">
              {wsConnected ? 'Real-time position updates enabled' : 'Manual refresh required'}
              {lastUpdate && (
                <span className="ml-2 text-xs text-gray-500">
                  • Last update: {lastUpdate.toLocaleTimeString()}
                </span>
              )}
              <span className="ml-2 text-xs text-blue-500">
                • Renders: {forceRender}
              </span>
            </p>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={loadPositions}
              disabled={loading}
              className="btn btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
            <button
              onClick={() => {
                console.log('🧪 Manual force render test');
                setForceRender(prev => prev + 1);
                setLastUpdate(new Date());
              }}
              className="btn btn-primary flex items-center space-x-2"
            >
              <span>🧪 Test Render</span>
            </button>
          </div>
        </div>

        {/* Summary Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <div className="text-center">
            <p className="text-sm text-gray-600">Total Positions</p>
            <p className="text-2xl font-semibold text-gray-900">{positions.length}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Open Positions</p>
            <p className="text-2xl font-semibold text-green-600">{openPositions.length}</p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Total P&L</p>
            <p className={`text-2xl font-semibold ${totalProfit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(totalProfit)}
            </p>
          </div>
          <div className="text-center">
            <p className="text-sm text-gray-600">Unrealized P&L</p>
            <p className={`text-2xl font-semibold ${totalUnrealizedPnL >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {formatCurrency(totalUnrealizedPnL)}
            </p>
          </div>
        </div>
      </div>

      {/* Filter Controls */}
      <div className="card">
        <div className="flex items-center space-x-4">
          <span className="text-sm font-medium text-gray-700">Filter:</span>
          <div className="flex space-x-2">
            {[
              { value: 'all', label: 'All Positions' },
              { value: 'open', label: 'Open Only' },
              { value: 'closed', label: 'Closed Only' }
            ].map((option) => (
              <button
                key={option.value}
                onClick={() => setFilter(option.value)}
                className={`px-3 py-1 rounded-md text-sm font-medium transition-colors ${
                  filter === option.value
                    ? 'bg-primary-100 text-primary-700'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {option.label}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Positions Table */}
      <div className="card">
        <div className="overflow-x-auto">
          {filteredPositions.length === 0 ? (
            <div className="text-center py-8">
              <AlertTriangle className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-600">No positions found</p>
              <p className="text-sm text-gray-500 mt-2">
                {filter === 'open'
                  ? 'No open positions at the moment'
                  : filter === 'closed'
                  ? 'No closed positions found'
                  : 'Start trading to see positions here'
                }
              </p>
            </div>
          ) : (
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Position
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Symbol
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Type
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Size
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Entry Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Current Price
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    P&L
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Duration
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {filteredPositions.map((position, index) => (
                  <tr key={`${position.ticket || position.id}-${forceRender}-${index}`} className="hover:bg-gray-50">
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      #{position.ticket || position.id}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {position.symbol}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        {getPositionIcon(position.order_type)}
                        <span>{position.order_type}</span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.lot_size}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.entry_price?.toFixed(5)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      <div className="flex items-center space-x-2">
                        <span key={`${position.ticket}-price-${position.current_price}-${forceRender}`}>
                          {position.current_price?.toFixed(5) || 'N/A'}
                        </span>
                        {wsConnected && position.current_price && (
                          <div className="w-1 h-1 bg-green-500 rounded-full animate-ping"></div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <div className="flex items-center space-x-2">
                        <span
                          className={`font-medium transition-all duration-300 ${
                            (position.profit || 0) >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}
                          key={`${position.ticket}-${position.profit}-${forceRender}`}
                        >
                          {formatCurrency(position.profit || 0)}
                        </span>
                        {wsConnected && (
                          <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
                        )}
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {calculateDuration(position.open_time, position.close_time)}
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        {getStatusIcon(position.status)}
                        <span
                          className={`status-indicator ${
                            position.status === 'OPEN'
                              ? 'status-healthy'
                              : position.status === 'CLOSED'
                              ? 'bg-gray-100 text-gray-800'
                              : 'status-warning'
                          }`}
                        >
                          {position.status}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {position.status === 'OPEN' && (
                        <button
                          onClick={() => handleClosePosition(position.ticket)}
                          disabled={closingPosition === position.ticket}
                          className="btn btn-danger btn-sm flex items-center space-x-1"
                        >
                          <X className="w-3 h-3" />
                          <span>
                            {closingPosition === position.ticket ? 'Closing...' : 'Close'}
                          </span>
                        </button>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          )}
        </div>
      </div>
    </div>
  );
};

export default PositionManager;
