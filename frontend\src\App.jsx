import React, { useState, useEffect } from 'react';
import { Activity, TrendingUp, DollarSign, Settings } from 'lucide-react';
import Dashboard from './components/Dashboard';
import TradingPanel from './components/TradingPanel';
import MarketData from './components/MarketData';
import PositionManager from './components/PositionManager';
import { tradingApi, wsService } from './services/api';

function App() {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [systemStatus, setSystemStatus] = useState(null);
  const [isConnected, setIsConnected] = useState(false);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Initialize WebSocket connection
    wsService.connect();

    // Add WebSocket listeners
    wsService.addListener('connected', () => {
      setIsConnected(true);
    });

    wsService.addListener('disconnected', () => {
      setIsConnected(false);
    });

    wsService.addListener('system_update', (data) => {
      if (data.data && data.data.system_status) {
        setSystemStatus(data.data.system_status);
      }
    });

    // Load initial data
    loadInitialData();

    // Cleanup on unmount
    return () => {
      wsService.disconnect();
    };
  }, []);

  const loadInitialData = async () => {
    try {
      setLoading(true);
      const response = await tradingApi.getSystemStatus();
      setSystemStatus(response.data);
    } catch (error) {
      console.error('Error loading initial data:', error);
    } finally {
      setLoading(false);
    }
  };

  const tabs = [
    { id: 'dashboard', name: 'Dashboard', icon: Activity },
    { id: 'trading', name: 'Trading', icon: TrendingUp },
    { id: 'market', name: 'Market Data', icon: DollarSign },
    { id: 'positions', name: 'Positions', icon: Settings },
  ];

  const getStatusColor = (health) => {
    switch (health) {
      case 'HEALTHY':
        return 'bg-success-500';
      case 'WARNING':
        return 'bg-warning-500';
      case 'ERROR':
        return 'bg-danger-500';
      default:
        return 'bg-gray-500';
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading Auto Trading System...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                Auto Trading System
              </h1>
              <div className="ml-4 flex items-center space-x-2">
                <div
                  className={`w-3 h-3 rounded-full ${
                    isConnected ? 'bg-success-500' : 'bg-danger-500'
                  }`}
                ></div>
                <span className="text-sm text-gray-600">
                  {isConnected ? 'Connected' : 'Disconnected'}
                </span>
              </div>
            </div>

            {/* System Status */}
            {systemStatus && (
              <div className="flex items-center space-x-4">
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">System:</span>
                  <span
                    className={`status-indicator ${
                      systemStatus.system_health === 'HEALTHY'
                        ? 'status-healthy'
                        : systemStatus.system_health === 'WARNING'
                        ? 'status-warning'
                        : 'status-error'
                    }`}
                  >
                    {systemStatus.system_health}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">MT5:</span>
                  <span
                    className={`status-indicator ${
                      systemStatus.is_mt5_connected ? 'status-healthy' : 'status-error'
                    }`}
                  >
                    {systemStatus.is_mt5_connected ? 'Connected' : 'Disconnected'}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-sm text-gray-600">GPT:</span>
                  <span
                    className={`status-indicator ${
                      systemStatus.is_gpt_available ? 'status-healthy' : 'status-error'
                    }`}
                  >
                    {systemStatus.is_gpt_available ? 'Available' : 'Unavailable'}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Navigation */}
      <nav className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex space-x-8">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 py-4 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-primary-500 text-primary-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {activeTab === 'dashboard' && <Dashboard systemStatus={systemStatus} />}
        {activeTab === 'trading' && <TradingPanel />}
        {activeTab === 'market' && <MarketData />}
        {activeTab === 'positions' && <PositionManager />}
      </main>
    </div>
  );
}

export default App;
