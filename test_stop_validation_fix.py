#!/usr/bin/env python3
"""
Test stop validation fix in trading engine
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from backend.models.trading_models import GPTAnalysis, SignalType, RiskLevel, MarketData
from backend.services.trading_engine import TradingEngine
from backend.utils.risk_management import RiskManager, RiskManagement
from datetime import datetime

def test_stop_validation_fix():
    """Test stop validation fix"""
    print("🔧 STOP VALIDATION FIX TEST")
    print("=" * 50)
    
    # Create mock market data
    market_data = MarketData(
        symbol="BTCUSDm",
        bid=106800.0,
        ask=106820.0,
        mid_price=106810.0,
        spread=20.0,
        timestamp=datetime.now()
    )
    
    # Test Case 1: GPT gives small stop levels (should be recalculated)
    print("1️⃣ Testing GPT with SMALL stop levels:")
    print("-" * 40)
    
    gpt_analysis_small = GPTAnalysis(
        symbol="BTCUSDm",
        timestamp=datetime.now(),
        signal=SignalType.SELL,
        confidence=0.85,
        entry_price=106810.0,
        stop_loss=106860.0,  # Only $50 distance - TOO SMALL!
        take_profit=106710.0,  # Only $100 distance - TOO SMALL!
        risk_level=RiskLevel.MEDIUM,
        reasoning="Test with small stops",
        technical_summary="Test",
        market_sentiment="bearish"
    )
    
    print(f"   GPT Entry: ${gpt_analysis_small.entry_price:,.2f}")
    print(f"   GPT Stop Loss: ${gpt_analysis_small.stop_loss:,.2f}")
    print(f"   GPT Take Profit: ${gpt_analysis_small.take_profit:,.2f}")
    print(f"   SL Distance: ${abs(gpt_analysis_small.stop_loss - gpt_analysis_small.entry_price):,.2f}")
    print(f"   TP Distance: ${abs(gpt_analysis_small.take_profit - gpt_analysis_small.entry_price):,.2f}")
    
    # Simulate the validation logic from trading engine
    entry_price = gpt_analysis_small.entry_price
    stop_loss = gpt_analysis_small.stop_loss
    take_profit = gpt_analysis_small.take_profit
    
    sl_distance = abs(stop_loss - entry_price)
    tp_distance = abs(take_profit - entry_price)
    
    # BTCUSDm minimum distances
    min_sl_distance = 500.0
    min_tp_distance = 1000.0
    
    print(f"\n   🔍 VALIDATION:")
    print(f"   Min SL Distance: ${min_sl_distance:,.2f}")
    print(f"   Min TP Distance: ${min_tp_distance:,.2f}")
    print(f"   Current SL: ${sl_distance:,.2f} ({'❌ TOO SMALL' if sl_distance < min_sl_distance else '✅ OK'})")
    print(f"   Current TP: ${tp_distance:,.2f} ({'❌ TOO SMALL' if tp_distance < min_tp_distance else '✅ OK'})")
    
    needs_recalculation = sl_distance < min_sl_distance or tp_distance < min_tp_distance
    print(f"   Needs Recalculation: {'✅ YES' if needs_recalculation else '❌ NO'}")
    
    if needs_recalculation:
        # Simulate risk manager calculation
        risk_params = RiskManagement(
            max_risk_percent=2.0,
            max_open_positions=5,
            max_daily_loss=1000.0,
            max_drawdown_percent=30.0,
            stop_loss_pips=50,
            take_profit_pips=100,
            trailing_stop_pips=30
        )
        
        risk_manager = RiskManager(risk_params)
        from backend.models.trading_models import OrderType
        
        new_sl, new_tp = risk_manager.calculate_stop_loss_take_profit(
            "BTCUSDm", entry_price, OrderType.SELL
        )
        
        print(f"\n   🔧 RECALCULATED VALUES:")
        print(f"   New Stop Loss: ${new_sl:,.2f}")
        print(f"   New Take Profit: ${new_tp:,.2f}")
        print(f"   New SL Distance: ${abs(new_sl - entry_price):,.2f}")
        print(f"   New TP Distance: ${abs(new_tp - entry_price):,.2f}")
        
        # Validate new values
        new_sl_distance = abs(new_sl - entry_price)
        new_tp_distance = abs(new_tp - entry_price)
        
        print(f"   New SL Valid: {'✅ YES' if new_sl_distance >= min_sl_distance else '❌ NO'}")
        print(f"   New TP Valid: {'✅ YES' if new_tp_distance >= min_tp_distance else '❌ NO'}")
        
        return new_sl_distance >= min_sl_distance and new_tp_distance >= min_tp_distance
    
    # Test Case 2: GPT gives valid stop levels
    print("\n2️⃣ Testing GPT with VALID stop levels:")
    print("-" * 40)
    
    gpt_analysis_valid = GPTAnalysis(
        symbol="BTCUSDm",
        timestamp=datetime.now(),
        signal=SignalType.SELL,
        confidence=0.85,
        entry_price=106810.0,
        stop_loss=107310.0,  # $500 distance - VALID!
        take_profit=105810.0,  # $1000 distance - VALID!
        risk_level=RiskLevel.MEDIUM,
        reasoning="Test with valid stops",
        technical_summary="Test",
        market_sentiment="bearish"
    )
    
    entry_price_2 = gpt_analysis_valid.entry_price
    stop_loss_2 = gpt_analysis_valid.stop_loss
    take_profit_2 = gpt_analysis_valid.take_profit
    
    sl_distance_2 = abs(stop_loss_2 - entry_price_2)
    tp_distance_2 = abs(take_profit_2 - entry_price_2)
    
    print(f"   GPT Entry: ${entry_price_2:,.2f}")
    print(f"   GPT Stop Loss: ${stop_loss_2:,.2f}")
    print(f"   GPT Take Profit: ${take_profit_2:,.2f}")
    print(f"   SL Distance: ${sl_distance_2:,.2f}")
    print(f"   TP Distance: ${tp_distance_2:,.2f}")
    
    needs_recalculation_2 = sl_distance_2 < min_sl_distance or tp_distance_2 < min_tp_distance
    print(f"   Needs Recalculation: {'✅ YES' if needs_recalculation_2 else '❌ NO'}")
    print(f"   SL Valid: {'✅ YES' if sl_distance_2 >= min_sl_distance else '❌ NO'}")
    print(f"   TP Valid: {'✅ YES' if tp_distance_2 >= min_tp_distance else '❌ NO'}")
    
    return not needs_recalculation_2

if __name__ == "__main__":
    print("🧪 TESTING STOP VALIDATION FIX")
    print("=" * 60)
    
    test1_result = test_stop_validation_fix()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    print(f"   Stop Validation Fix: {'✅ WORKING' if test1_result else '❌ FAILED'}")
    
    if test1_result:
        print("\n🎉 FIX SUCCESSFUL!")
        print("✅ Small GPT stop levels will be recalculated")
        print("✅ Valid GPT stop levels will be preserved")
        print("✅ BTCUSDm should no longer get 'Invalid stops' error")
        print("\n🚀 NEXT STEPS:")
        print("1. Restart backend server")
        print("2. Test BTCUSDm order placement")
        print("3. Check logs for recalculation messages")
    else:
        print("\n❌ FIX FAILED!")
        print("🔧 Need to debug further")
