#!/usr/bin/env python3
"""
Test with smaller position sizes and cheaper symbols
"""
import requests
import time

def test_affordable_orders():
    """Test orders that fit within $16.33 margin"""
    print("🔧 === TESTING AFFORDABLE ORDERS ===")
    
    # Get account info
    try:
        response = requests.get("http://localhost:8001/api/trading/account", timeout=5)
        account = response.json()
        free_margin = account.get('free_margin', 16.33)
        print(f"💰 Available margin: ${free_margin:.2f}")
    except:
        free_margin = 16.33
        print(f"💰 Assumed margin: ${free_margin:.2f}")
    
    # Test different symbols and calculate affordable lot sizes
    symbols_to_test = [
        {"symbol": "BTCUSDm", "approx_price": 108000},
        {"symbol": "ETHUSDm", "approx_price": 4000},
        {"symbol": "XAUUSDm", "approx_price": 2000},
        {"symbol": "EURUSDm", "approx_price": 1.1},
        {"symbol": "USDAED", "approx_price": 3.67},
        {"symbol": "USDAMD", "approx_price": 400}
    ]
    
    print(f"\n📊 Calculating affordable lot sizes:")
    print(f"{'Symbol':<10} {'Price':<10} {'Min Margin':<12} {'Max Lots':<10} {'Affordable?'}")
    print("-" * 60)
    
    affordable_orders = []
    
    for item in symbols_to_test:
        symbol = item["symbol"]
        price = item["approx_price"]
        
        # Calculate minimum margin needed for 0.01 lots with 20:1 leverage
        position_value = 0.01 * price
        min_margin = position_value / 20  # 20:1 leverage
        
        # Calculate maximum affordable lot size
        max_position_value = free_margin * 20  # 20:1 leverage
        max_lots = max_position_value / price
        
        affordable = min_margin <= free_margin
        
        print(f"{symbol:<10} ${price:<9.2f} ${min_margin:<11.2f} {max_lots:<9.4f} {'✅' if affordable else '❌'}")
        
        if affordable:
            affordable_orders.append({
                "symbol": symbol,
                "max_lots": min(max_lots, 0.01),  # Cap at 0.01 for safety
                "margin_needed": min_margin
            })
    
    print(f"\n✅ Affordable orders found: {len(affordable_orders)}")
    
    return affordable_orders

def create_test_order_endpoint():
    """Create a simple test endpoint for order placement"""
    print("\n🔧 === CREATING TEST ORDER ENDPOINT ===")
    
    test_endpoint_code = '''
# Add this to backend/api/trading_routes.py

@router.post("/test-order")
async def test_order_placement(engine: TradingEngine = Depends(get_trading_engine)):
    """Test order placement with safe parameters"""
    try:
        from ..models.trading_models import OrderRequest, OrderType
        
        # Get current market data for a cheap symbol
        market_data = await engine.mt5_connector.get_market_data("USDAED")
        if not market_data:
            raise HTTPException(status_code=400, detail="Cannot get market data")
        
        # Create minimal test order
        order_request = OrderRequest(
            symbol="USDAED",
            order_type=OrderType.BUY,
            lot_size=0.01,
            comment="Test",
            deviation=100
        )
        
        # Place order
        ticket = await engine.mt5_connector.place_order(order_request)
        
        if ticket:
            return {"message": f"Test order placed successfully: {ticket}", "ticket": ticket}
        else:
            return {"message": "Test order failed", "ticket": None}
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Test order error: {e}")
'''
    
    print("📝 Test endpoint code:")
    print(test_endpoint_code)
    
    print("\n💡 To use this endpoint:")
    print("1. Add the code above to trading_routes.py")
    print("2. Restart the backend")
    print("3. Call: POST http://localhost:8001/api/trading/test-order")

def test_with_manual_signal():
    """Test by creating a manual trading signal"""
    print("\n🔧 === TESTING WITH MANUAL SIGNAL ===")
    
    # Find the most affordable symbol
    affordable = test_affordable_orders()
    
    if not affordable:
        print("❌ No affordable symbols found with current margin")
        return False
    
    # Use the most affordable symbol
    best_option = affordable[0]
    symbol = best_option["symbol"]
    lot_size = best_option["max_lots"]
    
    print(f"\n🎯 Best option for testing:")
    print(f"   Symbol: {symbol}")
    print(f"   Lot size: {lot_size:.4f}")
    print(f"   Margin needed: ${best_option['margin_needed']:.2f}")
    
    # Create a manual trading signal
    print(f"\n📝 Manual signal creation:")
    print(f"   This would bypass GPT analysis")
    print(f"   And directly create a TradingSignal object")
    print(f"   Then call _execute_signal() method")
    
    manual_signal_code = f'''
# Manual signal for testing:
from backend.models.trading_models import TradingSignal, SignalType

signal = TradingSignal(
    symbol="{symbol}",
    signal_type=SignalType.BUY,
    confidence=0.8,
    entry_price=current_market_price,
    stop_loss=current_market_price * 0.99,  # 1% stop loss
    take_profit=current_market_price * 1.01,  # 1% take profit
    lot_size={lot_size:.4f},
    reasoning="Manual test signal"
)

# Then call:
await trading_engine._execute_signal(signal)
'''
    
    print(manual_signal_code)
    return True

if __name__ == "__main__":
    print("🎯 Testing Affordable Order Placement")
    print("=" * 60)
    
    affordable = test_affordable_orders()
    
    if affordable:
        print(f"\n🎉 Found {len(affordable)} affordable symbols!")
        create_test_order_endpoint()
        test_with_manual_signal()
        
        print("\n" + "=" * 60)
        print("🎯 RECOMMENDATIONS:")
        print("1. Test with cheaper symbols first (USDAED, EURUSDm)")
        print("2. Use very small lot sizes (0.001 if allowed)")
        print("3. Add more funds to account for proper testing")
        print("4. Consider using demo account for testing")
        
        print("\n💡 NEXT STEPS:")
        print("1. Add test endpoint to backend")
        print("2. Test with USDAED (most affordable)")
        print("3. Monitor MT5 terminal for actual order placement")
        print("4. Check for any broker-specific restrictions")
    else:
        print("\n❌ No affordable symbols found!")
        print("💡 Solutions:")
        print("1. Add more funds to MT5 account")
        print("2. Use demo account for testing")
        print("3. Check if micro lots (0.001) are available")
        print("4. Test with different broker")
    
    print(f"\n🌐 Account needs more margin for proper order testing")
