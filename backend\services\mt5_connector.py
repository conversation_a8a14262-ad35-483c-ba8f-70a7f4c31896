"""
MetaTrader 5 Connector Service
Handles all MT5 operations including connection, data retrieval, and order execution
"""
import MetaTrader5 as mt5
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from loguru import logger

from ..models.trading_models import (
    MarketData, OHLCV, Position, OrderRequest, AccountInfo,
    OrderType, PositionStatus, TechnicalIndicators
)
from ..config import settings, SYMBOL_CONFIG


class MT5Connector:
    """MetaTrader 5 connection and trading operations"""

    def __init__(self):
        self.is_connected = False
        self.account_info = None

    async def connect(self) -> bool:
        """Connect to MetaTrader 5"""
        try:
            # Initialize MT5 connection
            if not mt5.initialize():
                logger.error(f"MT5 initialization failed: {mt5.last_error()}")
                return False

            # Login to account
            if not mt5.login(
                login=settings.mt5_login,
                password=settings.mt5_password,
                server=settings.mt5_server
            ):
                logger.error(f"MT5 login failed: {mt5.last_error()}")
                mt5.shutdown()
                return False

            self.is_connected = True
            self.account_info = await self.get_account_info()
            logger.info(f"Connected to MT5 account: {settings.mt5_login}")

            # Log available symbols for debugging
            await self._log_available_symbols()

            return True

        except Exception as e:
            logger.error(f"MT5 connection error: {e}")
            return False

    async def disconnect(self):
        """Disconnect from MetaTrader 5"""
        if self.is_connected:
            mt5.shutdown()
            self.is_connected = False
            logger.info("Disconnected from MT5")

    async def _log_available_symbols(self):
        """Log available symbols for debugging"""
        try:
            symbols = mt5.symbols_get()
            if symbols:
                # Log first 50 symbols for debugging
                symbol_names = [s.name for s in symbols[:50]]
                logger.info(f"Available symbols (first 50): {symbol_names}")

                # Look for common trading symbols
                common_symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD',
                                'XAUUSD', 'XAGUSD', 'BTCUSD', 'ETHUSD', 'GOLD', 'SILVER', 'OIL', 'US30', 'US500']
                found_symbols = []
                for symbol in common_symbols:
                    if any(s.name == symbol for s in symbols):
                        found_symbols.append(symbol)
                logger.info(f"Found common trading symbols: {found_symbols}")

                # Check if our trading symbols exist
                trading_symbols = settings.symbols_list
                for symbol in trading_symbols:
                    symbol_info = mt5.symbol_info(symbol)
                    if symbol_info:
                        logger.info(f"Symbol {symbol}: Found, Selected: {symbol_info.select}")
                    else:
                        logger.warning(f"Symbol {symbol}: Not found in broker")
            else:
                logger.warning("No symbols available")
        except Exception as e:
            logger.error(f"Error logging symbols: {e}")

    async def get_account_info(self) -> Optional[AccountInfo]:
        """Get account information"""
        try:
            if not self.is_connected:
                return None

            account = mt5.account_info()
            if account is None:
                return None

            return AccountInfo(
                login=account.login,
                balance=account.balance,
                equity=account.equity,
                margin=account.margin,
                free_margin=account.margin_free,
                margin_level=account.margin_level,
                profit=account.profit,
                currency=account.currency,
                leverage=account.leverage,
                server=account.server
            )

        except Exception as e:
            logger.error(f"Error getting account info: {e}")
            return None

    async def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get current market data for symbol"""
        try:
            if not self.is_connected:
                return None

            # Check if symbol exists and is selected
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.warning(f"Symbol {symbol} not found")
                return None

            # Select symbol if not selected
            if not symbol_info.select:
                if not mt5.symbol_select(symbol, True):
                    logger.warning(f"Failed to select symbol: {symbol}")
                    return None

            tick = mt5.symbol_info_tick(symbol)
            if tick is None:
                logger.warning(f"No tick data for symbol: {symbol}")
                return None

            return MarketData(
                symbol=symbol,
                timestamp=datetime.fromtimestamp(tick.time),
                bid=tick.bid,
                ask=tick.ask,
                spread=tick.ask - tick.bid,
                volume=tick.volume
            )

        except Exception as e:
            logger.error(f"Error getting market data for {symbol}: {e}")
            return None

    async def get_multi_timeframe_data(self, symbol: str, timeframes: List[str] = None) -> Dict[str, List[OHLCV]]:
        """Get OHLCV data for multiple timeframes"""
        if timeframes is None:
            timeframes = ["15m", "30m", "1h", "4h"]

        timeframe_data = {}

        for tf in timeframes:
            # Get appropriate count for each timeframe
            count = self._get_count_for_timeframe(tf)
            ohlcv_data = await self.get_ohlcv_data(symbol, tf, count)
            timeframe_data[tf] = ohlcv_data

        return timeframe_data

    def _get_count_for_timeframe(self, timeframe: str) -> int:
        """Get appropriate candle count for each timeframe"""
        # Increased counts to ensure sufficient data for technical indicators
        # Technical indicators need at least 50-100 candles for accurate calculation
        count_map = {
            "15m": 200,  # ~50 hours of data
            "30m": 150,  # ~75 hours of data
            "1h": 120,   # ~5 days of data
            "4h": 100,   # ~16 days of data
            "1d": 60     # ~2 months of data
        }
        return count_map.get(timeframe, 100)

    async def get_ohlcv_data(
        self,
        symbol: str,
        timeframe: str = "M5",
        count: int = 100
    ) -> List[OHLCV]:
        """Get OHLCV candlestick data"""
        try:
            if not self.is_connected:
                return []

            # Map timeframe string to MT5 constant
            timeframe_map = {
                # New format
                "1m": mt5.TIMEFRAME_M1,
                "5m": mt5.TIMEFRAME_M5,
                "15m": mt5.TIMEFRAME_M15,
                "30m": mt5.TIMEFRAME_M30,
                "1h": mt5.TIMEFRAME_H1,
                "4h": mt5.TIMEFRAME_H4,
                "1d": mt5.TIMEFRAME_D1,
                # Legacy format
                "M1": mt5.TIMEFRAME_M1,
                "M5": mt5.TIMEFRAME_M5,
                "M15": mt5.TIMEFRAME_M15,
                "M30": mt5.TIMEFRAME_M30,
                "H1": mt5.TIMEFRAME_H1,
                "H4": mt5.TIMEFRAME_H4,
                "D1": mt5.TIMEFRAME_D1
            }

            mt5_timeframe = timeframe_map.get(timeframe, mt5.TIMEFRAME_M5)

            # Get rates
            rates = mt5.copy_rates_from_pos(symbol, mt5_timeframe, 0, count)
            if rates is None or len(rates) == 0:
                logger.warning(f"No OHLCV data for {symbol}")
                return []

            ohlcv_data = []
            for rate in rates:
                ohlcv_data.append(OHLCV(
                    symbol=symbol,
                    timestamp=datetime.fromtimestamp(rate['time']),
                    open=rate['open'],
                    high=rate['high'],
                    low=rate['low'],
                    close=rate['close'],
                    volume=rate['tick_volume'],
                    timeframe=timeframe
                ))

            return ohlcv_data

        except Exception as e:
            logger.error(f"Error getting OHLCV data for {symbol}: {e}")
            return []

    async def place_order(self, order_request: OrderRequest) -> Optional[int]:
        """Place trading order"""
        try:
            if not self.is_connected:
                logger.error("MT5 not connected")
                return None

            # Prepare order request
            symbol = order_request.symbol
            lot_size = order_request.lot_size

            # Get current market data
            market_data = await self.get_market_data(symbol)
            if not market_data:
                logger.error(f"Cannot get market data for {symbol}")
                return None

            # Determine order type and price
            if order_request.order_type == OrderType.BUY:
                action = mt5.TRADE_ACTION_DEAL
                order_type = mt5.ORDER_TYPE_BUY
                price = market_data.ask
            elif order_request.order_type == OrderType.SELL:
                action = mt5.TRADE_ACTION_DEAL
                order_type = mt5.ORDER_TYPE_SELL
                price = market_data.bid
            elif order_request.order_type == OrderType.BUY_LIMIT:
                action = mt5.TRADE_ACTION_PENDING
                order_type = mt5.ORDER_TYPE_BUY_LIMIT
                price = order_request.price or market_data.bid
            elif order_request.order_type == OrderType.SELL_LIMIT:
                action = mt5.TRADE_ACTION_PENDING
                order_type = mt5.ORDER_TYPE_SELL_LIMIT
                price = order_request.price or market_data.ask
            elif order_request.order_type == OrderType.BUY_STOP:
                action = mt5.TRADE_ACTION_PENDING
                order_type = mt5.ORDER_TYPE_BUY_STOP
                price = order_request.price or market_data.ask
            elif order_request.order_type == OrderType.SELL_STOP:
                action = mt5.TRADE_ACTION_PENDING
                order_type = mt5.ORDER_TYPE_SELL_STOP
                price = order_request.price or market_data.bid
            else:
                logger.error(f"Unsupported order type: {order_request.order_type}")
                return None

            # Get symbol info for validation
            symbol_info = mt5.symbol_info(symbol)
            if symbol_info is None:
                logger.error(f"Symbol {symbol} not found for order placement")
                return None

            # Validate lot size against symbol requirements
            min_lot = symbol_info.volume_min
            max_lot = symbol_info.volume_max
            lot_step = symbol_info.volume_step

            # Round lot size to valid step
            lot_size = round(lot_size / lot_step) * lot_step
            lot_size = max(min_lot, min(lot_size, max_lot))

            logger.info(f"Order details for {symbol}: lot_size={lot_size}, price={price:.5f}, type={order_request.order_type}")

            # Determine appropriate filling type based on symbol
            # Use simple filling type that works for most symbols
            filling_type = mt5.ORDER_FILLING_IOC

            # Check if symbol supports different filling modes
            try:
                # Check filling mode flags (these are bit flags)
                if hasattr(symbol_info, 'filling_mode'):
                    if symbol_info.filling_mode & 1:  # FOK mode
                        filling_type = mt5.ORDER_FILLING_FOK
                    elif symbol_info.filling_mode & 2:  # Return mode
                        filling_type = mt5.ORDER_FILLING_RETURN
            except:
                # Fallback to IOC if there's any issue
                filling_type = mt5.ORDER_FILLING_IOC

            # Prepare request
            request = {
                "action": action,
                "symbol": symbol,
                "volume": lot_size,
                "type": order_type,
                "price": price,
                "deviation": order_request.deviation or 100,  # Default deviation for crypto
                "magic": 234000,
                "comment": order_request.comment or "Auto Trading",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": filling_type,
            }

            # Add stop loss and take profit if provided
            if order_request.stop_loss:
                request["sl"] = order_request.stop_loss
            if order_request.take_profit:
                request["tp"] = order_request.take_profit

            # Log the complete request for debugging
            logger.info(f"Sending order request for {symbol}: {request}")

            # Send order
            result = mt5.order_send(request)

            # Check if result is None
            if result is None:
                last_error = mt5.last_error()
                logger.error(f"Order send failed: MT5 returned None result for {symbol}")
                logger.error(f"MT5 last error: {last_error}")
                return None

            # Check result code
            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Order failed for {symbol}: {result.retcode} - {result.comment}")
                logger.error(f"Request was: {request}")
                return None

            logger.info(f"Order placed successfully for {symbol}: Ticket {result.order}")
            return result.order

        except Exception as e:
            logger.error(f"Error placing order: {e}")
            return None

    async def get_positions(self, calculate_realtime_pnl: bool = False) -> List[Position]:
        """Get all open positions with optional real-time P&L calculation"""
        try:
            if not self.is_connected:
                return []

            positions = mt5.positions_get()
            if positions is None:
                return []

            position_list = []
            for pos in positions:
                # Determine order type
                if pos.type == mt5.POSITION_TYPE_BUY:
                    order_type = OrderType.BUY
                else:
                    order_type = OrderType.SELL

                # Get current market price for real-time P&L
                current_price = pos.price_current
                profit = pos.profit

                if calculate_realtime_pnl:
                    # Get fresh market data for real-time P&L
                    market_data = await self.get_market_data(pos.symbol)
                    if market_data:
                        # Use bid for SELL positions, ask for BUY positions
                        if order_type == OrderType.BUY:
                            current_price = market_data.bid
                        else:
                            current_price = market_data.ask

                        # Calculate real-time P&L
                        profit = self._calculate_realtime_pnl(
                            order_type=order_type,
                            entry_price=pos.price_open,
                            current_price=current_price,
                            lot_size=pos.volume,
                            symbol=pos.symbol
                        )

                position_list.append(Position(
                    ticket=pos.ticket,
                    symbol=pos.symbol,
                    order_type=order_type,
                    lot_size=pos.volume,
                    entry_price=pos.price_open,
                    current_price=current_price,
                    stop_loss=pos.sl if pos.sl != 0 else None,
                    take_profit=pos.tp if pos.tp != 0 else None,
                    open_time=datetime.fromtimestamp(pos.time),
                    status=PositionStatus.OPEN,
                    profit=profit,
                    commission=getattr(pos, 'commission', 0.0),
                    swap=getattr(pos, 'swap', 0.0),
                    comment=getattr(pos, 'comment', '')
                ))

            return position_list

        except Exception as e:
            logger.error(f"Error getting positions: {e}")
            return []

    def _calculate_realtime_pnl(
        self,
        order_type: OrderType,
        entry_price: float,
        current_price: float,
        lot_size: float,
        symbol: str
    ) -> float:
        """Calculate real-time P&L based on current market price"""
        try:
            # Get symbol info for contract size and pip value
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                return 0.0

            # Calculate price difference
            if order_type == OrderType.BUY:
                price_diff = current_price - entry_price
            else:  # SELL
                price_diff = entry_price - current_price

            # Calculate P&L based on symbol type
            if symbol.endswith('m'):  # Micro contracts (BTCUSDm, ETHUSDm, etc.)
                # For crypto micro contracts, 1 lot = 1 unit of base currency
                pnl = price_diff * lot_size
            elif 'USD' in symbol:  # Forex pairs
                # For forex, calculate based on pip value
                pip_value = self._get_pip_value(symbol, lot_size)
                pips = price_diff / self._get_pip_size(symbol)
                pnl = pips * pip_value
            else:
                # Default calculation
                pnl = price_diff * lot_size * symbol_info.trade_contract_size

            return round(pnl, 2)

        except Exception as e:
            logger.error(f"Error calculating real-time P&L for {symbol}: {e}")
            return 0.0

    def _get_pip_value(self, symbol: str, lot_size: float) -> float:
        """Get pip value for symbol"""
        try:
            symbol_info = mt5.symbol_info(symbol)
            if not symbol_info:
                return 0.0

            # Standard pip value calculation
            if symbol.endswith('JPY'):
                pip_size = 0.01
            else:
                pip_size = 0.0001

            return pip_size * lot_size * symbol_info.trade_contract_size

        except Exception:
            return 0.0

    def _get_pip_size(self, symbol: str) -> float:
        """Get pip size for symbol"""
        if symbol.endswith('JPY'):
            return 0.01
        else:
            return 0.0001

    async def close_position(self, ticket: int) -> bool:
        """Close position by ticket"""
        try:
            if not self.is_connected:
                return False

            # Get position info
            position = mt5.positions_get(ticket=ticket)
            if not position:
                logger.error(f"Position {ticket} not found")
                return False

            pos = position[0]

            # Prepare close request
            if pos.type == mt5.POSITION_TYPE_BUY:
                order_type = mt5.ORDER_TYPE_SELL
                price = mt5.symbol_info_tick(pos.symbol).bid
            else:
                order_type = mt5.ORDER_TYPE_BUY
                price = mt5.symbol_info_tick(pos.symbol).ask

            request = {
                "action": mt5.TRADE_ACTION_DEAL,
                "symbol": pos.symbol,
                "volume": pos.volume,
                "type": order_type,
                "position": ticket,
                "price": price,
                "deviation": 20,
                "magic": 234000,
                "comment": "Auto close",
                "type_time": mt5.ORDER_TIME_GTC,
                "type_filling": mt5.ORDER_FILLING_IOC,
            }

            result = mt5.order_send(request)

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Close position failed: {result.retcode}")
                return False

            logger.info(f"Position {ticket} closed successfully")
            return True

        except Exception as e:
            logger.error(f"Error closing position {ticket}: {e}")
            return False

    async def modify_position(
        self,
        ticket: int,
        stop_loss: Optional[float] = None,
        take_profit: Optional[float] = None
    ) -> bool:
        """Modify position stop loss and take profit"""
        try:
            if not self.is_connected:
                return False

            request = {
                "action": mt5.TRADE_ACTION_SLTP,
                "position": ticket,
                "magic": 234000,
            }

            if stop_loss is not None:
                request["sl"] = stop_loss
            if take_profit is not None:
                request["tp"] = take_profit

            result = mt5.order_send(request)

            if result.retcode != mt5.TRADE_RETCODE_DONE:
                logger.error(f"Modify position failed: {result.retcode}")
                return False

            logger.info(f"Position {ticket} modified successfully")
            return True

        except Exception as e:
            logger.error(f"Error modifying position {ticket}: {e}")
            return False
