#!/usr/bin/env python3
"""
Debug GPT Analysis Error
Test individual components to identify the source of the error
"""
import asyncio
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

async def debug_gpt_components():
    """Debug individual components step by step"""
    print("🔍 Debugging GPT Analysis Components")
    print("=" * 60)
    
    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.services.gpt_analyzer import GPTAnalyzer
        from backend.utils.technical_analysis import TechnicalAnalyzer
        
        # Step 1: Test MT5 Connection
        print("1️⃣ Testing MT5 Connection...")
        connector = MT5Connector()
        success = await connector.connect()
        
        if not success:
            print("❌ MT5 connection failed")
            return False
        
        print("✅ MT5 connected successfully")
        
        # Step 2: Test Market Data
        print("\n2️⃣ Testing Market Data...")
        symbol = "BTCUSDm"
        market_data = await connector.get_market_data(symbol)
        
        if not market_data:
            print(f"❌ No market data for {symbol}")
            await connector.disconnect()
            return False
        
        print(f"✅ Market data: ${market_data.mid_price:,.5f}")
        
        # Step 3: Test Multi-timeframe Data
        print("\n3️⃣ Testing Multi-timeframe Data...")
        timeframe_data = await connector.get_multi_timeframe_data(
            symbol, 
            timeframes=["15m", "30m", "1h", "4h"]
        )
        
        for tf, data in timeframe_data.items():
            print(f"   {tf}: {len(data)} candles")
        
        if not any(timeframe_data.values()):
            print("❌ No timeframe data available")
            await connector.disconnect()
            return False
        
        # Step 4: Test Technical Indicators
        print("\n4️⃣ Testing Technical Indicators...")
        technical_analyzer = TechnicalAnalyzer()
        technical_indicators = {}
        
        for tf, ohlcv_data in timeframe_data.items():
            if ohlcv_data:
                try:
                    indicators = technical_analyzer.calculate_indicators(ohlcv_data)
                    if indicators:
                        technical_indicators[tf] = indicators
                        print(f"   ✅ {tf}: Indicators calculated")
                    else:
                        print(f"   ⚠️ {tf}: No indicators (insufficient data)")
                except Exception as e:
                    print(f"   ❌ {tf}: Error - {e}")
        
        # Step 5: Test GPT Analyzer Initialization
        print("\n5️⃣ Testing GPT Analyzer...")
        try:
            gpt_analyzer = GPTAnalyzer()
            print("✅ GPT Analyzer initialized")
        except Exception as e:
            print(f"❌ GPT Analyzer initialization failed: {e}")
            await connector.disconnect()
            return False
        
        # Step 6: Test Data Preparation
        print("\n6️⃣ Testing Data Preparation...")
        try:
            analysis_data = gpt_analyzer._prepare_multi_timeframe_data(
                symbol, market_data, timeframe_data, technical_indicators
            )
            print(f"✅ Analysis data prepared: {len(analysis_data)} fields")
            print(f"   Timeframes: {list(analysis_data.get('timeframes', {}).keys())}")
        except Exception as e:
            print(f"❌ Data preparation failed: {e}")
            import traceback
            traceback.print_exc()
            await connector.disconnect()
            return False
        
        # Step 7: Test Prompt Creation
        print("\n7️⃣ Testing Prompt Creation...")
        try:
            prompt = gpt_analyzer._create_multi_timeframe_prompt(analysis_data)
            print(f"✅ Prompt created: {len(prompt)} characters")
        except Exception as e:
            print(f"❌ Prompt creation failed: {e}")
            import traceback
            traceback.print_exc()
            await connector.disconnect()
            return False
        
        # Step 8: Test GPT API Call (with timeout)
        print("\n8️⃣ Testing GPT API Call...")
        try:
            import asyncio
            
            # Create a timeout wrapper
            async def gpt_call_with_timeout():
                response = await gpt_analyzer.client.chat.completions.create(
                    model=gpt_analyzer.model,
                    messages=[
                        {
                            "role": "system",
                            "content": gpt_analyzer._get_multi_timeframe_system_prompt()
                        },
                        {
                            "role": "user",
                            "content": prompt[:4000]  # Limit prompt size for testing
                        }
                    ],
                    temperature=0.3,
                    max_tokens=1000  # Reduced for testing
                )
                return response
            
            # Call with 30 second timeout
            response = await asyncio.wait_for(gpt_call_with_timeout(), timeout=30.0)
            print(f"✅ GPT API call successful: {len(response.choices[0].message.content)} characters")
            
            # Step 9: Test Response Parsing
            print("\n9️⃣ Testing Response Parsing...")
            try:
                parsed_result = gpt_analyzer._parse_multi_timeframe_response(
                    response.choices[0].message.content,
                    symbol,
                    market_data
                )
                
                if parsed_result and isinstance(parsed_result, dict):
                    print("✅ Response parsed successfully")
                    print(f"   Decision: {parsed_result.get('decision', 'UNKNOWN')}")
                    print(f"   Confidence: {parsed_result.get('confidence', 0):.2f}")
                else:
                    print("❌ Response parsing returned invalid data")
                    print(f"   Raw response: {response.choices[0].message.content[:200]}...")
                
            except Exception as parse_error:
                print(f"❌ Response parsing failed: {parse_error}")
                print(f"   Raw response: {response.choices[0].message.content[:200]}...")
                import traceback
                traceback.print_exc()
            
        except asyncio.TimeoutError:
            print("❌ GPT API call timed out (30 seconds)")
        except Exception as api_error:
            print(f"❌ GPT API call failed: {api_error}")
            import traceback
            traceback.print_exc()
        
        await connector.disconnect()
        print("\n✅ Debug completed")
        return True
        
    except Exception as e:
        print(f"❌ Debug error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_simple_gpt_call():
    """Test a simple GPT call to verify API connectivity"""
    print("\n🧪 Testing Simple GPT Call")
    print("=" * 40)
    
    try:
        from backend.services.gpt_analyzer import GPTAnalyzer
        
        gpt_analyzer = GPTAnalyzer()
        
        response = await gpt_analyzer.client.chat.completions.create(
            model=gpt_analyzer.model,
            messages=[
                {
                    "role": "user",
                    "content": "Say 'Hello, GPT is working!' in JSON format with a 'message' field."
                }
            ],
            temperature=0.1,
            max_tokens=100
        )
        
        print(f"✅ Simple GPT call successful")
        print(f"Response: {response.choices[0].message.content}")
        return True
        
    except Exception as e:
        print(f"❌ Simple GPT call failed: {e}")
        return False

async def main():
    """Main debug function"""
    print("🔧 GPT ANALYSIS ERROR DEBUG")
    print("=" * 80)
    
    # Test 1: Simple GPT call
    success1 = await test_simple_gpt_call()
    
    # Test 2: Full component debug
    success2 = await debug_gpt_components()
    
    print("\n" + "=" * 80)
    if success1 and success2:
        print("✅ All debug tests passed!")
    else:
        print("❌ Some debug tests failed - check logs above")

if __name__ == "__main__":
    asyncio.run(main())
