#!/usr/bin/env python3
"""
Test order placement with custom symbol selection
"""
import requests
import time

def test_custom_symbol_order(symbol="BTCUSDm", lot_size=0.01):
    """Test order placement with custom symbol"""
    print(f"🎯 TESTING ORDER PLACEMENT FOR {symbol}")
    print("=" * 60)
    
    # Step 1: Start trading engine
    print("\n📋 STEP 1: START TRADING ENGINE")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        if response.status_code == 200:
            print("✅ Trading engine started successfully")
        else:
            print(f"❌ Failed to start: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Wait for initialization
    print("\n⏳ Waiting 3 seconds for initialization...")
    time.sleep(3)
    
    # Step 2: Get account info
    print("\n📋 STEP 2: ACCOUNT INFORMATION")
    try:
        response = requests.get("http://localhost:8001/api/trading/account", timeout=5)
        if response.status_code == 200:
            account = response.json()
            balance = account.get('balance', 0)
            free_margin = account.get('free_margin', 0)
            print(f"✅ Balance: ${balance:.2f}")
            print(f"✅ Free Margin: ${free_margin:.2f}")
            print(f"✅ Leverage: {account.get('leverage', 'Unknown')}")
        else:
            print(f"❌ Account check failed: {response.status_code}")
            free_margin = 16.33  # Default
    except Exception as e:
        print(f"❌ Error: {e}")
        free_margin = 16.33
    
    # Step 3: Get market data for symbol
    print(f"\n📋 STEP 3: MARKET DATA FOR {symbol}")
    try:
        response = requests.get(f"http://localhost:8001/api/trading/market-data/{symbol}", timeout=5)
        if response.status_code == 200:
            market = response.json()
            bid = market.get('bid', 0)
            ask = market.get('ask', 0)
            spread = market.get('spread', 0)
            print(f"✅ Bid: ${bid:.5f}")
            print(f"✅ Ask: ${ask:.5f}")
            print(f"✅ Spread: ${spread:.5f}")
        else:
            print(f"❌ Market data failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Step 4: Calculate margin requirement
    print(f"\n📋 STEP 4: MARGIN CALCULATION FOR {symbol}")
    if symbol == "BTCUSDm":
        approx_price = bid or 108000
        position_value = lot_size * approx_price
        margin_needed = position_value / 10  # 10:1 leverage
    elif symbol == "ETHUSDm":
        approx_price = bid or 4000
        position_value = lot_size * approx_price
        margin_needed = position_value / 10  # 10:1 leverage
    elif symbol == "USDAED":
        approx_price = bid or 3.67
        position_value = lot_size * approx_price
        margin_needed = position_value / 20  # 20:1 leverage
    else:
        approx_price = bid or 1000
        position_value = lot_size * approx_price
        margin_needed = position_value / 10  # Default 10:1 leverage
    
    print(f"📊 Position Analysis:")
    print(f"   Price: ${approx_price:.2f}")
    print(f"   Position Value: ${position_value:.2f}")
    print(f"   Margin Needed: ${margin_needed:.2f}")
    print(f"   Available Margin: ${free_margin:.2f}")
    
    if margin_needed > free_margin:
        print(f"⚠️  WARNING: Insufficient margin!")
        print(f"   Need: ${margin_needed:.2f}, Have: ${free_margin:.2f}")
        print(f"   This order will likely fail due to insufficient margin")
    else:
        print(f"✅ Sufficient margin available")
    
    # Step 5: Create custom order via force analysis
    print(f"\n📋 STEP 5: FORCE ANALYSIS FOR {symbol}")
    print(f"⚠️  ATTEMPTING TO TRIGGER ORDER FOR {symbol}!")
    print(f"📊 Order Details:")
    print(f"   - Symbol: {symbol}")
    print(f"   - Lot Size: {lot_size}")
    print(f"   - Expected Type: BUY/SELL (depends on GPT analysis)")
    
    try:
        # Force analysis to trigger trading signal
        data = {"symbol": symbol}
        response = requests.post("http://localhost:8001/api/trading/analyze", json=data, timeout=15)
        print(f"\n📡 Force Analysis Response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Analysis completed: {result.get('message')}")
            
            # Wait for potential order execution
            print("\n⏳ Waiting 10 seconds for order execution...")
            time.sleep(10)
            
            # Check positions
            print(f"\n📋 STEP 6: CHECK POSITIONS")
            pos_response = requests.get("http://localhost:8001/api/trading/positions", timeout=5)
            if pos_response.status_code == 200:
                positions = pos_response.json()
                print(f"📈 Total positions: {len(positions)}")
                
                # Look for our symbol
                found_position = False
                for pos in positions:
                    if pos.get('symbol') == symbol:
                        print(f"✅ FOUND POSITION FOR {symbol}!")
                        print(f"   Type: {pos.get('order_type')}")
                        print(f"   Lot Size: {pos.get('lot_size')}")
                        print(f"   Entry Price: {pos.get('entry_price')}")
                        found_position = True
                
                if not found_position:
                    print(f"❌ No position found for {symbol}")
                    print("   Possible reasons:")
                    print("   - GPT gave HOLD signal")
                    print("   - Insufficient margin")
                    print("   - Order failed (check backend logs)")
                
                return found_position
            else:
                print(f"❌ Position check failed: {pos_response.status_code}")
                return False
        else:
            print(f"❌ Analysis failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def show_symbol_options():
    """Show available symbol options"""
    print("\n📋 AVAILABLE SYMBOLS FOR TESTING:")
    print("=" * 40)
    
    symbols = [
        {"symbol": "BTCUSDm", "price": "~$108,000", "margin": "~$110 (0.01 lots)"},
        {"symbol": "ETHUSDm", "price": "~$4,000", "margin": "~$4.40 (0.01 lots)"},
        {"symbol": "XAUUSDm", "price": "~$2,000", "margin": "~$2.20 (0.01 lots)"},
        {"symbol": "USDAED", "price": "~$3.67", "margin": "~$0.18 (0.01 lots)"},
        {"symbol": "USDAMD", "price": "~$400", "margin": "~$2.00 (0.01 lots)"},
        {"symbol": "EURUSDm", "price": "~$1.10", "margin": "~$0.11 (0.01 lots)"}
    ]
    
    for i, item in enumerate(symbols, 1):
        print(f"{i}. {item['symbol']:<10} - {item['price']:<12} - Margin: {item['margin']}")
    
    return symbols

if __name__ == "__main__":
    print("🎯 CUSTOM SYMBOL ORDER PLACEMENT TEST")
    print("This will test order placement for your chosen symbol")
    print("=" * 60)
    
    # Show options
    symbols = show_symbol_options()
    
    print(f"\n💰 Your available margin: ~$16.33")
    print(f"✅ Recommended: USDAED, USDAMD, EURUSDm (affordable)")
    print(f"⚠️  BTCUSDm, ETHUSDm: May fail due to insufficient margin")
    
    # Get user choice
    print(f"\n" + "=" * 60)
    symbol_choice = input("Enter symbol to test (default: BTCUSDm): ").strip().upper()
    if not symbol_choice:
        symbol_choice = "BTCUSDm"
    
    lot_choice = input("Enter lot size (default: 0.01): ").strip()
    if not lot_choice:
        lot_size = 0.01
    else:
        try:
            lot_size = float(lot_choice)
        except:
            lot_size = 0.01
    
    print(f"\n🎯 Testing with:")
    print(f"   Symbol: {symbol_choice}")
    print(f"   Lot Size: {lot_size}")
    
    input("\nPress ENTER to continue...")
    
    success = test_custom_symbol_order(symbol_choice, lot_size)
    
    print("\n" + "=" * 60)
    if success:
        print(f"🎉 ORDER TEST SUCCESSFUL FOR {symbol_choice}!")
        print("✅ Position found in account")
        print("✅ System working correctly")
        print("⚠️  Remember to manage your position in MT5")
    else:
        print(f"❌ ORDER TEST FAILED FOR {symbol_choice}")
        print("🔍 Check:")
        print("   - Backend logs for detailed error messages")
        print("   - MT5 terminal for any error notifications")
        print("   - Account margin requirements")
        print("   - GPT analysis results (might be HOLD signal)")
    
    print("\n🌐 Check backend logs and MT5 terminal for details")
