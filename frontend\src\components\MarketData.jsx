import React, { useState, useEffect } from 'react';
import { RefreshCw, TrendingUp, TrendingDown, Activity } from 'lucide-react';
import { tradingApi, formatDateTime, wsService } from '../services/api';

const MarketData = () => {
  const [symbols, setSymbols] = useState([]);
  const [marketData, setMarketData] = useState({});
  const [loading, setLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);

  useEffect(() => {
    loadSymbols();
    
    // Listen for real-time updates
    wsService.addListener('market_update', handleMarketUpdate);
    
    return () => {
      wsService.removeListener('market_update', handleMarketUpdate);
    };
  }, []);

  useEffect(() => {
    if (symbols.length > 0) {
      loadAllMarketData();
      
      // Set up auto-refresh
      if (autoRefresh) {
        const interval = setInterval(loadAllMarketData, 5000); // Refresh every 5 seconds
        return () => clearInterval(interval);
      }
    }
  }, [symbols, autoRefresh]);

  const handleMarketUpdate = (data) => {
    if (data.symbol && data.marketData) {
      setMarketData(prev => ({
        ...prev,
        [data.symbol]: data.marketData
      }));
    }
  };

  const loadSymbols = async () => {
    try {
      const response = await tradingApi.getSymbols();
      setSymbols(response.data.symbols || []);
    } catch (error) {
      console.error('Error loading symbols:', error);
    }
  };

  const loadAllMarketData = async () => {
    if (symbols.length === 0) return;
    
    try {
      setLoading(true);
      
      // Load market data for all symbols
      const promises = symbols.map(symbol => 
        tradingApi.getMarketData(symbol).catch(error => {
          console.error(`Error loading data for ${symbol}:`, error);
          return null;
        })
      );
      
      const responses = await Promise.all(promises);
      
      const newMarketData = {};
      responses.forEach((response, index) => {
        if (response && response.data) {
          newMarketData[symbols[index]] = response.data;
        }
      });
      
      setMarketData(prev => ({ ...prev, ...newMarketData }));
      
    } catch (error) {
      console.error('Error loading market data:', error);
    } finally {
      setLoading(false);
    }
  };

  const calculatePriceChange = (symbol) => {
    const data = marketData[symbol];
    if (!data || !data.previous_close) return null;
    
    const current = (data.bid + data.ask) / 2;
    const change = current - data.previous_close;
    const changePercent = (change / data.previous_close) * 100;
    
    return { change, changePercent };
  };

  const formatPrice = (price, symbol) => {
    // Different symbols have different decimal places
    const decimals = symbol.includes('JPY') ? 3 : 5;
    return price?.toFixed(decimals) || 'N/A';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="card">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-lg font-medium text-gray-900">Market Data</h2>
            <p className="text-sm text-gray-600">
              Real-time market prices and spreads
            </p>
          </div>
          <div className="flex items-center space-x-4">
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh(e.target.checked)}
                className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
              />
              <span className="text-sm text-gray-700">Auto Refresh</span>
            </label>
            <button
              onClick={loadAllMarketData}
              disabled={loading}
              className="btn btn-secondary flex items-center space-x-2"
            >
              <RefreshCw className={`w-4 h-4 ${loading ? 'animate-spin' : ''}`} />
              <span>Refresh</span>
            </button>
          </div>
        </div>
      </div>

      {/* Market Data Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {symbols.map((symbol) => {
          const data = marketData[symbol];
          const priceChange = calculatePriceChange(symbol);
          
          return (
            <div key={symbol} className="card hover:shadow-md transition-shadow">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold text-gray-900">{symbol}</h3>
                <div className="flex items-center space-x-2">
                  {data ? (
                    <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                  ) : (
                    <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
                  )}
                  <Activity className="w-4 h-4 text-gray-400" />
                </div>
              </div>

              {data ? (
                <div className="space-y-3">
                  {/* Bid/Ask */}
                  <div className="grid grid-cols-2 gap-4">
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Bid</p>
                      <p className="text-xl font-semibold text-red-600">
                        {formatPrice(data.bid, symbol)}
                      </p>
                    </div>
                    <div className="text-center">
                      <p className="text-sm text-gray-600">Ask</p>
                      <p className="text-xl font-semibold text-green-600">
                        {formatPrice(data.ask, symbol)}
                      </p>
                    </div>
                  </div>

                  {/* Spread */}
                  <div className="text-center">
                    <p className="text-sm text-gray-600">Spread</p>
                    <p className="text-lg font-medium text-gray-900">
                      {formatPrice(data.spread, symbol)}
                    </p>
                  </div>

                  {/* Price Change */}
                  {priceChange && (
                    <div className="text-center">
                      <div className="flex items-center justify-center space-x-2">
                        {priceChange.changePercent >= 0 ? (
                          <TrendingUp className="w-4 h-4 text-green-600" />
                        ) : (
                          <TrendingDown className="w-4 h-4 text-red-600" />
                        )}
                        <span
                          className={`text-sm font-medium ${
                            priceChange.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}
                        >
                          {priceChange.changePercent >= 0 ? '+' : ''}
                          {priceChange.changePercent.toFixed(2)}%
                        </span>
                      </div>
                    </div>
                  )}

                  {/* Last Update */}
                  <div className="text-center pt-2 border-t border-gray-100">
                    <p className="text-xs text-gray-500">
                      Updated: {formatDateTime(data.timestamp)}
                    </p>
                  </div>
                </div>
              ) : (
                <div className="text-center py-8">
                  <div className="animate-pulse">
                    <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto mb-2"></div>
                    <div className="h-4 bg-gray-200 rounded w-1/2 mx-auto"></div>
                  </div>
                  <p className="text-sm text-gray-500 mt-2">Loading...</p>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Market Summary */}
      {Object.keys(marketData).length > 0 && (
        <div className="card">
          <h3 className="text-lg font-medium text-gray-900 mb-4">Market Summary</h3>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-gray-200">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Symbol
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Bid
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Ask
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Spread
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Change %
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Last Update
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {symbols.map((symbol) => {
                  const data = marketData[symbol];
                  const priceChange = calculatePriceChange(symbol);
                  
                  return (
                    <tr key={symbol} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        {symbol}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-red-600">
                        {data ? formatPrice(data.bid, symbol) : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-green-600">
                        {data ? formatPrice(data.ask, symbol) : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {data ? formatPrice(data.spread, symbol) : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm">
                        {priceChange ? (
                          <span
                            className={`flex items-center space-x-1 ${
                              priceChange.changePercent >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}
                          >
                            {priceChange.changePercent >= 0 ? (
                              <TrendingUp className="w-3 h-3" />
                            ) : (
                              <TrendingDown className="w-3 h-3" />
                            )}
                            <span>
                              {priceChange.changePercent >= 0 ? '+' : ''}
                              {priceChange.changePercent.toFixed(2)}%
                            </span>
                          </span>
                        ) : (
                          <span className="text-gray-500">N/A</span>
                        )}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                        {data ? formatDateTime(data.timestamp) : 'N/A'}
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
};

export default MarketData;
