#!/usr/bin/env python3
"""
Test New GPT Analysis Flow
1. Multi-timeframe analysis (4h, 1h, 30m, 15m) for entry decisions
2. Stop loss and take profit placement with buffer pips
3. Save all analysis and trading results for evaluation
4. Position analysis with different timeframes (30m, 15m, 5m) for trailing stop
5. Include reasoning in all signal logs
"""
import asyncio
import os
from dotenv import load_dotenv
import uuid

# Load environment variables
load_dotenv()

async def test_new_gpt_flow():
    """Test the complete new GPT analysis flow"""
    print("🚀 Testing New GPT Analysis Flow")
    print("=" * 60)

    try:
        from backend.services.mt5_connector import MT5Connector
        from backend.services.gpt_analyzer import GPTAnalyzer

        # Connect to MT5
        connector = MT5Connector()
        success = await connector.connect()

        if not success:
            print("❌ Cannot connect to MT5")
            return False

        # Initialize GPT analyzer
        gpt_analyzer = GPTAnalyzer()

        # Test symbol
        symbol = "BTCUSDm"

        print(f"📊 Testing New Flow for {symbol}...")

        # Step 1: Multi-timeframe data collection
        print("\n1️⃣ Multi-Timeframe Data Collection (4h, 1h, 30m, 15m)")
        timeframe_data = await connector.get_multi_timeframe_data(
            symbol,
            timeframes=["4h", "1h", "30m", "15m"]
        )

        for tf, data in timeframe_data.items():
            print(f"   {tf}: {len(data)} candles")

        if not any(timeframe_data.values()):
            print("❌ No timeframe data available")
            await connector.disconnect()
            return False

        # Get current market data
        market_data = await connector.get_market_data(symbol)
        if not market_data:
            print("❌ No market data available")
            await connector.disconnect()
            return False

        print(f"💰 Current price: ${market_data.mid_price:,.5f}")

        # Step 2: Multi-timeframe GPT analysis for entry decision
        print("\n2️⃣ Multi-Timeframe GPT Analysis for Entry Decision")
        analysis_id = f"test_{uuid.uuid4().hex[:8]}"

        analysis = await gpt_analyzer.analyze_market_multi_timeframe(
            symbol=symbol,
            market_data=market_data,
            timeframe_data=timeframe_data
        )

        if analysis:
            print("✅ Multi-timeframe analysis completed!")

            # Handle both dictionary and GPTAnalysis object
            if hasattr(analysis, 'dict'):
                # It's a Pydantic model
                analysis_dict = analysis.dict()
            else:
                # It's already a dictionary
                analysis_dict = analysis

            print(f"📊 Decision: {analysis_dict.get('decision', 'UNKNOWN')}")
            print(f"🎯 Confidence: {analysis_dict.get('confidence', 0):.2f}")
            print(f"💡 Reasoning: {analysis_dict.get('reasoning', 'No reasoning')[:100]}...")

            # Display timeframe analysis
            if 'timeframe_analysis' in analysis_dict:
                print("📈 Timeframe Analysis:")
                for tf, tf_analysis in analysis_dict['timeframe_analysis'].items():
                    print(f"   {tf}: {tf_analysis[:50]}...")

            # Display key levels with buffer
            if 'key_levels' in analysis_dict:
                levels = analysis_dict['key_levels']
                print(f"🎯 Key Levels:")
                print(f"   Support: ${levels.get('support', 0):,.5f}")
                print(f"   Resistance: ${levels.get('resistance', 0):,.5f}")
                print(f"   Stop Buffer: {levels.get('stop_buffer_pips', 0)} pips")
                print(f"   Target Buffer: {levels.get('target_buffer_pips', 0)} pips")

            # Step 3: Save evaluation
            print("\n3️⃣ Saving Analysis for Evaluation")
            await gpt_analyzer.save_trading_evaluation(
                analysis_id=analysis_id,
                symbol=symbol,
                analysis_result=analysis_dict,
                market_data=market_data,
                timeframe_data=timeframe_data
            )
            print(f"💾 Analysis saved with ID: {analysis_id}")

        else:
            print("❌ Multi-timeframe analysis failed")

        # Step 4: Test position analysis for trailing stop
        print("\n4️⃣ Testing Position Analysis for Trailing Stop")

        # Create mock position
        mock_position = {
            'symbol': symbol,
            'ticket': 12345,
            'order_type': 'BUY',
            'entry_price': market_data.mid_price - 100,  # Profitable position
            'lot_size': 0.01,
            'profit': 50.0,
            'stop_loss': market_data.mid_price - 200,
            'take_profit': market_data.mid_price + 100,
            'open_time': '2024-01-01T10:00:00'
        }

        print(f"📊 Mock Position: {mock_position['type']} {mock_position['lot_size']} {symbol}")
        print(f"💰 Entry: ${mock_position['entry_price']:,.5f}")
        print(f"💵 Current P&L: ${mock_position['profit']:,.2f}")

        # Get position timeframes (30m, 15m, 5m)
        position_timeframes = await connector.get_multi_timeframe_data(
            symbol,
            timeframes=["30m", "15m", "5m"]
        )

        for tf, data in position_timeframes.items():
            print(f"   {tf}: {len(data)} candles")

        # Analyze position for trailing stop
        trailing_analysis = await gpt_analyzer.analyze_position_for_trailing_stop(
            position=mock_position,
            market_data=market_data,
            mt5_connector=connector
        )

        if trailing_analysis:
            print("✅ Position trailing stop analysis completed!")
            print(f"🎯 Decision: {'TRAIL STOP' if trailing_analysis['should_trail'] else 'CONTINUE'}")
            print(f"🎯 Confidence: {trailing_analysis['confidence']:.2f}")
            print(f"📊 Market Outlook: {trailing_analysis.get('market_outlook', 'unknown')}")
            print(f"💡 Reasoning: {trailing_analysis['reasoning'][:100]}...")
        else:
            print("❌ Position analysis failed")

        # Step 5: Test learning context
        print("\n5️⃣ Testing Learning Context")
        learning_context = gpt_analyzer._get_learning_context()
        print(f"📚 Learning Context: {learning_context[:200]}...")

        await connector.disconnect()
        return True

    except Exception as e:
        print(f"❌ Test error: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_evaluation_system():
    """Test the evaluation and learning system"""
    print("\n🧠 Testing Evaluation and Learning System")
    print("=" * 60)

    try:
        from backend.services.gpt_analyzer import GPTAnalyzer

        gpt_analyzer = GPTAnalyzer()

        # Test saving evaluation
        test_analysis_id = f"eval_test_{uuid.uuid4().hex[:8]}"

        # Mock analysis result
        mock_analysis = {
            "decision": "ENTRY_BUY",
            "confidence": 0.85,
            "reasoning": "Strong bullish confluence across all timeframes",
            "entry_price": 108500.0,
            "stop_loss": 108000.0,
            "take_profit": 109500.0
        }

        # Mock market data
        from backend.models.trading_models import MarketData
        from datetime import datetime

        mock_market_data = MarketData(
            symbol="BTCUSDm",
            timestamp=datetime.now(),
            bid=108450.0,
            ask=108550.0,
            spread=100.0
        )

        # Mock timeframe data
        mock_timeframe_data = {
            "4h": [],
            "1h": [],
            "30m": [],
            "15m": []
        }

        print(f"💾 Saving test evaluation: {test_analysis_id}")
        await gpt_analyzer.save_trading_evaluation(
            analysis_id=test_analysis_id,
            symbol="BTCUSDm",
            analysis_result=mock_analysis,
            market_data=mock_market_data,
            timeframe_data=mock_timeframe_data
        )

        # Test updating trade outcome
        print("📊 Updating trade outcome...")
        mock_outcome = {
            "ticket": 12345,
            "symbol": "BTCUSDm",
            "profit": 75.50,
            "close_reason": "Take profit hit",
            "close_time": datetime.now().isoformat()
        }

        await gpt_analyzer.update_trade_outcome(test_analysis_id, mock_outcome)

        # Test learning context
        print("📚 Testing learning context...")
        context = gpt_analyzer._get_learning_context()
        print(f"Learning context length: {len(context)} characters")

        print("✅ Evaluation system test completed!")
        return True

    except Exception as e:
        print(f"❌ Evaluation test error: {e}")
        return False

async def main():
    """Main test function"""
    print("🧪 NEW GPT ANALYSIS FLOW TEST")
    print("=" * 80)

    # Test 1: Complete new flow
    success1 = await test_new_gpt_flow()

    # Test 2: Evaluation system
    success2 = await test_evaluation_system()

    print("\n" + "=" * 80)
    if success1 and success2:
        print("✅ All new GPT flow tests passed!")
        print("\n🎉 NEW FEATURES IMPLEMENTED:")
        print("   ✅ Multi-timeframe analysis (4h, 1h, 30m, 15m)")
        print("   ✅ Single decision after analyzing all timeframes")
        print("   ✅ Stop/TP placement with buffer pips")
        print("   ✅ Analysis evaluation and learning system")
        print("   ✅ Position analysis with different timeframes (30m, 15m, 5m)")
        print("   ✅ Detailed reasoning in all logs")
    else:
        print("❌ Some tests failed")

if __name__ == "__main__":
    asyncio.run(main())
