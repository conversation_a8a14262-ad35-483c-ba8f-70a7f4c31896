"""
Main Trading Engine
Orchestrates market analysis, signal generation, and trade execution
"""
import asyncio
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
from loguru import logger

from ..models.trading_models import (
    MarketData, OHLCV, Position, TradingSignal, GPTAnalysis,
    OrderRequest, OrderType, SignalType, SystemStatus, AccountInfo, RiskLevel
)
from ..services.mt5_connector import MT5Connector
from ..services.gpt_analyzer import GPTAnalyzer
from ..utils.technical_analysis import TechnicalAnalyzer
from ..utils.risk_management import RiskManager
from ..config import settings


class TradingEngine:
    """Main trading engine that coordinates all trading operations"""

    def __init__(self):
        self.mt5_connector = MT5Connector()
        self.gpt_analyzer = GPTAnalyzer()
        self.technical_analyzer = TechnicalAnalyzer()
        self.risk_manager = RiskManager()

        self.is_running = False
        self.system_status = SystemStatus()
        self.active_signals: Dict[str, TradingSignal] = {}
        self.positions: List[Position] = []
        self.account_info: Optional[AccountInfo] = None

        # Analysis cache
        self.market_data_cache: Dict[str, MarketData] = {}
        self.ohlcv_cache: Dict[str, List[OHLCV]] = {}
        self.last_analysis_time: Dict[str, datetime] = {}

    async def initialize(self) -> bool:
        """Initialize the trading engine"""
        try:
            logger.info("Initializing Trading Engine...")

            # Connect to MT5
            if not await self.mt5_connector.connect():
                logger.error("Failed to connect to MT5")
                return False

            self.system_status.is_mt5_connected = True

            # Get account info
            self.account_info = await self.mt5_connector.get_account_info()
            if not self.account_info:
                logger.error("Failed to get account info")
                return False

            # Test GPT connection
            try:
                test_symbols = settings.symbols_list[:1]  # Test with one symbol
                await self.gpt_analyzer.get_market_sentiment(test_symbols)
                self.system_status.is_gpt_available = True
            except Exception as e:
                logger.warning(f"GPT connection test failed: {e}")
                self.system_status.is_gpt_available = False

            # Load existing positions
            self.positions = await self.mt5_connector.get_positions()
            self.system_status.active_positions = len(self.positions)

            self.system_status.system_health = "HEALTHY"
            logger.info("Trading Engine initialized successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to initialize trading engine: {e}")
            self.system_status.system_health = "ERROR"
            self.system_status.error_messages.append(str(e))
            return False

    async def start(self):
        """Start the trading engine"""
        if not await self.initialize():
            logger.error("Cannot start trading engine - initialization failed")
            return

        self.is_running = True
        logger.info("Trading Engine started")

        # Start main trading loop
        await self._main_trading_loop()

    async def stop(self):
        """Stop the trading engine"""
        self.is_running = False
        await self.mt5_connector.disconnect()
        logger.info("Trading Engine stopped")

    async def _main_trading_loop(self):
        """Main trading loop"""
        while self.is_running:
            try:
                # Update system status
                await self._update_system_status()

                # Process each active symbol
                for symbol in settings.active_symbols_list:
                    if not self.is_running:
                        break

                    await self._process_symbol(symbol)

                # Manage existing positions
                await self._manage_positions()

                # Wait before next iteration
                await asyncio.sleep(60)  # 1 minute between cycles

            except Exception as e:
                logger.error(f"Error in main trading loop: {e}")
                self.system_status.error_messages.append(str(e))
                await asyncio.sleep(30)  # Wait before retrying

    async def _process_symbol(self, symbol: str):
        """
        Process trading logic for a single symbol with NEW INTELLIGENT FLOW:
        1. No position → GPT analysis for entry
        2. Position with loss → SKIP analysis (wait for profit)
        3. Position with profit → GPT analysis for trailing stop decision
        """
        try:
            # Check if it's time for analysis
            if not self._should_analyze_symbol(symbol):
                return

            logger.info(f"🔍 Processing symbol: {symbol}")

            # Get market data
            market_data = await self.mt5_connector.get_market_data(symbol)
            if not market_data:
                logger.warning(f"No market data for {symbol}")
                return

            self.market_data_cache[symbol] = market_data

            # 🔍 CHECK EXISTING POSITION STATUS
            existing_position = self._get_position_by_symbol(symbol)

            if not existing_position:
                # 1️⃣ NO POSITION → Analyze for entry decision
                logger.info(f"📊 No position for {symbol} - analyzing for entry decision")
                await self._analyze_for_entry_decision(symbol, market_data)

            elif existing_position.profit < 0:
                # 2️⃣ POSITION WITH LOSS → SKIP analysis (wait for profit)
                logger.info(f"⏸️ Position {existing_position.ticket} for {symbol} has loss (${existing_position.profit:.2f}) - SKIPPING analysis")
                return

            else:
                # 3️⃣ POSITION WITH PROFIT → Analyze for trailing stop decision
                logger.info(f"💰 Position {existing_position.ticket} for {symbol} has profit (${existing_position.profit:.2f}) - analyzing for trailing stop")
                await self._analyze_for_trailing_stop_decision(symbol, market_data, existing_position)

            self.last_analysis_time[symbol] = datetime.now()

        except Exception as e:
            logger.error(f"Error processing symbol {symbol}: {e}")

    async def _analyze_for_entry_decision(self, symbol: str, market_data: MarketData):
        """Analyze symbol for entry decision when no position exists"""
        try:
            # Get multi-timeframe OHLCV data for enhanced analysis
            timeframe_data = await self.mt5_connector.get_multi_timeframe_data(
                symbol,
                timeframes=["15m", "30m", "1h", "4h"]
            )

            if not any(timeframe_data.values()):
                logger.warning(f"No multi-timeframe data for {symbol}")
                return

            # Cache the 15m data for position management
            self.ohlcv_cache[symbol] = timeframe_data.get("15m", [])

            # Calculate technical indicators for each timeframe
            technical_indicators = {}
            for tf, ohlcv_data in timeframe_data.items():
                if ohlcv_data:
                    try:
                        indicators = self.technical_analyzer.calculate_indicators(ohlcv_data)
                        if indicators:
                            technical_indicators[tf] = indicators
                        else:
                            logger.warning(f"⚠️ No technical indicators calculated for {symbol} {tf}")
                    except Exception as indicator_error:
                        logger.warning(f"⚠️ Error calculating technical indicators for {symbol} {tf}: {indicator_error}")

            # Perform GPT multi-timeframe analysis for entry decision
            if self.system_status.is_gpt_available:
                analysis_id = f"entry_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                try:
                    gpt_analysis = await self.gpt_analyzer.analyze_market_multi_timeframe(
                        symbol=symbol,
                        market_data=market_data,
                        timeframe_data=timeframe_data,
                        technical_indicators=technical_indicators,
                        context="entry_decision"  # Context for entry analysis
                    )

                    if gpt_analysis and isinstance(gpt_analysis, dict):
                        await self._process_gpt_multi_timeframe_analysis(
                            symbol, gpt_analysis, market_data, timeframe_data, analysis_id
                        )
                    else:
                        logger.warning(f"⚠️ GPT entry analysis failed for {symbol}")

                except Exception as gpt_error:
                    logger.error(f"❌ GPT entry analysis error for {symbol}: {gpt_error}")

        except Exception as e:
            logger.error(f"Error in entry analysis for {symbol}: {e}")

    async def _analyze_for_trailing_stop_decision(self, symbol: str, market_data: MarketData, position: Position):
        """Analyze symbol for trailing stop decision when position has profit"""
        try:
            # Get multi-timeframe data for trailing stop analysis
            timeframe_data = await self.mt5_connector.get_multi_timeframe_data(
                symbol,
                timeframes=["15m", "30m", "1h"]  # Shorter timeframes for trailing stop
            )

            if not any(timeframe_data.values()):
                logger.warning(f"No multi-timeframe data for trailing stop analysis of {symbol}")
                return

            # Calculate technical indicators
            technical_indicators = {}
            for tf, ohlcv_data in timeframe_data.items():
                if ohlcv_data:
                    try:
                        indicators = self.technical_analyzer.calculate_indicators(ohlcv_data)
                        if indicators:
                            technical_indicators[tf] = indicators
                    except Exception as indicator_error:
                        logger.warning(f"⚠️ Error calculating indicators for {symbol} {tf}: {indicator_error}")

            # Perform GPT analysis for trailing stop decision
            if self.system_status.is_gpt_available:
                analysis_id = f"trail_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                try:
                    gpt_analysis = await self.gpt_analyzer.analyze_market_multi_timeframe(
                        symbol=symbol,
                        market_data=market_data,
                        timeframe_data=timeframe_data,
                        technical_indicators=technical_indicators,
                        context="trailing_stop_decision"  # Context for trailing stop analysis
                    )

                    if gpt_analysis and isinstance(gpt_analysis, dict):
                        await self._process_trailing_stop_analysis(
                            symbol, gpt_analysis, market_data, position, analysis_id
                        )
                    else:
                        logger.warning(f"⚠️ GPT trailing stop analysis failed for {symbol}")

                except Exception as gpt_error:
                    logger.error(f"❌ GPT trailing stop analysis error for {symbol}: {gpt_error}")

        except Exception as e:
            logger.error(f"Error in trailing stop analysis for {symbol}: {e}")

    async def _process_trailing_stop_analysis(
        self,
        symbol: str,
        gpt_analysis: Dict[str, Any],
        market_data: MarketData,
        position: Position,
        analysis_id: str
    ):
        """
        Process GPT trailing stop analysis with NEW LOGIC:
        - Confidence ≥ 0.6 (harga akan lanjut) → HOLD posisi
        - Confidence < 0.6 (tidak yakin) → TRAIL STOP
        """
        try:
            confidence = gpt_analysis.get('confidence', 0.0)
            reasoning = gpt_analysis.get('reasoning', 'No reasoning provided')
            decision = gpt_analysis.get('decision', 'HOLD')

            logger.info(f"🧠 GPT Trailing Stop Analysis for {symbol}:")
            logger.info(f"   💰 Position Profit: ${position.profit:.2f}")
            logger.info(f"   🎯 Confidence: {confidence:.2f}")
            logger.info(f"   📊 Decision: {decision}")
            logger.info(f"   💡 Reasoning: {reasoning[:100]}...")

            # NEW TRAILING STOP LOGIC:
            # Confidence ≥ 0.6 → HOLD (harga akan lanjut)
            # Confidence < 0.6 → TRAIL STOP (tidak yakin)

            if confidence >= 0.6:
                # HIGH CONFIDENCE → HOLD position (harga akan lanjut)
                logger.info(f"✅ HIGH confidence ({confidence:.2f}) - HOLDING position {position.ticket}")
                logger.info(f"💡 GPT believes price will continue in favorable direction")
                return
            else:
                # LOW CONFIDENCE → TRAIL STOP (tidak yakin harga akan lanjut)
                logger.info(f"⚠️ LOW confidence ({confidence:.2f}) - EXECUTING TRAIL STOP for position {position.ticket}")
                logger.info(f"💡 GPT uncertain about price continuation - securing profit")

                # Close position due to low confidence
                success = await self.mt5_connector.close_position(position.ticket)

                if success:
                    # Update trade outcome for evaluation
                    if hasattr(self, 'position_analysis_map') and position.ticket in self.position_analysis_map:
                        original_analysis_id = self.position_analysis_map[position.ticket]
                        outcome = {
                            "ticket": position.ticket,
                            "symbol": position.symbol,
                            "profit": position.profit or 0,
                            "close_reason": f"GPT trailing stop (low confidence: {confidence:.2f})",
                            "close_time": datetime.now().isoformat()
                        }
                        await self.gpt_analyzer.update_trade_outcome(original_analysis_id, outcome)
                        del self.position_analysis_map[position.ticket]

                    # Remove from active signals
                    self.active_signals.pop(position.symbol, None)
                    logger.info(f"✅ Position {position.ticket} closed via trailing stop")
                    logger.info(f"💰 Final P&L: ${position.profit or 0:.2f}")

                    # 🚀 IMMEDIATE RE-ANALYSIS AFTER TRAILING STOP
                    logger.info(f"🧠 Triggering immediate re-analysis for {symbol} after trailing stop")
                    await self._immediate_reanalysis_after_close(symbol, market_data)
                else:
                    logger.error(f"❌ Failed to close position {position.ticket} via trailing stop")

        except Exception as e:
            logger.error(f"Error processing trailing stop analysis for {symbol}: {e}")

    async def _process_gpt_analysis(
        self,
        symbol: str,
        gpt_analysis: GPTAnalysis,
        market_data: MarketData
    ):
        """Process GPT analysis and generate trading signals"""
        try:
            # Skip if signal is HOLD or confidence is too low
            if gpt_analysis.signal == SignalType.HOLD or gpt_analysis.confidence < 0.6:
                logger.info(f"Skipping {symbol} - Signal: {gpt_analysis.signal}, Confidence: {gpt_analysis.confidence}")
                return

            # Check if we already have a position for this symbol
            existing_position = self._get_position_by_symbol(symbol)
            if existing_position:
                logger.info(f"Already have position for {symbol}")
                return

            # Determine entry price
            entry_price = gpt_analysis.entry_price or market_data.mid_price

            # Determine order type for stop loss calculation
            order_type = OrderType.BUY if gpt_analysis.signal == SignalType.BUY else OrderType.SELL

            # PRIORITIZE GPT STOP LOSS/TAKE PROFIT VALUES
            stop_loss = gpt_analysis.stop_loss
            take_profit = gpt_analysis.take_profit

            # Only use manual calculation as FALLBACK if GPT doesn't provide values
            if not stop_loss or not take_profit:
                logger.warning(f"GPT didn't provide stop levels for {symbol}, using manual calculation as fallback")
                stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
                    symbol, entry_price, order_type
                )
                logger.info(f"Manual fallback stops for {symbol}: SL={stop_loss:.2f}, TP={take_profit:.2f}")
            else:
                # Trust GPT values completely
                logger.info(f"Using GPT stop levels for {symbol}: SL={stop_loss:.2f}, TP={take_profit:.2f}")

                # PROPER VALIDATION FOR SYMBOL-SPECIFIC MINIMUM DISTANCES
                if stop_loss <= 0 or take_profit <= 0:
                    logger.error(f"Invalid GPT stop levels for {symbol} (SL: {stop_loss}, TP: {take_profit}), using fallback")
                    stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
                        symbol, entry_price, order_type
                    )
                else:
                    # Symbol-specific minimum distance validation
                    sl_distance = abs(stop_loss - entry_price)
                    tp_distance = abs(take_profit - entry_price)

                    # Define minimum distances based on symbol
                    if symbol.startswith('BTC'):
                        min_sl_distance = 500.0  # $500 minimum for BTC
                        min_tp_distance = 1000.0  # $1000 minimum for BTC
                    elif symbol.startswith('ETH'):
                        min_sl_distance = 20.0   # $20 minimum for ETH
                        min_tp_distance = 40.0   # $40 minimum for ETH
                    else:
                        min_sl_distance = 10.0   # $10 minimum for other symbols
                        min_tp_distance = 20.0   # $20 minimum for other symbols

                    # Check if GPT distances are too small
                    if sl_distance < min_sl_distance or tp_distance < min_tp_distance:
                        logger.error(f"GPT stop levels too small for {symbol} (SL: {sl_distance:.2f}, TP: {tp_distance:.2f}), minimum required: SL={min_sl_distance}, TP={min_tp_distance}")
                        stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
                            symbol, entry_price, order_type
                        )
                        logger.info(f"Using fallback stops for {symbol}: SL={stop_loss:.2f}, TP={take_profit:.2f}")
                    else:
                        logger.info(f"GPT stop levels validated for {symbol}: SL distance={sl_distance:.2f}, TP distance={tp_distance:.2f}")

            # Create trading signal
            signal = TradingSignal(
                symbol=symbol,
                signal_type=gpt_analysis.signal,
                timestamp=datetime.now(),
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                lot_size=0.0,  # Will be calculated
                risk_level=gpt_analysis.risk_level,
                confidence=gpt_analysis.confidence,
                reasoning=gpt_analysis.reasoning
            )

            # Calculate position size
            if signal.stop_loss:
                signal.lot_size = self.risk_manager.calculate_position_size(
                    symbol,
                    self.account_info,
                    signal.entry_price,
                    signal.stop_loss
                )
            else:
                signal.lot_size = settings.default_lot_size

            # Execute signal
            await self._execute_signal(signal)

        except Exception as e:
            logger.error(f"Error processing GPT analysis for {symbol}: {e}")

    async def _process_gpt_multi_timeframe_analysis(
        self,
        symbol: str,
        gpt_analysis: Dict[str, Any],
        market_data: MarketData,
        timeframe_data: Dict[str, List[OHLCV]],
        analysis_id: str
    ):
        """Process GPT multi-timeframe analysis and generate trading signals"""
        try:
            # Validate gpt_analysis is not None and is a dictionary
            if not gpt_analysis or not isinstance(gpt_analysis, dict):
                logger.error(f"❌ Invalid GPT analysis data for {symbol}: {type(gpt_analysis)}")
                return

            decision = gpt_analysis.get('decision', 'HOLD')
            confidence = gpt_analysis.get('confidence', 0.0)
            reasoning = gpt_analysis.get('reasoning', 'No reasoning provided')

            # Enhanced logging with detailed reasoning
            logger.info(f"🧠 GPT Multi-Timeframe Analysis for {symbol}:")
            logger.info(f"   📊 Decision: {decision}")
            logger.info(f"   🎯 Confidence: {confidence:.2f}")
            logger.info(f"   💡 Reasoning: {reasoning}")

            # Log timeframe analysis
            if 'timeframe_analysis' in gpt_analysis:
                logger.info(f"   📈 Timeframe Analysis:")
                for tf, analysis in gpt_analysis['timeframe_analysis'].items():
                    logger.info(f"      {tf}: {analysis}")

            # Save evaluation for learning
            await self.gpt_analyzer.save_trading_evaluation(
                analysis_id=analysis_id,
                symbol=symbol,
                analysis_result=gpt_analysis,
                market_data=market_data,
                timeframe_data=timeframe_data
            )

            # Skip if decision is HOLD or confidence is too low
            if decision == 'HOLD' or confidence < 0.6:
                logger.info(f"⏸️ Skipping {symbol} - Decision: {decision}, Confidence: {confidence:.2f}")
                return

            # Check if we already have a position for this symbol
            existing_position = self._get_position_by_symbol(symbol)
            if existing_position:
                logger.info(f"📊 Already have position for {symbol}")
                return

            # Create trading signal from GPT analysis
            if decision == 'ENTRY_BUY':
                signal_type = SignalType.BUY
            elif decision == 'ENTRY_SELL':
                signal_type = SignalType.SELL
            else:
                logger.warning(f"Unknown decision type: {decision}")
                return

            # Safe risk level parsing
            risk_level_str = gpt_analysis.get('risk_level', 'MEDIUM').upper()
            try:
                risk_level = RiskLevel(risk_level_str)
            except ValueError:
                logger.warning(f"Invalid risk level '{risk_level_str}' for {symbol}, defaulting to MEDIUM")
                risk_level = RiskLevel.MEDIUM

            # Determine entry price
            entry_price = gpt_analysis.get('entry_price', market_data.mid_price)

            # Determine order type for stop loss calculation
            order_type = OrderType.BUY if signal_type == SignalType.BUY else OrderType.SELL

            # PRIORITIZE GPT STOP LOSS/TAKE PROFIT VALUES
            stop_loss = gpt_analysis.get('stop_loss')
            take_profit = gpt_analysis.get('take_profit')

            # Only use manual calculation as FALLBACK if GPT doesn't provide values
            if not stop_loss or not take_profit:
                logger.warning(f"GPT didn't provide stop levels for {symbol}, using manual calculation as fallback")
                stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
                    symbol, entry_price, order_type
                )
                logger.info(f"Manual fallback stops for {symbol}: SL={stop_loss:.2f}, TP={take_profit:.2f}")
            else:
                # Trust GPT values completely
                logger.info(f"Using GPT stop levels for {symbol}: SL={stop_loss:.2f}, TP={take_profit:.2f}")

                # PROPER VALIDATION FOR SYMBOL-SPECIFIC MINIMUM DISTANCES
                if stop_loss <= 0 or take_profit <= 0:
                    logger.error(f"Invalid GPT stop levels for {symbol} (SL: {stop_loss}, TP: {take_profit}), using fallback")
                    stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
                        symbol, entry_price, order_type
                    )
                else:
                    # Symbol-specific minimum distance validation
                    sl_distance = abs(stop_loss - entry_price)
                    tp_distance = abs(take_profit - entry_price)

                    # Define minimum distances based on symbol
                    if symbol.startswith('BTC'):
                        min_sl_distance = 500.0  # $500 minimum for BTC
                        min_tp_distance = 1000.0  # $1000 minimum for BTC
                    elif symbol.startswith('ETH'):
                        min_sl_distance = 20.0   # $20 minimum for ETH
                        min_tp_distance = 40.0   # $40 minimum for ETH
                    else:
                        min_sl_distance = 10.0   # $10 minimum for other symbols
                        min_tp_distance = 20.0   # $20 minimum for other symbols

                    # Check if GPT distances are too small
                    if sl_distance < min_sl_distance or tp_distance < min_tp_distance:
                        logger.error(f"GPT stop levels too small for {symbol} (SL: {sl_distance:.2f}, TP: {tp_distance:.2f}), minimum required: SL={min_sl_distance}, TP={min_tp_distance}")
                        stop_loss, take_profit = self.risk_manager.calculate_stop_loss_take_profit(
                            symbol, entry_price, order_type
                        )
                        logger.info(f"Using fallback stops for {symbol}: SL={stop_loss:.2f}, TP={take_profit:.2f}")
                    else:
                        logger.info(f"GPT stop levels validated for {symbol}: SL distance={sl_distance:.2f}, TP distance={tp_distance:.2f}")

            signal = TradingSignal(
                symbol=symbol,
                signal_type=signal_type,
                timestamp=datetime.now(),
                entry_price=entry_price,
                stop_loss=stop_loss,
                take_profit=take_profit,
                lot_size=0.0,  # Will be calculated
                risk_level=risk_level,
                confidence=confidence,
                reasoning=reasoning
            )

            # Calculate position size
            if signal.stop_loss:
                signal.lot_size = self.risk_manager.calculate_position_size(
                    symbol,
                    self.account_info,
                    signal.entry_price,
                    signal.stop_loss
                )
            else:
                signal.lot_size = settings.default_lot_size

            # Execute signal
            await self._execute_signal_with_evaluation(signal, analysis_id)

        except Exception as e:
            logger.error(f"Error processing GPT multi-timeframe analysis for {symbol}: {e}")

    async def _execute_signal(self, signal: TradingSignal):
        """Execute a trading signal"""
        try:
            # Determine order type
            if signal.signal_type == SignalType.BUY:
                order_type = OrderType.BUY
            else:
                order_type = OrderType.SELL

            # Create order request with safe comment
            safe_comment = "GPT Signal"  # Keep it simple to avoid MT5 comment errors

            order_request = OrderRequest(
                symbol=signal.symbol,
                order_type=order_type,
                lot_size=signal.lot_size,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                comment=safe_comment,
                deviation=100  # Increase deviation for crypto volatility
            )

            # Validate order
            is_valid, validation_message = self.risk_manager.validate_order(
                order_request,
                self.account_info,
                self.positions,
                self.market_data_cache[signal.symbol]
            )

            if not is_valid:
                logger.warning(f"Order validation failed for {signal.symbol}: {validation_message}")
                return

            # Place order
            ticket = await self.mt5_connector.place_order(order_request)

            if ticket:
                logger.info(f"Order placed successfully for {signal.symbol}: Ticket {ticket}")
                self.active_signals[signal.symbol] = signal

                # Update positions
                self.positions = await self.mt5_connector.get_positions()
                self.system_status.active_positions = len(self.positions)
            else:
                logger.error(f"Failed to place order for {signal.symbol}")

        except Exception as e:
            logger.error(f"Error executing signal for {signal.symbol}: {e}")

    async def _execute_signal_with_evaluation(self, signal: TradingSignal, analysis_id: str):
        """Execute a trading signal and track for evaluation"""
        try:
            # Determine order type
            if signal.signal_type == SignalType.BUY:
                order_type = OrderType.BUY
            else:
                order_type = OrderType.SELL

            # Create order request with safe comment
            safe_comment = f"GPT-{analysis_id[:8]}"  # Include analysis ID for tracking

            order_request = OrderRequest(
                symbol=signal.symbol,
                order_type=order_type,
                lot_size=signal.lot_size,
                stop_loss=signal.stop_loss,
                take_profit=signal.take_profit,
                comment=safe_comment,
                deviation=100  # Increase deviation for crypto volatility
            )

            # Enhanced logging with reasoning
            logger.info(f"🎯 Executing Signal for {signal.symbol}:")
            logger.info(f"   📊 Type: {signal.signal_type}")
            logger.info(f"   💰 Entry: ${signal.entry_price:,.5f}")
            logger.info(f"   🛡️ Stop Loss: ${signal.stop_loss:,.5f}")
            logger.info(f"   🎯 Take Profit: ${signal.take_profit:,.5f}")
            logger.info(f"   📏 Lot Size: {signal.lot_size}")
            logger.info(f"   💡 Reasoning: {signal.reasoning}")

            # Validate order
            is_valid, validation_message = self.risk_manager.validate_order(
                order_request,
                self.account_info,
                self.positions,
                self.market_data_cache[signal.symbol]
            )

            if not is_valid:
                logger.warning(f"❌ Order validation failed for {signal.symbol}: {validation_message}")
                return

            # Place order
            ticket = await self.mt5_connector.place_order(order_request)

            if ticket:
                logger.info(f"✅ Order placed successfully for {signal.symbol}: Ticket {ticket}")
                self.active_signals[signal.symbol] = signal

                # Update positions
                self.positions = await self.mt5_connector.get_positions()
                self.system_status.active_positions = len(self.positions)

                # Store analysis ID for later evaluation
                if not hasattr(self, 'position_analysis_map'):
                    self.position_analysis_map = {}
                self.position_analysis_map[ticket] = analysis_id

            else:
                logger.error(f"❌ Failed to place order for {signal.symbol}")

        except Exception as e:
            logger.error(f"Error executing signal with evaluation for {signal.symbol}: {e}")

    async def _manage_positions(self):
        """Manage existing positions with GPT-powered trailing stop analysis"""
        try:
            # Update positions from MT5 with real-time P&L calculation
            self.positions = await self.mt5_connector.get_positions(calculate_realtime_pnl=True)
            self.system_status.active_positions = len(self.positions)

            for position in self.positions:
                # Get current market data
                market_data = self.market_data_cache.get(position.symbol)
                if not market_data:
                    continue

                # Check if position should be closed with GPT multi-timeframe analysis
                should_close, reason = await self.risk_manager.should_close_position(
                    position,
                    market_data,
                    self.account_info,
                    gpt_analyzer=self.gpt_analyzer if self.system_status.is_gpt_available else None,
                    mt5_connector=self.mt5_connector if self.system_status.is_gpt_available else None
                )

                if should_close:
                    logger.info(f"🔄 Closing position {position.ticket} for {position.symbol}: {reason}")
                    success = await self.mt5_connector.close_position(position.ticket)

                    if success:
                        # Update trade outcome for evaluation
                        if hasattr(self, 'position_analysis_map') and position.ticket in self.position_analysis_map:
                            analysis_id = self.position_analysis_map[position.ticket]
                            outcome = {
                                "ticket": position.ticket,
                                "symbol": position.symbol,
                                "profit": position.profit or 0,
                                "close_reason": reason,
                                "close_time": datetime.now().isoformat()
                            }
                            await self.gpt_analyzer.update_trade_outcome(analysis_id, outcome)
                            del self.position_analysis_map[position.ticket]

                        # Remove from active signals
                        self.active_signals.pop(position.symbol, None)
                        logger.info(f"✅ Position {position.ticket} closed successfully")
                        logger.info(f"💰 Final P&L: ${position.profit or 0:.2f}")

                        # 🚀 IMMEDIATE RE-ANALYSIS AFTER TRAILING STOP CLOSE
                        if "trailing stop" in reason.lower() or "gpt" in reason.lower():
                            logger.info(f"🧠 Triggering immediate re-analysis for {position.symbol} after trailing stop close")
                            await self._immediate_reanalysis_after_close(position.symbol, market_data)
                    else:
                        logger.error(f"❌ Failed to close position {position.ticket}")

        except Exception as e:
            logger.error(f"Error managing positions: {e}")

    async def _immediate_reanalysis_after_close(self, symbol: str, market_data: MarketData):
        """
        Immediate multi-timeframe re-analysis after trailing stop close
        Analyzes 4h, 1h, 30m, 15m to decide: SELL/BUY/HOLD
        """
        try:
            logger.info(f"🔍 Starting immediate re-analysis for {symbol} after position close")

            # Get fresh multi-timeframe data (4h, 1h, 30m, 15m)
            reanalysis_timeframes = ["4h", "1h", "30m", "15m"]
            timeframe_data = await self.mt5_connector.get_multi_timeframe_data(
                symbol, timeframes=reanalysis_timeframes
            )

            if not timeframe_data:
                logger.warning(f"⚠️ No timeframe data available for immediate re-analysis of {symbol}")
                return

            # Calculate technical indicators for each timeframe
            technical_indicators = {}
            for timeframe, ohlcv_data in timeframe_data.items():
                if ohlcv_data is not None and len(ohlcv_data) > 0:
                    indicators = self.mt5_connector.calculate_indicators(ohlcv_data)
                    if indicators:
                        technical_indicators[timeframe] = indicators
                        logger.info(f"📊 {timeframe}: Technical indicators calculated for re-analysis")
                    else:
                        logger.warning(f"⚠️ {timeframe}: No technical indicators for re-analysis")

            # Perform GPT multi-timeframe analysis for re-entry decision
            if self.system_status.is_gpt_available:
                analysis_id = f"reentry_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

                logger.info(f"🧠 Performing GPT re-analysis for {symbol} with timeframes: {list(timeframe_data.keys())}")

                gpt_analysis = await self.gpt_analyzer.analyze_market_multi_timeframe(
                    symbol=symbol,
                    market_data=market_data,
                    timeframe_data=timeframe_data,
                    technical_indicators=technical_indicators,
                    context="immediate_reentry_after_close"  # Special context for re-entry analysis
                )

                if gpt_analysis and isinstance(gpt_analysis, dict):
                    decision = gpt_analysis.get('decision', 'HOLD')
                    confidence = gpt_analysis.get('confidence', 0.0)
                    reasoning = gpt_analysis.get('reasoning', 'No reasoning provided')

                    logger.info(f"🎯 GPT re-analysis result for {symbol}:")
                    logger.info(f"   Decision: {decision}")
                    logger.info(f"   Confidence: {confidence:.2f}")
                    logger.info(f"   Reasoning: {reasoning[:100]}...")

                    # Process the re-analysis result
                    if decision in ['ENTRY_SELL', 'ENTRY_BUY'] and confidence >= 0.6:
                        logger.info(f"🚀 Executing immediate re-entry for {symbol}: {decision}")
                        await self._process_gpt_multi_timeframe_analysis(
                            symbol, gpt_analysis, market_data, timeframe_data, analysis_id
                        )
                    elif decision == 'HOLD':
                        logger.info(f"⏸️ GPT recommends HOLD for {symbol} after re-analysis")
                    else:
                        logger.info(f"⚠️ GPT re-analysis confidence too low for {symbol}: {confidence:.2f}")
                else:
                    logger.warning(f"⚠️ GPT re-analysis failed for {symbol}")
            else:
                logger.warning(f"⚠️ GPT not available for immediate re-analysis of {symbol}")

        except Exception as e:
            logger.error(f"Error in immediate re-analysis for {symbol}: {e}")

    async def _update_system_status(self):
        """Update system status"""
        try:
            # Update account info
            self.account_info = await self.mt5_connector.get_account_info()

            # Calculate daily profit
            today = datetime.now().date()
            daily_profit = sum(
                position.profit or 0
                for position in self.positions
                if position.close_time and position.close_time.date() == today
            )
            self.system_status.daily_profit = daily_profit

            # Update last analysis time
            if self.last_analysis_time:
                self.system_status.last_analysis_time = max(self.last_analysis_time.values())

            # Check system health
            if not self.system_status.is_mt5_connected:
                self.system_status.system_health = "ERROR"
            elif not self.system_status.is_gpt_available:
                self.system_status.system_health = "WARNING"
            else:
                self.system_status.system_health = "HEALTHY"

        except Exception as e:
            logger.error(f"Error updating system status: {e}")
            self.system_status.system_health = "ERROR"

    def _should_analyze_symbol(self, symbol: str) -> bool:
        """Check if symbol should be analyzed"""
        last_analysis = self.last_analysis_time.get(symbol)
        if not last_analysis:
            return True

        time_since_analysis = datetime.now() - last_analysis
        return time_since_analysis >= timedelta(minutes=settings.analysis_interval_minutes)

    def _get_position_by_symbol(self, symbol: str) -> Optional[Position]:
        """Get position by symbol"""
        for position in self.positions:
            if position.symbol == symbol and position.status.value == "OPEN":
                return position
        return None

    # Public methods for API
    async def get_system_status(self) -> SystemStatus:
        """Get current system status"""
        return self.system_status

    async def get_positions(self, realtime_pnl: bool = True) -> List[Position]:
        """Get all positions with optional real-time P&L"""
        if realtime_pnl:
            # Get fresh positions with real-time P&L calculation
            return await self.mt5_connector.get_positions(calculate_realtime_pnl=True)
        else:
            # Return cached positions
            return self.positions

    async def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get market data for symbol"""
        return self.market_data_cache.get(symbol)

    async def force_analysis(self, symbol: str) -> bool:
        """Force analysis for a specific symbol"""
        try:
            await self._process_symbol(symbol)
            return True
        except Exception as e:
            logger.error(f"Error in forced analysis for {symbol}: {e}")
            return False

    async def close_position_manual(self, ticket: int) -> bool:
        """Manually close a position"""
        try:
            success = await self.mt5_connector.close_position(ticket)
            if success:
                # Update positions with real-time P&L
                self.positions = await self.mt5_connector.get_positions(calculate_realtime_pnl=True)
                self.system_status.active_positions = len(self.positions)
            return success
        except Exception as e:
            logger.error(f"Error closing position manually: {e}")
            return False
