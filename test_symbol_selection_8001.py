#!/usr/bin/env python3
"""
Test Symbol Selection Feature on port 8001
"""
import requests
import time

BASE_URL = "http://localhost:8001"

def test_symbol_selection():
    """Test the new symbol selection feature"""
    print("🎯 === TESTING SYMBOL SELECTION FEATURE (Port 8001) ===")
    
    # Test 1: Get active symbols endpoint
    print("\n1. Testing Get Active Symbols Endpoint...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/active")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Active symbols: {data.get('active_symbols', [])}")
            print(f"✅ All symbols: {data.get('all_symbols', [])}")
            print(f"✅ Active count: {data.get('total_active', 0)}")
            print(f"✅ Available count: {data.get('total_available', 0)}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 2: Update active symbols
    print("\n2. Testing Update Active Symbols...")
    try:
        # Select first 2 symbols for testing
        test_symbols = ["XAUUSDm", "BTCUSDm"]
        
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": test_symbols}
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Update successful: {data.get('message')}")
            print(f"✅ Active symbols set to: {data.get('active_symbols')}")
            print(f"✅ Note: {data.get('note')}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 3: Verify update
    print("\n3. Verifying Update...")
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/active")
        if response.status_code == 200:
            data = response.json()
            active_symbols = data.get('active_symbols', [])
            print(f"✅ Current active symbols: {active_symbols}")
            
            if set(active_symbols) == set(test_symbols):
                print("✅ Update verified successfully!")
            else:
                print(f"⚠️  Update not reflected yet. Expected: {test_symbols}, Got: {active_symbols}")
        else:
            print(f"❌ Error: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Test 4: Test with invalid symbols
    print("\n4. Testing Invalid Symbols...")
    try:
        invalid_symbols = ["INVALID1", "INVALID2"]
        
        response = requests.post(
            f"{BASE_URL}/api/trading/symbols/active/update",
            json={"active_symbols": invalid_symbols}
        )
        
        print(f"Status: {response.status_code}")
        if response.status_code == 400:
            print("✅ Invalid symbols correctly rejected")
            print(f"✅ Error message: {response.json().get('detail')}")
        else:
            print(f"⚠️  Expected 400 error, got: {response.status_code}")
            print(f"Response: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    test_symbol_selection()
