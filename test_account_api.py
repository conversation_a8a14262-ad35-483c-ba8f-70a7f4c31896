#!/usr/bin/env python3
"""
Test account API endpoint
"""
import requests
import json
import time

BASE_URL = "http://localhost:8000"

def test_account_info():
    """Test account info endpoint"""
    url = f"{BASE_URL}/api/trading/account"
    
    try:
        response = requests.get(url)
        print(f"Account Info - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Account info retrieved successfully!")
            print(f"Login: {data.get('login')}")
            print(f"Balance: ${data.get('balance', 0):,.2f}")
            print(f"Equity: ${data.get('equity', 0):,.2f}")
            print(f"Free Margin: ${data.get('free_margin', 0):,.2f}")
            print(f"Currency: {data.get('currency')}")
            print(f"Leverage: 1:{data.get('leverage')}")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error getting account info: {e}")
        return False

def test_positions():
    """Test positions endpoint"""
    url = f"{BASE_URL}/api/trading/positions"
    
    try:
        response = requests.get(url)
        print(f"Positions - Status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {len(data)} positions")
            for pos in data:
                print(f"  - {pos.get('symbol')} {pos.get('order_type')} {pos.get('lot_size')} lots")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error getting positions: {e}")
        return False

def test_status():
    """Test status endpoint"""
    url = f"{BASE_URL}/api/trading/status"
    
    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            print(f"MT5: {'✅' if data.get('is_mt5_connected') else '❌'}")
            print(f"GPT: {'✅' if data.get('is_gpt_available') else '❌'}")
            print(f"Trading: {'✅' if data.get('is_trading_enabled') else '❌'}")
            print(f"Active Positions: {data.get('active_positions', 0)}")
            return data
        else:
            print(f"❌ Status error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error getting status: {e}")
        return None

if __name__ == "__main__":
    print("=== Testing Account & Positions API ===")
    
    print("\n1. Checking system status...")
    status = test_status()
    
    print("\n2. Testing account info...")
    test_account_info()
    
    print("\n3. Testing positions...")
    test_positions()
    
    if status and status.get('is_mt5_connected'):
        print("\n🎉 All tests completed! MT5 is connected.")
    else:
        print("\n⚠️  MT5 not connected. Some tests may fail.")
