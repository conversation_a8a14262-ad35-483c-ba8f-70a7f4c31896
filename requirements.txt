# Web Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
websockets==12.0

# MetaTrader 5 Integration
MetaTrader5==5.0.45

# OpenAI GPT API
openai==1.3.7

# Data Processing
pandas==2.1.4
numpy==1.25.2
ta==0.10.2

# HTTP Requests
httpx==0.25.2
requests==2.31.0

# Environment Variables
python-dotenv==1.0.0

# Database (Optional for storing trading history)
sqlalchemy==2.0.23

# Async Support
aiofiles==23.2.1

# Logging
loguru==0.7.2

# Data Validation
pydantic==2.5.0
pydantic-settings==2.9.1

# CORS Support
python-multipart==0.0.6

# Scheduling (for periodic tasks)
apscheduler==3.10.4

# Time handling
pytz==2023.3

# JSON handling
orjson==3.9.10

# Development tools
pytest==7.4.3
pytest-asyncio==0.21.1
black==23.11.0
flake8==6.1.0
