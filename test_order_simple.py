#!/usr/bin/env python3
"""
Simple test to check if order placement is working after drawdown fix
"""
import requests
import json
from datetime import datetime

def test_order_placement():
    """Test order placement after drawdown fix"""
    print("🎯 TESTING ORDER PLACEMENT AFTER DRAWDOWN FIX")
    print("=" * 60)
    
    try:
        # 1. Start trading engine
        print("1. Starting trading engine...")
        response = requests.post("http://localhost:8001/api/trading/start", 
                               json={"auto_trading_enabled": True})
        
        if response.status_code == 200:
            print("✅ Trading engine started")
        else:
            print(f"❌ Failed to start engine: {response.status_code}")
            return
        
        # 2. Check risk status
        print("\n2. Checking risk status...")
        response = requests.get("http://localhost:8001/api/trading/risk-status")
        
        if response.status_code == 200:
            data = response.json()
            trading_allowed = data.get("trading_allowed", {})
            
            print(f"   Drawdown OK: {'✅' if trading_allowed.get('drawdown_ok') else '❌'}")
            print(f"   Daily Loss OK: {'✅' if trading_allowed.get('daily_loss_ok') else '❌'}")
            print(f"   Positions OK: {'✅' if trading_allowed.get('positions_ok') else '❌'}")
            print(f"   Overall Trading: {'✅ ALLOWED' if trading_allowed.get('overall') else '❌ BLOCKED'}")
            
            if not trading_allowed.get('overall'):
                print("\n❌ TRADING STILL BLOCKED!")
                risk_metrics = data.get("risk_metrics", {})
                print(f"   Current Drawdown: {risk_metrics.get('drawdown_percent', 0):.2f}%")
                print(f"   Max Drawdown: {risk_metrics.get('max_drawdown_percent', 0):.2f}%")
                return
            else:
                print("\n✅ TRADING IS ALLOWED!")
        else:
            print(f"❌ Failed to get risk status: {response.status_code}")
            return
        
        # 3. Test order placement
        print("\n3. Testing order placement...")
        order_data = {
            "symbol": "BTCUSDm",
            "order_type": "BUY",
            "lot_size": 0.01,
            "comment": "Drawdown Fix Test"
        }
        
        response = requests.post("http://localhost:8001/api/trading/test-order", 
                               json=order_data)
        
        if response.status_code == 200:
            result = response.json()
            
            if result.get("ticket"):
                print(f"✅ ORDER PLACED SUCCESSFULLY!")
                print(f"   Ticket: {result.get('ticket')}")
                print(f"   Symbol: {result.get('symbol')}")
                print(f"   Price: ${result.get('price', 0):,.2f}")
                print(f"   Lot Size: {result.get('lot_size')}")
            else:
                print(f"❌ ORDER FAILED: {result.get('message')}")
        else:
            print(f"❌ API Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server")
        print("💡 Make sure backend is running on port 8001")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_account_status():
    """Quick account status check"""
    print("\n💰 ACCOUNT STATUS")
    print("=" * 20)
    
    try:
        response = requests.get("http://localhost:8001/api/trading/account")
        
        if response.status_code == 200:
            data = response.json()
            print(f"Balance: ${data.get('balance', 0):,.2f}")
            print(f"Equity: ${data.get('equity', 0):,.2f}")
            print(f"Free Margin: ${data.get('free_margin', 0):,.2f}")
            print(f"Margin Level: {data.get('margin_level', 0):.2f}%")
            print(f"Profit: ${data.get('profit', 0):,.2f}")
        else:
            print(f"❌ Error: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print(f"🕒 Order Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    test_account_status()
    test_order_placement()
    print("\n" + "=" * 60)
    print("🎉 Test completed! Check MT5 terminal for any new positions.")
