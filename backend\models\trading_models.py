"""
Trading data models using Pydantic
"""
from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, Field
from enum import Enum


class OrderType(str, Enum):
    """Order types enumeration"""
    BUY = "BUY"
    SELL = "SELL"
    BUY_LIMIT = "BUY_LIMIT"
    SELL_LIMIT = "SELL_LIMIT"
    BUY_STOP = "BUY_STOP"
    SELL_STOP = "SELL_STOP"


class PositionStatus(str, Enum):
    """Position status enumeration"""
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    PENDING = "PENDING"


class SignalType(str, Enum):
    """Trading signal types"""
    BUY = "BUY"
    SELL = "SELL"
    HOLD = "HOLD"


class RiskLevel(str, Enum):
    """Risk level enumeration"""
    LOW = "LOW"
    MEDIUM = "MEDIUM"
    HIGH = "HIGH"


class MarketData(BaseModel):
    """Market data model"""
    symbol: str
    timestamp: datetime
    bid: float
    ask: float
    spread: float
    volume: Optional[int] = None

    @property
    def mid_price(self) -> float:
        """Calculate mid price"""
        return (self.bid + self.ask) / 2

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class OHLCV(BaseModel):
    """OHLCV candlestick data"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    timeframe: str

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }


class TechnicalIndicators(BaseModel):
    """Technical indicators data"""
    symbol: str
    timestamp: datetime
    sma_20: Optional[float] = None
    sma_50: Optional[float] = None
    ema_12: Optional[float] = None
    ema_26: Optional[float] = None
    rsi: Optional[float] = None
    macd: Optional[float] = None
    macd_signal: Optional[float] = None
    bollinger_upper: Optional[float] = None
    bollinger_lower: Optional[float] = None
    bollinger_middle: Optional[float] = None


class GPTAnalysis(BaseModel):
    """GPT analysis result"""
    symbol: str
    timestamp: datetime
    signal: SignalType
    confidence: float = Field(..., ge=0, le=1)
    entry_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    risk_level: RiskLevel
    reasoning: str
    technical_summary: str
    market_sentiment: str


class TradingSignal(BaseModel):
    """Trading signal model"""
    id: Optional[str] = None
    symbol: str
    signal_type: SignalType
    timestamp: datetime
    entry_price: float
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    lot_size: float
    risk_level: RiskLevel
    confidence: float = Field(..., ge=0, le=1)
    reasoning: str
    gpt_analysis_id: Optional[str] = None


class Position(BaseModel):
    """Trading position model"""
    id: Optional[str] = None
    ticket: Optional[int] = None  # MT5 ticket number
    symbol: str
    order_type: OrderType
    lot_size: float
    entry_price: float
    current_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    open_time: datetime
    close_time: Optional[datetime] = None
    status: PositionStatus
    profit: Optional[float] = None
    commission: Optional[float] = None
    swap: Optional[float] = None
    comment: Optional[str] = None
    signal_id: Optional[str] = None


class OrderRequest(BaseModel):
    """Order request model"""
    symbol: str
    order_type: OrderType
    lot_size: float
    price: Optional[float] = None  # For limit/stop orders
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    comment: Optional[str] = None
    deviation: Optional[int] = 10  # Price deviation in points


class RiskManagement(BaseModel):
    """Risk management parameters"""
    max_risk_percent: float = Field(default=2.0, ge=0.1, le=10.0)
    max_open_positions: int = Field(default=5, ge=1, le=20)
    max_daily_loss: float = Field(default=1000, ge=100)
    max_drawdown_percent: float = Field(default=30, ge=1, le=50)  # Changed default to 30%
    stop_loss_pips: int = Field(default=50, ge=10, le=500)
    take_profit_pips: int = Field(default=100, ge=20, le=1000)
    trailing_stop_pips: int = Field(default=30, ge=10, le=200)


class AccountInfo(BaseModel):
    """Account information model"""
    login: int
    balance: float
    equity: float
    margin: float
    free_margin: float
    margin_level: float
    profit: float
    currency: str
    leverage: int
    server: str


class TradingStats(BaseModel):
    """Trading statistics"""
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_profit: float = 0.0
    total_loss: float = 0.0
    win_rate: float = 0.0
    profit_factor: float = 0.0
    max_drawdown: float = 0.0
    sharpe_ratio: Optional[float] = None

    def calculate_stats(self, positions: List[Position]):
        """Calculate trading statistics from positions"""
        closed_positions = [p for p in positions if p.status == PositionStatus.CLOSED and p.profit is not None]

        if not closed_positions:
            return

        self.total_trades = len(closed_positions)

        profits = [p.profit for p in closed_positions if p.profit > 0]
        losses = [p.profit for p in closed_positions if p.profit < 0]

        self.winning_trades = len(profits)
        self.losing_trades = len(losses)
        self.total_profit = sum(profits) if profits else 0.0
        self.total_loss = abs(sum(losses)) if losses else 0.0

        if self.total_trades > 0:
            self.win_rate = self.winning_trades / self.total_trades

        if self.total_loss > 0:
            self.profit_factor = self.total_profit / self.total_loss


class SystemStatus(BaseModel):
    """System status model"""
    is_trading_enabled: bool = True
    is_mt5_connected: bool = False
    is_gpt_available: bool = False
    last_analysis_time: Optional[datetime] = None
    active_positions: int = 0
    daily_profit: float = 0.0
    system_health: str = "UNKNOWN"  # HEALTHY, WARNING, ERROR
    error_messages: List[str] = []

    class Config:
        json_encoders = {
            datetime: lambda v: v.isoformat() if v else None
        }
