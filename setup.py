#!/usr/bin/env python3
"""
Auto Trading System Setup Script
Menginstall dependencies dan setup environment
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path

def run_command(command, cwd=None, check=True):
    """Run shell command"""
    print(f"🔧 Running: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Command failed: {e}")
        if e.stderr:
            print(f"Error: {e.stderr}")
        return False

def check_python_version():
    """Check Python version"""
    print("🐍 Checking Python version...")
    if sys.version_info < (3, 8):
        print(f"❌ Python 3.8+ required, found {sys.version}")
        return False
    print(f"✅ Python {sys.version.split()[0]} OK")
    return True

def check_node_version():
    """Check Node.js version"""
    print("📦 Checking Node.js version...")
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js {version} OK")
            return True
        else:
            print("❌ Node.js not found")
            return False
    except FileNotFoundError:
        print("❌ Node.js not found")
        print("Please install Node.js from https://nodejs.org/")
        return False

def setup_python_environment():
    """Setup Python virtual environment and install dependencies"""
    print("🐍 Setting up Python environment...")
    
    # Create virtual environment if it doesn't exist
    venv_path = Path("venv")
    if not venv_path.exists():
        print("📦 Creating virtual environment...")
        if not run_command(f"{sys.executable} -m venv venv"):
            return False
    
    # Determine activation script
    if os.name == 'nt':  # Windows
        activate_script = "venv\\Scripts\\activate"
        pip_command = "venv\\Scripts\\pip"
    else:  # Unix/Linux/macOS
        activate_script = "source venv/bin/activate"
        pip_command = "venv/bin/pip"
    
    # Upgrade pip
    print("📦 Upgrading pip...")
    if not run_command(f"{pip_command} install --upgrade pip"):
        return False
    
    # Install Python dependencies
    print("📦 Installing Python dependencies...")
    if not run_command(f"{pip_command} install -r requirements.txt"):
        return False
    
    print("✅ Python environment setup complete")
    return True

def setup_frontend():
    """Setup frontend dependencies"""
    print("⚛️ Setting up frontend...")
    
    frontend_dir = Path("frontend")
    if not frontend_dir.exists():
        print("❌ Frontend directory not found")
        return False
    
    # Install npm dependencies
    print("📦 Installing npm dependencies...")
    if not run_command("npm install", cwd=frontend_dir):
        return False
    
    print("✅ Frontend setup complete")
    return True

def setup_environment_file():
    """Setup .env file"""
    print("⚙️ Setting up environment file...")
    
    env_example = Path(".env.example")
    env_file = Path(".env")
    
    if not env_example.exists():
        print("❌ .env.example not found")
        return False
    
    if env_file.exists():
        print("⚠️ .env file already exists, skipping...")
        return True
    
    # Copy .env.example to .env
    shutil.copy(env_example, env_file)
    print("✅ .env file created from template")
    print("⚠️ Please edit .env file with your configuration:")
    print("   - OpenAI API Key")
    print("   - MetaTrader 5 credentials")
    print("   - Trading parameters")
    
    return True

def create_directories():
    """Create necessary directories"""
    print("📁 Creating directories...")
    
    directories = [
        "logs",
        "trading_data",
        "backtest_results"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")
    
    return True

def verify_installation():
    """Verify installation"""
    print("🔍 Verifying installation...")
    
    # Check Python imports
    try:
        import fastapi
        import MetaTrader5
        import openai
        import pandas
        import numpy
        print("✅ Python dependencies verified")
    except ImportError as e:
        print(f"❌ Python dependency missing: {e}")
        return False
    
    # Check frontend
    if not Path("frontend/node_modules").exists():
        print("❌ Frontend dependencies not installed")
        return False
    print("✅ Frontend dependencies verified")
    
    # Check .env file
    if not Path(".env").exists():
        print("❌ .env file not found")
        return False
    print("✅ Environment file verified")
    
    return True

def main():
    """Main setup function"""
    print("🚀 Auto Trading System Setup")
    print("=" * 40)
    
    # Check system requirements
    if not check_python_version():
        return False
    
    if not check_node_version():
        return False
    
    # Setup steps
    steps = [
        ("Creating directories", create_directories),
        ("Setting up environment file", setup_environment_file),
        ("Setting up Python environment", setup_python_environment),
        ("Setting up frontend", setup_frontend),
        ("Verifying installation", verify_installation)
    ]
    
    for step_name, step_func in steps:
        print(f"\n📋 {step_name}...")
        if not step_func():
            print(f"❌ {step_name} failed")
            return False
    
    print("\n🎉 Setup completed successfully!")
    print("\n📝 Next steps:")
    print("1. Edit .env file with your configuration")
    print("2. Make sure MetaTrader 5 is installed and configured")
    print("3. Run the system with: python run_system.py")
    print("\n📚 For more information, see README.md")
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Setup failed")
        sys.exit(1)
    
    input("\nPress Enter to exit...")
