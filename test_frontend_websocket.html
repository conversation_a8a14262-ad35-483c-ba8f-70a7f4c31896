<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frontend WebSocket Debug</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .connected { background-color: #d4edda; color: #155724; }
        .disconnected { background-color: #f8d7da; color: #721c24; }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .position-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .position-table th, .position-table td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        .position-table th {
            background-color: #f2f2f2;
        }
        .profit-positive { color: #28a745; font-weight: bold; }
        .profit-negative { color: #dc3545; font-weight: bold; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .refresh-btn {
            background-color: #28a745;
        }
        .refresh-btn:hover {
            background-color: #1e7e34;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Frontend WebSocket Debug Tool</h1>
        <p>This tool helps debug real-time position updates in the frontend</p>
        
        <div id="connectionStatus" class="status disconnected">
            ❌ WebSocket: Disconnected
        </div>
        
        <div>
            <button onclick="connectWebSocket()">Connect WebSocket</button>
            <button onclick="disconnectWebSocket()">Disconnect</button>
            <button onclick="loadPositions()" class="refresh-btn">Load Positions</button>
            <button onclick="clearLogs()">Clear Logs</button>
        </div>
        
        <h3>📊 Current Positions</h3>
        <div id="positionsContainer">
            <p>No positions loaded</p>
        </div>
        
        <h3>📝 WebSocket Logs</h3>
        <div id="logs" class="log">
            <p>Logs will appear here...</p>
        </div>
    </div>

    <script>
        let ws = null;
        let positions = [];
        let messageCount = 0;

        function log(message) {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('logs');
            logElement.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(message);
        }

        function updateConnectionStatus(connected) {
            const statusElement = document.getElementById('connectionStatus');
            if (connected) {
                statusElement.className = 'status connected';
                statusElement.innerHTML = '✅ WebSocket: Connected';
            } else {
                statusElement.className = 'status disconnected';
                statusElement.innerHTML = '❌ WebSocket: Disconnected';
            }
        }

        function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('⚠️ WebSocket already connected');
                return;
            }

            log('🔌 Connecting to WebSocket...');
            ws = new WebSocket('ws://localhost:8001/ws');

            ws.onopen = function() {
                log('✅ WebSocket connected successfully');
                updateConnectionStatus(true);
            };

            ws.onmessage = function(event) {
                messageCount++;
                try {
                    const data = JSON.parse(event.data);
                    log(`📨 Message #${messageCount}: ${data.type || 'unknown'}`);
                    
                    if (data.type === 'position_update') {
                        log(`💰 Position update: ${data.positions?.length || 0} positions`);
                        if (data.positions) {
                            positions = data.positions;
                            updatePositionsTable();
                            
                            // Log P&L details
                            data.positions.forEach(pos => {
                                log(`   📊 ${pos.symbol}: Entry $${pos.entry_price?.toFixed(5)}, Current $${pos.current_price?.toFixed(5)}, P&L $${pos.profit?.toFixed(2)}`);
                            });
                        }
                    } else if (data.type === 'system_update') {
                        const systemData = data.data || {};
                        log(`🖥️ System update: ${systemData.positions_count || 0} positions, Balance $${systemData.account_balance?.toFixed(2) || 0}`);
                    } else {
                        log(`📝 Other message: ${JSON.stringify(data).substring(0, 100)}...`);
                    }
                } catch (error) {
                    log(`❌ Error parsing message: ${error.message}`);
                }
            };

            ws.onclose = function() {
                log('❌ WebSocket disconnected');
                updateConnectionStatus(false);
            };

            ws.onerror = function(error) {
                log(`❌ WebSocket error: ${error}`);
                updateConnectionStatus(false);
            };
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                log('🔌 WebSocket disconnected manually');
                updateConnectionStatus(false);
            }
        }

        async function loadPositions() {
            try {
                log('📊 Loading positions via API...');
                const response = await fetch('http://localhost:8001/api/trading/positions?realtime_pnl=true');
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                positions = data || [];
                log(`✅ Loaded ${positions.length} positions via API`);
                updatePositionsTable();
                
                // Log position details
                positions.forEach(pos => {
                    log(`   📊 ${pos.symbol}: Entry $${pos.entry_price?.toFixed(5)}, Current $${pos.current_price?.toFixed(5)}, P&L $${pos.profit?.toFixed(2)}`);
                });
                
            } catch (error) {
                log(`❌ Error loading positions: ${error.message}`);
            }
        }

        function updatePositionsTable() {
            const container = document.getElementById('positionsContainer');
            
            if (positions.length === 0) {
                container.innerHTML = '<p>No positions found</p>';
                return;
            }

            let tableHTML = `
                <table class="position-table">
                    <thead>
                        <tr>
                            <th>Symbol</th>
                            <th>Type</th>
                            <th>Size</th>
                            <th>Entry Price</th>
                            <th>Current Price</th>
                            <th>P&L</th>
                            <th>Status</th>
                        </tr>
                    </thead>
                    <tbody>
            `;

            positions.forEach(pos => {
                const profitClass = (pos.profit || 0) >= 0 ? 'profit-positive' : 'profit-negative';
                tableHTML += `
                    <tr>
                        <td>${pos.symbol || 'N/A'}</td>
                        <td>${pos.order_type || 'N/A'}</td>
                        <td>${pos.lot_size || 'N/A'}</td>
                        <td>$${pos.entry_price?.toFixed(5) || 'N/A'}</td>
                        <td>$${pos.current_price?.toFixed(5) || 'N/A'}</td>
                        <td class="${profitClass}">$${(pos.profit || 0).toFixed(2)}</td>
                        <td>${pos.status || 'N/A'}</td>
                    </tr>
                `;
            });

            tableHTML += '</tbody></table>';
            container.innerHTML = tableHTML;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = '<p>Logs cleared...</p>';
            messageCount = 0;
        }

        // Auto-connect on page load
        window.onload = function() {
            log('🚀 Frontend WebSocket Debug Tool loaded');
            log('🔧 Auto-connecting to WebSocket...');
            connectWebSocket();
            
            // Auto-load positions
            setTimeout(() => {
                loadPositions();
            }, 2000);
        };
    </script>
</body>
</html>
