#!/usr/bin/env python3
"""
Test forced analysis to trigger order placement
"""
import requests
import time

def test_force_analysis():
    """Test forced analysis"""
    print("🔧 === TESTING FORCED ANALYSIS ===")
    
    # Start trading engine
    print("\n1. Starting trading engine...")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Trading engine started")
        else:
            print(f"❌ Failed: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Wait a bit
    time.sleep(3)
    
    # Force analysis
    print("\n2. Forcing analysis for BTCUSDm...")
    try:
        data = {"symbol": "BTCUSDm"}
        response = requests.post("http://localhost:8001/api/trading/analyze", json=data, timeout=15)
        print(f"   Status: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print("✅ Forced analysis completed")
            print(f"   Response: {result}")
        else:
            print(f"❌ Failed: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Monitor for 20 seconds
    print("\n3. Monitoring backend logs for 20 seconds...")
    print("   Watch for:")
    print("   - Order details logging")
    print("   - Order request with improved comment")
    print("   - Successful order placement or specific error")
    
    time.sleep(20)
    
    print("\n✅ Test completed - check backend logs for order placement attempts")

if __name__ == "__main__":
    print("🎯 Testing Forced Analysis and Order Placement")
    print("=" * 60)
    
    test_force_analysis()
    
    print("\n" + "=" * 60)
    print("🎉 FORCED ANALYSIS TEST COMPLETED!")
    print("\n📝 Improvements Made:")
    print("✅ Fixed comment: 'GPT Signal' (short and simple)")
    print("✅ Increased deviation: 100 points for crypto volatility")
    print("✅ Better error logging with MT5 last_error()")
    print("✅ Detailed order request logging")
    
    print("\n🔍 Expected Results:")
    print("   - Comment error should be resolved")
    print("   - Order should have better chance of success")
    print("   - If still failing, will show specific MT5 error")
    
    print(f"\n🌐 Monitor backend logs for detailed order placement information")
