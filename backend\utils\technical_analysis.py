"""
Technical Analysis Utilities
Calculate various technical indicators from OHLCV data
"""
import pandas as pd
import numpy as np
from typing import List, Optional
from ta import add_all_ta_features
from ta.utils import dropna

from ..models.trading_models import OHLCV, TechnicalIndicators


class TechnicalAnalyzer:
    """Technical analysis calculator"""
    
    @staticmethod
    def calculate_indicators(ohlcv_data: List[OHLCV]) -> Optional[TechnicalIndicators]:
        """Calculate technical indicators from OHLCV data"""
        try:
            if len(ohlcv_data) < 50:  # Need enough data for indicators
                return None
            
            # Convert to DataFrame
            df = pd.DataFrame([
                {
                    'timestamp': candle.timestamp,
                    'open': candle.open,
                    'high': candle.high,
                    'low': candle.low,
                    'close': candle.close,
                    'volume': candle.volume
                }
                for candle in ohlcv_data
            ])
            
            # Clean data
            df = dropna(df)
            
            if len(df) < 50:
                return None
            
            # Calculate indicators manually for better control
            indicators = TechnicalAnalyzer._calculate_manual_indicators(df)
            
            # Get latest values
            latest = df.iloc[-1]
            
            return TechnicalIndicators(
                symbol=ohlcv_data[-1].symbol,
                timestamp=latest['timestamp'],
                sma_20=indicators.get('sma_20'),
                sma_50=indicators.get('sma_50'),
                ema_12=indicators.get('ema_12'),
                ema_26=indicators.get('ema_26'),
                rsi=indicators.get('rsi'),
                macd=indicators.get('macd'),
                macd_signal=indicators.get('macd_signal'),
                bollinger_upper=indicators.get('bb_upper'),
                bollinger_lower=indicators.get('bb_lower'),
                bollinger_middle=indicators.get('bb_middle')
            )
            
        except Exception as e:
            print(f"Error calculating technical indicators: {e}")
            return None
    
    @staticmethod
    def _calculate_manual_indicators(df: pd.DataFrame) -> dict:
        """Calculate technical indicators manually"""
        indicators = {}
        
        try:
            # Simple Moving Averages
            indicators['sma_20'] = df['close'].rolling(window=20).mean().iloc[-1]
            indicators['sma_50'] = df['close'].rolling(window=50).mean().iloc[-1]
            
            # Exponential Moving Averages
            indicators['ema_12'] = df['close'].ewm(span=12).mean().iloc[-1]
            indicators['ema_26'] = df['close'].ewm(span=26).mean().iloc[-1]
            
            # RSI
            indicators['rsi'] = TechnicalAnalyzer._calculate_rsi(df['close'])
            
            # MACD
            macd_data = TechnicalAnalyzer._calculate_macd(df['close'])
            indicators['macd'] = macd_data['macd']
            indicators['macd_signal'] = macd_data['signal']
            
            # Bollinger Bands
            bb_data = TechnicalAnalyzer._calculate_bollinger_bands(df['close'])
            indicators['bb_upper'] = bb_data['upper']
            indicators['bb_lower'] = bb_data['lower']
            indicators['bb_middle'] = bb_data['middle']
            
        except Exception as e:
            print(f"Error in manual indicator calculation: {e}")
        
        return indicators
    
    @staticmethod
    def _calculate_rsi(prices: pd.Series, period: int = 14) -> Optional[float]:
        """Calculate RSI"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            return rsi.iloc[-1] if not pd.isna(rsi.iloc[-1]) else None
            
        except Exception:
            return None
    
    @staticmethod
    def _calculate_macd(prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> dict:
        """Calculate MACD"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            
            macd_line = ema_fast - ema_slow
            signal_line = macd_line.ewm(span=signal).mean()
            
            return {
                'macd': macd_line.iloc[-1] if not pd.isna(macd_line.iloc[-1]) else None,
                'signal': signal_line.iloc[-1] if not pd.isna(signal_line.iloc[-1]) else None
            }
            
        except Exception:
            return {'macd': None, 'signal': None}
    
    @staticmethod
    def _calculate_bollinger_bands(prices: pd.Series, period: int = 20, std_dev: int = 2) -> dict:
        """Calculate Bollinger Bands"""
        try:
            sma = prices.rolling(window=period).mean()
            std = prices.rolling(window=period).std()
            
            upper = sma + (std * std_dev)
            lower = sma - (std * std_dev)
            
            return {
                'upper': upper.iloc[-1] if not pd.isna(upper.iloc[-1]) else None,
                'lower': lower.iloc[-1] if not pd.isna(lower.iloc[-1]) else None,
                'middle': sma.iloc[-1] if not pd.isna(sma.iloc[-1]) else None
            }
            
        except Exception:
            return {'upper': None, 'lower': None, 'middle': None}
    
    @staticmethod
    def identify_support_resistance(ohlcv_data: List[OHLCV], lookback: int = 20) -> dict:
        """Identify support and resistance levels"""
        try:
            if len(ohlcv_data) < lookback * 2:
                return {'support': None, 'resistance': None}
            
            # Get recent data
            recent_data = ohlcv_data[-lookback:]
            highs = [candle.high for candle in recent_data]
            lows = [candle.low for candle in recent_data]
            
            # Find local maxima and minima
            resistance_levels = []
            support_levels = []
            
            for i in range(2, len(highs) - 2):
                # Check for local maximum (resistance)
                if (highs[i] > highs[i-1] and highs[i] > highs[i-2] and 
                    highs[i] > highs[i+1] and highs[i] > highs[i+2]):
                    resistance_levels.append(highs[i])
                
                # Check for local minimum (support)
                if (lows[i] < lows[i-1] and lows[i] < lows[i-2] and 
                    lows[i] < lows[i+1] and lows[i] < lows[i+2]):
                    support_levels.append(lows[i])
            
            # Get strongest levels
            current_price = ohlcv_data[-1].close
            
            # Find nearest support (below current price)
            valid_support = [level for level in support_levels if level < current_price]
            nearest_support = max(valid_support) if valid_support else None
            
            # Find nearest resistance (above current price)
            valid_resistance = [level for level in resistance_levels if level > current_price]
            nearest_resistance = min(valid_resistance) if valid_resistance else None
            
            return {
                'support': nearest_support,
                'resistance': nearest_resistance
            }
            
        except Exception as e:
            print(f"Error identifying support/resistance: {e}")
            return {'support': None, 'resistance': None}
    
    @staticmethod
    def calculate_volatility(ohlcv_data: List[OHLCV], period: int = 20) -> Optional[float]:
        """Calculate price volatility"""
        try:
            if len(ohlcv_data) < period:
                return None
            
            recent_data = ohlcv_data[-period:]
            prices = [candle.close for candle in recent_data]
            
            # Calculate returns
            returns = []
            for i in range(1, len(prices)):
                returns.append((prices[i] - prices[i-1]) / prices[i-1])
            
            if not returns:
                return None
            
            # Calculate standard deviation of returns
            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            volatility = (variance ** 0.5) * (252 ** 0.5)  # Annualized volatility
            
            return volatility
            
        except Exception:
            return None
    
    @staticmethod
    def detect_trend(ohlcv_data: List[OHLCV], period: int = 20) -> str:
        """Detect trend direction"""
        try:
            if len(ohlcv_data) < period:
                return "SIDEWAYS"
            
            recent_data = ohlcv_data[-period:]
            prices = [candle.close for candle in recent_data]
            
            # Calculate trend using linear regression
            x = list(range(len(prices)))
            n = len(prices)
            
            sum_x = sum(x)
            sum_y = sum(prices)
            sum_xy = sum(x[i] * prices[i] for i in range(n))
            sum_x2 = sum(xi ** 2 for xi in x)
            
            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)
            
            # Determine trend based on slope
            if slope > 0.001:
                return "UPTREND"
            elif slope < -0.001:
                return "DOWNTREND"
            else:
                return "SIDEWAYS"
                
        except Exception:
            return "SIDEWAYS"
