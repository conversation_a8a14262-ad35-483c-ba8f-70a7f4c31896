#!/usr/bin/env python3
"""
Check spread issue for BTCUSDm
"""
import MetaTrader5 as mt5
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def check_spread_issue():
    """Check spread issue for BTCUSDm"""
    print("🔍 === CHECKING SPREAD ISSUE FOR BTCUSDm ===")

    # Initialize MT5
    if not mt5.initialize():
        print("❌ Failed to initialize MT5")
        return

    # Login
    login = 186982659
    password = "J@mzmail641@"
    server = "Exness-MT5Real26"

    if not mt5.login(login, password=password, server=server):
        print("❌ Failed to login to MT5")
        mt5.shutdown()
        return

    print("✅ Connected to MT5")

    # Check BTCUSDm
    symbol = "BTCUSDm"
    print(f"\n📊 Checking {symbol}...")

    # Get symbol info
    symbol_info = mt5.symbol_info(symbol)
    if symbol_info is None:
        print(f"❌ Symbol {symbol} not found")
        mt5.shutdown()
        return

    # Select symbol
    if not symbol_info.select:
        if not mt5.symbol_select(symbol, True):
            print(f"❌ Failed to select symbol {symbol}")
            mt5.shutdown()
            return

    # Get tick data
    tick = mt5.symbol_info_tick(symbol)
    if tick is None:
        print(f"❌ No tick data for {symbol}")
        mt5.shutdown()
        return

    # Calculate spread
    spread_raw = tick.ask - tick.bid

    # From config.py - BTCUSDm configuration (CORRECTED)
    pip_value = 1.0  # BTCUSDm pip value (FIXED: was 0.01)
    spread_threshold = 50.0  # BTCUSDm spread threshold

    # Calculate spread in pips
    spread_pips = spread_raw / pip_value

    print(f"📈 Market Data for {symbol}:")
    print(f"   Bid: {tick.bid}")
    print(f"   Ask: {tick.ask}")
    print(f"   Spread (raw): {spread_raw}")
    print(f"   Pip value: {pip_value}")
    print(f"   Spread (pips): {spread_pips:.2f}")
    print(f"   Threshold: {spread_threshold} pips")

    if spread_pips > spread_threshold:
        print(f"❌ SPREAD TOO WIDE!")
        print(f"   Current: {spread_pips:.2f} pips")
        print(f"   Threshold: {spread_threshold} pips")
        print(f"   Difference: {spread_pips - spread_threshold:.2f} pips over limit")

        print(f"\n🔧 SOLUTIONS:")
        print(f"1. Increase spread threshold in config.py:")
        print(f'   "BTCUSDm": {{"spread_threshold": {spread_pips + 5:.0f}}}')
        print(f"2. Wait for better market conditions")
        print(f"3. Use different symbol with lower spread")

    else:
        print(f"✅ Spread is acceptable")
        print(f"   Current: {spread_pips:.2f} pips")
        print(f"   Threshold: {spread_threshold} pips")

    # Check other crypto symbols for comparison
    print(f"\n📊 Checking other crypto symbols for comparison...")
    crypto_symbols = ["BTCUSDm", "ETHUSDm", "EURUSDm", "XAUUSDm"]

    for sym in crypto_symbols:
        sym_info = mt5.symbol_info(sym)
        if sym_info is None:
            print(f"   {sym}: Not available")
            continue

        if not sym_info.select:
            mt5.symbol_select(sym, True)

        sym_tick = mt5.symbol_info_tick(sym)
        if sym_tick is None:
            print(f"   {sym}: No tick data")
            continue

        sym_spread = sym_tick.ask - sym_tick.bid

        # Determine pip value based on symbol (CORRECTED)
        if "BTC" in sym:
            sym_pip_value = 1.0  # Bitcoin pip value
        elif "ETH" in sym:
            sym_pip_value = 0.1  # Ethereum pip value
        elif "XAU" in sym:
            sym_pip_value = 0.01  # Gold pip value
        else:
            sym_pip_value = 0.0001  # Standard forex pip value

        sym_spread_pips = sym_spread / sym_pip_value

        print(f"   {sym}: {sym_spread_pips:.2f} pips (raw: {sym_spread})")

    mt5.shutdown()
    print("\n👋 Disconnected from MT5")

if __name__ == "__main__":
    check_spread_issue()
