#!/usr/bin/env python3
"""
Simple test for technical indicators
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.utils.technical_analysis import TechnicalAnalyzer
from backend.models.trading_models import OHLCV
from datetime import datetime

def test_indicators():
    """Test technical indicators with sample data"""
    print("🧪 Testing Technical Indicators")
    print("=" * 50)
    
    # Create sample OHLCV data (100 candles)
    sample_data = []
    base_price = 50000.0
    
    for i in range(100):
        # Simulate price movement
        price_change = (i % 10 - 5) * 100  # Simple oscillation
        current_price = base_price + price_change + (i * 10)  # Slight uptrend
        
        ohlcv = OHLCV(
            symbol="BTCUSD",
            timestamp=datetime.now(),
            open=current_price - 50,
            high=current_price + 100,
            low=current_price - 100,
            close=current_price,
            volume=1000,
            timeframe="15m"
        )
        sample_data.append(ohlcv)
    
    print(f"📊 Created {len(sample_data)} sample candles")
    print(f"💰 Price range: {sample_data[0].close:.2f} - {sample_data[-1].close:.2f}")
    
    # Test technical indicators
    try:
        analyzer = TechnicalAnalyzer()
        indicators = analyzer.calculate_indicators(sample_data)
        
        if indicators:
            print("\n✅ Technical Indicators Calculated Successfully!")
            print("-" * 40)
            print(f"📈 SMA 20: {indicators.sma_20:.2f}" if indicators.sma_20 else "📈 SMA 20: None")
            print(f"📈 SMA 50: {indicators.sma_50:.2f}" if indicators.sma_50 else "📈 SMA 50: None")
            print(f"📊 RSI: {indicators.rsi:.2f}" if indicators.rsi else "📊 RSI: None")
            print(f"📉 MACD: {indicators.macd:.4f}" if indicators.macd else "📉 MACD: None")
            print(f"🎯 BB Upper: {indicators.bollinger_upper:.2f}" if indicators.bollinger_upper else "🎯 BB Upper: None")
            print(f"🎯 BB Lower: {indicators.bollinger_lower:.2f}" if indicators.bollinger_lower else "🎯 BB Lower: None")
            
            return True
        else:
            print("❌ Technical indicators calculation returned None")
            return False
            
    except Exception as e:
        print(f"❌ Error calculating indicators: {e}")
        return False

def test_insufficient_data():
    """Test with insufficient data"""
    print("\n🧪 Testing with Insufficient Data")
    print("=" * 50)
    
    # Create only 20 candles (insufficient for most indicators)
    sample_data = []
    base_price = 50000.0
    
    for i in range(20):
        ohlcv = OHLCV(
            symbol="BTCUSD",
            timestamp=datetime.now(),
            open=base_price,
            high=base_price + 100,
            low=base_price - 100,
            close=base_price + (i * 10),
            volume=1000,
            timeframe="15m"
        )
        sample_data.append(ohlcv)
    
    print(f"📊 Created {len(sample_data)} sample candles (insufficient)")
    
    try:
        analyzer = TechnicalAnalyzer()
        indicators = analyzer.calculate_indicators(sample_data)
        
        if indicators is None:
            print("✅ Correctly returned None for insufficient data")
            return True
        else:
            print("⚠️ Unexpectedly calculated indicators with insufficient data")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🔬 TECHNICAL INDICATORS TEST")
    print("=" * 60)
    
    # Test 1: Normal indicators calculation
    test1_success = test_indicators()
    
    # Test 2: Insufficient data handling
    test2_success = test_insufficient_data()
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS:")
    print(f"✅ Normal calculation: {'PASS' if test1_success else 'FAIL'}")
    print(f"✅ Insufficient data handling: {'PASS' if test2_success else 'FAIL'}")
    
    if test1_success and test2_success:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n💡 CONCLUSION:")
        print("   ✅ Technical indicators work correctly")
        print("   ✅ The warnings in your logs are likely due to:")
        print("      - Insufficient historical data for some timeframes")
        print("      - MT5 data availability issues")
        print("      - Symbol-specific data limitations")
        print("\n🔧 SOLUTION:")
        print("   ✅ Increased data count in MT5Connector (already done)")
        print("   ✅ Multi-timeframe analysis still works without indicators")
        print("   ✅ GPT can analyze price action even without technical indicators")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("   Need to investigate technical indicators calculation")
