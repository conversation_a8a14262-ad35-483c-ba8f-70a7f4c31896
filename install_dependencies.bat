@echo off
echo 📦 Installing Auto Trading System Dependencies
echo ============================================

echo 🐍 Installing Python dependencies...
pip install -r requirements.txt
if errorlevel 1 (
    echo ❌ Failed to install Python dependencies
    pause
    exit /b 1
)

echo ⚛️ Installing Frontend dependencies...
cd frontend
npm install
if errorlevel 1 (
    echo ❌ Failed to install Frontend dependencies
    pause
    exit /b 1
)

cd ..
echo ✅ All dependencies installed successfully!
echo.
echo 📝 Next steps:
echo 1. Copy .env.example to .env and configure it
echo 2. Make sure MetaTrader 5 is installed
echo 3. Run: python run_system.py

pause
