"""
Trading API Routes
Provides REST API endpoints for trading operations
"""
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel

from ..models.trading_models import (
    Position, TradingSignal, SystemStatus, OrderRequest,
    MarketData, AccountInfo
)
from ..services.trading_engine import TradingEngine

router = APIRouter(prefix="/api/trading", tags=["trading"])

# Global trading engine instance
trading_engine: Optional[TradingEngine] = None


def get_trading_engine() -> TradingEngine:
    """Dependency to get trading engine instance"""
    global trading_engine
    if trading_engine is None:
        raise HTTPException(status_code=503, detail="Trading engine not initialized")
    return trading_engine


class StartEngineRequest(BaseModel):
    """Request model for starting trading engine"""
    auto_trading_enabled: bool = True


class ClosePositionRequest(BaseModel):
    """Request model for closing position"""
    ticket: int
    reason: Optional[str] = None


class ForceAnalysisRequest(BaseModel):
    """Request model for forcing analysis"""
    symbol: str


class UpdateSymbolsRequest(BaseModel):
    """Request model for updating trading symbols"""
    symbols: List[str]


class UpdateActiveSymbolsRequest(BaseModel):
    """Request model for updating active symbols"""
    active_symbols: List[str]


@router.post("/start")
async def start_trading_engine(
    request: StartEngineRequest,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """Start the trading engine"""
    try:
        if engine.is_running:
            return {"message": "Trading engine is already running", "status": "running"}

        # Start engine in background
        import asyncio
        asyncio.create_task(engine.start())

        return {"message": "Trading engine started successfully", "status": "starting"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to start trading engine: {e}")


@router.post("/stop")
async def stop_trading_engine(engine: TradingEngine = Depends(get_trading_engine)):
    """Stop the trading engine"""
    try:
        await engine.stop()
        return {"message": "Trading engine stopped successfully", "status": "stopped"}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to stop trading engine: {e}")


@router.get("/status", response_model=SystemStatus)
async def get_system_status(engine: TradingEngine = Depends(get_trading_engine)):
    """Get current system status"""
    try:
        return await engine.get_system_status()

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get system status: {e}")


@router.get("/positions", response_model=List[Position])
async def get_positions(
    realtime_pnl: bool = True,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """Get all trading positions with optional real-time P&L calculation"""
    try:
        return await engine.get_positions(realtime_pnl=realtime_pnl)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get positions: {e}")


@router.post("/positions/close")
async def close_position(
    request: ClosePositionRequest,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """Close a specific position"""
    try:
        # Get position data before closing for broadcast
        positions = await engine.get_positions()
        position_to_close = None
        for pos in positions:
            if pos.ticket == request.ticket:
                position_to_close = pos
                break

        success = await engine.close_position_manual(request.ticket)

        if success:
            # Broadcast position closure
            if position_to_close:
                from ..main import manager
                pos_dict = position_to_close.dict() if hasattr(position_to_close, 'dict') else position_to_close
                # Convert datetime fields
                if pos_dict.get('open_time'):
                    pos_dict['open_time'] = pos_dict['open_time'].isoformat() if hasattr(pos_dict['open_time'], 'isoformat') else str(pos_dict['open_time'])
                if pos_dict.get('close_time'):
                    pos_dict['close_time'] = pos_dict['close_time'].isoformat() if hasattr(pos_dict['close_time'], 'isoformat') else str(pos_dict['close_time'])

                await manager.broadcast_position_change("closed", pos_dict)

            return {"message": f"Position {request.ticket} closed successfully", "success": True}
        else:
            raise HTTPException(status_code=400, detail=f"Failed to close position {request.ticket}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error closing position: {e}")


@router.get("/account", response_model=AccountInfo)
async def get_account_info(engine: TradingEngine = Depends(get_trading_engine)):
    """Get account information"""
    try:
        # If account info is not available, try to get it from MT5 directly
        if not engine.account_info:
            if engine.mt5_connector and engine.mt5_connector.is_connected:
                engine.account_info = await engine.mt5_connector.get_account_info()

            if not engine.account_info:
                raise HTTPException(status_code=503, detail="Account information not available - MT5 not connected or engine not initialized")

        return engine.account_info

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get account info: {e}")


@router.post("/analyze")
async def force_analysis(
    request: ForceAnalysisRequest,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """Force market analysis for a specific symbol"""
    try:
        success = await engine.force_analysis(request.symbol)

        if success:
            return {"message": f"Analysis completed for {request.symbol}", "success": True}
        else:
            raise HTTPException(status_code=400, detail=f"Failed to analyze {request.symbol}")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error in analysis: {e}")


@router.post("/test-order")
async def test_order_placement(engine: TradingEngine = Depends(get_trading_engine)):
    """Test order placement with safe parameters"""
    try:
        from ..models.trading_models import OrderRequest, OrderType

        # Test with affordable symbol - USDAED (cheapest)
        symbol = "USDAED"

        # Get current market data
        market_data = await engine.mt5_connector.get_market_data(symbol)
        if not market_data:
            raise HTTPException(status_code=400, detail=f"Cannot get market data for {symbol}")

        # Create minimal test order with very small lot size
        order_request = OrderRequest(
            symbol=symbol,
            order_type=OrderType.BUY,
            lot_size=0.01,  # Minimum lot size
            comment="Test",
            deviation=100
        )

        # Log the test attempt
        print(f"🧪 Testing order placement for {symbol}")
        print(f"   Market price: {market_data.ask:.5f}")
        print(f"   Lot size: {order_request.lot_size}")

        # Place order
        ticket = await engine.mt5_connector.place_order(order_request)

        if ticket:
            return {
                "message": f"Test order placed successfully for {symbol}",
                "ticket": ticket,
                "symbol": symbol,
                "lot_size": order_request.lot_size,
                "price": market_data.ask
            }
        else:
            return {
                "message": f"Test order failed for {symbol}",
                "ticket": None,
                "symbol": symbol,
                "error": "MT5 returned None - check logs for details"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Test order error: {e}")


@router.post("/test-order/{symbol}")
async def test_order_custom_symbol(
    symbol: str,
    lot_size: float = 0.01,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """Test order placement with custom symbol"""
    try:
        from ..models.trading_models import OrderRequest, OrderType

        # Get current market data
        market_data = await engine.mt5_connector.get_market_data(symbol)
        if not market_data:
            raise HTTPException(status_code=400, detail=f"Cannot get market data for {symbol}")

        # Create test order
        order_request = OrderRequest(
            symbol=symbol,
            order_type=OrderType.BUY,
            lot_size=lot_size,
            comment="Custom Test",
            deviation=100
        )

        # Log the test attempt
        print(f"🧪 Testing custom order placement for {symbol}")
        print(f"   Market price: {market_data.ask:.5f}")
        print(f"   Lot size: {order_request.lot_size}")

        # Place order
        ticket = await engine.mt5_connector.place_order(order_request)

        if ticket:
            return {
                "message": f"Custom test order placed successfully for {symbol}",
                "ticket": ticket,
                "symbol": symbol,
                "lot_size": order_request.lot_size,
                "price": market_data.ask
            }
        else:
            return {
                "message": f"Custom test order failed for {symbol}",
                "ticket": None,
                "symbol": symbol,
                "error": "MT5 returned None - check logs for details"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Custom test order error: {e}")


@router.get("/market-data/{symbol}", response_model=MarketData)
async def get_market_data(
    symbol: str,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """Get current market data for a symbol"""
    try:
        market_data = await engine.get_market_data(symbol)

        if not market_data:
            raise HTTPException(status_code=404, detail=f"Market data not available for {symbol}")

        return market_data

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get market data: {e}")


@router.get("/signals")
async def get_active_signals(engine: TradingEngine = Depends(get_trading_engine)):
    """Get active trading signals"""
    try:
        return {"signals": list(engine.active_signals.values())}

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get signals: {e}")


@router.get("/symbols/available")
async def get_available_symbols(engine: TradingEngine = Depends(get_trading_engine)):
    """Get all available symbols from MT5"""
    try:
        if not engine.mt5_connector or not engine.mt5_connector.is_connected:
            raise HTTPException(status_code=503, detail="MT5 not connected")

        import MetaTrader5 as mt5
        symbols = mt5.symbols_get()

        if symbols is None:
            raise HTTPException(status_code=500, detail="Failed to get symbols from MT5")

        # Format symbols for frontend
        symbol_list = []
        for symbol in symbols:
            tick = mt5.symbol_info_tick(symbol.name)
            spread = (tick.ask - tick.bid) if tick else 0

            symbol_list.append({
                "name": symbol.name,
                "description": symbol.description,
                "selected": symbol.select,
                "spread": round(spread, 5),
                "currency_base": symbol.currency_base,
                "currency_profit": symbol.currency_profit,
                "path": symbol.path
            })

        return {"symbols": symbol_list, "total": len(symbol_list)}

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get available symbols: {e}")


@router.get("/symbols/current")
async def get_current_symbols():
    """Get currently configured trading symbols"""
    try:
        from ..config import settings
        return {
            "symbols": settings.symbols_list,
            "active_symbols": settings.active_symbols_list,
            "total": len(settings.symbols_list),
            "active_total": len(settings.active_symbols_list)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get current symbols: {e}")


@router.get("/symbols/active")
async def get_active_symbols():
    """Get currently active symbols for analysis"""
    try:
        from ..config import settings
        return {
            "active_symbols": settings.active_symbols_list,
            "all_symbols": settings.symbols_list,
            "total_active": len(settings.active_symbols_list),
            "total_available": len(settings.symbols_list)
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get active symbols: {e}")


@router.post("/symbols/active/update")
async def update_active_symbols(
    request: UpdateActiveSymbolsRequest,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """Update active symbols for analysis"""
    try:
        from ..config import settings
        import os

        # Validate that all requested symbols are in available symbols
        available_symbols = settings.symbols_list
        invalid_symbols = [s for s in request.active_symbols if s not in available_symbols]

        if invalid_symbols:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid symbols: {invalid_symbols}. Available symbols: {available_symbols}"
            )

        # Update .env file
        env_path = ".env"
        active_symbols_str = ",".join(request.active_symbols)

        # Read current .env content
        env_lines = []
        if os.path.exists(env_path):
            with open(env_path, 'r') as f:
                env_lines = f.readlines()

        # Update or add ACTIVE_SYMBOLS line
        updated = False
        for i, line in enumerate(env_lines):
            if line.startswith('ACTIVE_SYMBOLS='):
                env_lines[i] = f'ACTIVE_SYMBOLS={active_symbols_str}\n'
                updated = True
                break

        if not updated:
            env_lines.append(f'ACTIVE_SYMBOLS={active_symbols_str}\n')

        # Write back to .env file
        with open(env_path, 'w') as f:
            f.writelines(env_lines)

        # Update settings in memory (requires restart to fully take effect)
        settings.active_symbols = active_symbols_str

        return {
            "message": "Active symbols updated successfully",
            "active_symbols": request.active_symbols,
            "note": "Changes applied immediately. Trading engine will use new symbols in next cycle."
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update active symbols: {e}")


@router.post("/symbols/update")
async def update_trading_symbols(
    request: UpdateSymbolsRequest,
    engine: TradingEngine = Depends(get_trading_engine)
):
    """Update trading symbols configuration"""
    try:
        if not engine.mt5_connector or not engine.mt5_connector.is_connected:
            raise HTTPException(status_code=503, detail="MT5 not connected")

        # Validate symbols exist in MT5
        import MetaTrader5 as mt5
        available_symbols = [s.name for s in mt5.symbols_get() or []]

        invalid_symbols = [s for s in request.symbols if s not in available_symbols]
        if invalid_symbols:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid symbols: {invalid_symbols}. Not available in MT5."
            )

        # Update configuration (this would require restart to take effect)
        # For now, we'll return success and suggest restart
        return {
            "message": "Symbols updated successfully",
            "symbols": request.symbols,
            "note": "Restart trading engine to apply changes"
        }

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update symbols: {e}")


@router.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "trading-api"}


# Initialize trading engine
async def initialize_trading_engine():
    """Initialize the global trading engine"""
    global trading_engine
    if trading_engine is None:
        trading_engine = TradingEngine()
        # Note: Don't start automatically, wait for explicit start command
