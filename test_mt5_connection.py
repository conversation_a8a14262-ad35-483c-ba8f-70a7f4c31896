#!/usr/bin/env python3
"""
Test MT5 Connection
"""
import MetaTrader5 as mt5
import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_mt5_connection():
    """Test MT5 connection step by step"""
    print("🔍 === TESTING MT5 CONNECTION ===")
    
    # Step 1: Initialize MT5
    print("\n1️⃣ Initializing MT5...")
    if not mt5.initialize():
        print("❌ Failed to initialize MT5")
        print(f"Error: {mt5.last_error()}")
        print("\n🔧 SOLUTIONS:")
        print("- Make sure MetaTrader 5 is installed")
        print("- Run as Administrator")
        print("- Check if MT5 is already running")
        return False
    
    print("✅ MT5 initialized successfully")
    
    # Step 2: Get credentials
    print("\n2️⃣ Getting credentials from .env...")
    try:
        login = int(os.getenv('MT5_LOGIN'))
        password = os.getenv('MT5_PASSWORD')
        server = os.getenv('MT5_SERVER')
        
        print(f"📊 Login: {login}")
        print(f"🏦 Server: {server}")
        print(f"🔐 Password: {'*' * len(password)}")
    except Exception as e:
        print(f"❌ Error reading credentials: {e}")
        mt5.shutdown()
        return False
    
    # Step 3: Connect to account
    print("\n3️⃣ Connecting to account...")
    if not mt5.login(login, password=password, server=server):
        error = mt5.last_error()
        print(f"❌ Failed to connect to account {login}")
        print(f"Error code: {error[0]}")
        print(f"Error message: {error[1]}")
        
        print("\n🔧 POSSIBLE SOLUTIONS:")
        if error[0] == 10004:
            print("- Check internet connection")
            print("- Verify server name: Exness-MT5Real26")
        elif error[0] == 10014:
            print("- Check login credentials")
            print("- Verify password is correct")
        elif error[0] == 10015:
            print("- Account may be disabled")
            print("- Contact broker support")
        else:
            print("- Try manual login in MT5 first")
            print("- Restart MT5 application")
            print("- Check if account is active")
        
        mt5.shutdown()
        return False
    
    print(f"✅ Connected to account {login}")
    
    # Step 4: Get account info
    print("\n4️⃣ Getting account information...")
    account_info = mt5.account_info()
    if account_info:
        print(f"📊 Account: {account_info.login}")
        print(f"🏦 Server: {account_info.server}")
        print(f"💰 Balance: ${account_info.balance}")
        print(f"💵 Equity: ${account_info.equity}")
        print(f"📈 Margin: ${account_info.margin}")
        print(f"🔓 Trade Allowed: {account_info.trade_allowed}")
    else:
        print("❌ Failed to get account info")
    
    # Step 5: Test symbol access
    print("\n5️⃣ Testing symbol access...")
    test_symbols = ['USDAED', 'USDCNY', 'USDINR']
    
    for symbol in test_symbols:
        symbol_info = mt5.symbol_info(symbol)
        if symbol_info:
            print(f"✅ {symbol}: Available (Spread: {symbol_info.spread})")
        else:
            print(f"❌ {symbol}: Not available")
    
    # Step 6: Test market data
    print("\n6️⃣ Testing market data...")
    try:
        rates = mt5.copy_rates_from_pos("USDAED", mt5.TIMEFRAME_M1, 0, 10)
        if rates is not None and len(rates) > 0:
            print(f"✅ Market data available: {len(rates)} bars")
            print(f"📊 Latest price: {rates[-1]['close']}")
        else:
            print("❌ No market data available")
    except Exception as e:
        print(f"❌ Error getting market data: {e}")
    
    # Cleanup
    mt5.shutdown()
    print("\n✅ Connection test completed!")
    return True

def check_mt5_status():
    """Check if MT5 is running"""
    print("\n🔍 === CHECKING MT5 STATUS ===")
    
    try:
        import psutil
        mt5_processes = []
        for proc in psutil.process_iter(['pid', 'name']):
            if 'terminal64' in proc.info['name'].lower() or 'metatrader' in proc.info['name'].lower():
                mt5_processes.append(proc.info)
        
        if mt5_processes:
            print("✅ MT5 processes found:")
            for proc in mt5_processes:
                print(f"   PID: {proc['pid']}, Name: {proc['name']}")
        else:
            print("❌ No MT5 processes found")
            print("🔧 Please start MetaTrader 5 manually")
    except ImportError:
        print("⚠️ psutil not available, cannot check processes")

if __name__ == "__main__":
    check_mt5_status()
    test_mt5_connection()
