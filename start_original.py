#!/usr/bin/env python3
"""
Start script untuk ORIGINAL TRADING SYSTEM
Equivalent dengan "python backend.main" untuk original mode
"""

import os
import sys
import uvicorn
from pathlib import Path

def set_original_mode():
    """Set environment variable untuk original mode"""
    os.environ["SCALPING_MODE"] = "false"
    print("✅ Original trading mode activated")

def print_original_banner():
    """Print banner untuk original system"""
    print("=" * 70)
    print("🚀 ORIGINAL TRADING SYSTEM")
    print("=" * 70)
    print("📈 AI-Powered Auto Trading System with GPT-4 Analysis")
    print("🧠 GPT-4 Multi-timeframe Analysis & Decision Making")
    print("⚡ Real-time Position Management & Trailing Stops")
    print("📊 WebSocket Real-time Updates & Risk Management")
    print("")
    print("🔧 KOMPONEN AKTIF:")
    print("   📊 Multi-timeframe Data Analysis (4h, 1h, 30m, 15m)")
    print("   🧠 GPT-4 Analysis & Decision Engine")
    print("   💰 Position Management & Trailing Stops")
    print("   📡 WebSocket Real-time Updates")
    print("   🛡️ Risk Management & Account Protection")
    print("   📈 Technical Analysis & Indicators")
    print("")
    print("⚙️ TRADING FEATURES:")
    print("   📊 Multi-timeframe GPT Analysis")
    print("   💰 Automatic Position Management")
    print("   🛡️ Advanced Risk Management")
    print("   📡 Real-time P&L Updates")
    print("   🔄 Trailing Stop Management")
    print("")
    print("🌐 API ENDPOINTS:")
    print("   Status: http://localhost:8001/api/trading/status")
    print("   Positions: http://localhost:8001/api/trading/positions")
    print("   Analysis: http://localhost:8001/api/trading/analyze")
    print("   WebSocket: ws://localhost:8001/ws")
    print("=" * 70)
    print("")

def main():
    """Main function untuk start original system"""
    print_original_banner()
    
    # Set original mode
    set_original_mode()
    
    # Create logs directory
    Path("logs").mkdir(exist_ok=True)
    
    print("🚀 Starting Original Trading System...")
    print("📊 API Server will be available at: http://localhost:8001")
    print("🔄 Trading Engine will start automatically")
    print("📡 WebSocket available at: ws://localhost:8001/ws")
    print("")
    print("💡 TIPS:")
    print("   - Check status: curl http://localhost:8001/api/trading/status")
    print("   - View positions: curl http://localhost:8001/api/trading/positions")
    print("   - Manual analysis: curl -X POST http://localhost:8001/api/trading/analyze")
    print("   - Stop system: Ctrl+C")
    print("")
    print("🔥 System starting in 3 seconds...")
    
    import time
    time.sleep(3)
    
    try:
        # Import and run the backend main with original mode
        from backend.main import app
        
        # Run uvicorn server
        uvicorn.run(
            app,
            host="0.0.0.0",
            port=8001,
            log_level="info"
        )
        
    except KeyboardInterrupt:
        print("\n🛑 Original trading system stopped by user")
    except Exception as e:
        print(f"\n❌ Error starting original system: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
