#!/usr/bin/env python3
"""
Test GPT-powered trailing stop functionality
"""
import requests
import json
import time
from datetime import datetime

def test_gpt_trailing_stop():
    """Test GPT trailing stop analysis"""
    print("🧠 TESTING GPT-POWERED TRAILING STOP")
    print("=" * 60)
    
    try:
        # 1. Start trading engine
        print("1. Starting trading engine...")
        response = requests.post("http://localhost:8001/api/trading/start", 
                               json={"auto_trading_enabled": True})
        
        if response.status_code == 200:
            print("✅ Trading engine started")
        else:
            print(f"❌ Failed to start engine: {response.status_code}")
            return
        
        # 2. Check current positions
        print("\n2. Checking current positions...")
        response = requests.get("http://localhost:8001/api/trading/positions")
        
        if response.status_code == 200:
            positions = response.json()
            print(f"   Found {len(positions)} positions")
            
            if len(positions) == 0:
                print("   No positions found. Creating a test position...")
                # Place a test order
                order_data = {
                    "symbol": "BTCUSDm",
                    "order_type": "BUY",
                    "lot_size": 0.01,
                    "comment": "GPT Trailing Stop Test"
                }
                
                response = requests.post("http://localhost:8001/api/trading/test-order", 
                                       json=order_data)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get("ticket"):
                        print(f"   ✅ Test position created: Ticket {result.get('ticket')}")
                        time.sleep(3)  # Wait for position to be established
                    else:
                        print(f"   ❌ Failed to create test position: {result.get('message')}")
                        return
                else:
                    print(f"   ❌ API Error: {response.status_code}")
                    return
            
            # Get updated positions
            response = requests.get("http://localhost:8001/api/trading/positions")
            if response.status_code == 200:
                positions = response.json()
                
                for pos in positions:
                    symbol = pos.get('symbol', 'Unknown')
                    ticket = pos.get('ticket', 'Unknown')
                    profit = pos.get('profit', 0)
                    entry_price = pos.get('entry_price', 0)
                    current_price = pos.get('current_price', 0)
                    
                    print(f"   📊 {symbol} (Ticket: {ticket})")
                    print(f"      Entry: ${entry_price:.5f}")
                    print(f"      Current: ${current_price:.5f}")
                    print(f"      P&L: ${profit:.2f}")
                    
                    if profit > 0:
                        print(f"      ✅ Position is profitable - GPT analysis will be used for trailing stop")
                    else:
                        print(f"      ⚠️ Position is not profitable - traditional risk management applies")
        else:
            print(f"❌ Failed to get positions: {response.status_code}")
            return
        
        # 3. Monitor for GPT trailing stop decisions
        print("\n3. Monitoring for GPT trailing stop decisions...")
        print("   📝 Watch backend logs for:")
        print("   🧠 'Requesting GPT analysis for trailing stop decision'")
        print("   🎯 'GPT recommends trailing stop' or 'GPT recommends continuing position'")
        print("   ✅ 'Position closed successfully' or ❌ 'Failed to close position'")
        
        print("\n4. Simulating position monitoring cycle...")
        for i in range(3):
            print(f"\n   Cycle {i+1}/3:")
            
            # Get current positions
            response = requests.get("http://localhost:8001/api/trading/positions")
            if response.status_code == 200:
                positions = response.json()
                
                if len(positions) > 0:
                    for pos in positions:
                        symbol = pos.get('symbol')
                        profit = pos.get('profit', 0)
                        
                        if profit > 0:
                            print(f"   📊 {symbol}: P&L ${profit:.2f} - Eligible for GPT trailing stop analysis")
                        else:
                            print(f"   📊 {symbol}: P&L ${profit:.2f} - Not profitable, no trailing stop")
                else:
                    print("   📊 No positions found")
                    break
            
            time.sleep(10)  # Wait 10 seconds between checks
        
        print("\n" + "=" * 60)
        print("🎉 GPT TRAILING STOP TEST COMPLETED")
        print("\n💡 WHAT HAPPENS NEXT:")
        print("1. 🔄 Background position management runs every 5 seconds")
        print("2. 🧠 For profitable positions, GPT analyzes market conditions")
        print("3. 🎯 GPT decides: CONTINUE (let it run) or TRAIL STOP (secure profits)")
        print("4. ✅ Only high-confidence GPT recommendations trigger trailing stop")
        print("5. 🔄 If GPT analysis fails, falls back to traditional trailing stop")
        
        print("\n📋 MONITOR BACKEND LOGS FOR:")
        print("✅ '🧠 Requesting GPT analysis for trailing stop decision'")
        print("✅ '🎯 GPT recommends trailing stop with high confidence'")
        print("✅ '✅ GPT recommends continuing position for more profit'")
        print("✅ '🔄 Falling back to traditional trailing stop'")
        
    except requests.exceptions.ConnectionError:
        print("❌ Cannot connect to backend server")
        print("💡 Make sure backend is running on port 8001")
    except Exception as e:
        print(f"❌ Error: {e}")

def test_manual_gpt_analysis():
    """Test manual GPT analysis for a specific position"""
    print("\n🔬 MANUAL GPT ANALYSIS TEST")
    print("=" * 40)
    
    try:
        # This would be a direct API call to test GPT analysis
        # For now, we'll just show what the logs should contain
        print("📝 Expected GPT Analysis Process:")
        print("1. 📊 Collect position data (entry, current price, P&L)")
        print("2. 📈 Get recent market data (last 10 candles)")
        print("3. 🧠 Send to GPT with position analysis prompt")
        print("4. 🎯 GPT analyzes trend continuation vs profit protection")
        print("5. ✅ Return decision: should_trail (true/false) + reasoning")
        
        print("\n📋 GPT Analysis Criteria:")
        print("✅ Market momentum strength")
        print("✅ Technical support/resistance levels")
        print("✅ Profit potential vs reversal risk")
        print("✅ Overall market conditions")
        print("✅ Risk/reward optimization")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    print(f"🕒 GPT Trailing Stop Test - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    test_gpt_trailing_stop()
    test_manual_gpt_analysis()
    print("\n" + "=" * 60)
    print("🚀 GPT-powered trailing stop system is now active!")
    print("📊 Monitor your positions and backend logs to see GPT decisions in action.")
