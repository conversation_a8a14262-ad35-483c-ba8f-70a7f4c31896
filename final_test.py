#!/usr/bin/env python3
"""
Final comprehensive test of the trading system
"""
import requests
import json
import time

BASE_URL = "http://localhost:8001"

def start_engine():
    """Start trading engine"""
    url = f"{BASE_URL}/api/trading/start"
    data = {"auto_trading_enabled": True}

    try:
        response = requests.post(url, json=data)
        print(f"Start Engine - Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Trading engine started successfully!")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error starting engine: {e}")
        return False

def check_status():
    """Check system status"""
    url = f"{BASE_URL}/api/trading/status"

    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            print(f"MT5 Connected: {'✅' if data.get('is_mt5_connected') else '❌'}")
            print(f"GPT Available: {'✅' if data.get('is_gpt_available') else '❌'}")
            print(f"Trading Enabled: {'✅' if data.get('is_trading_enabled') else '❌'}")
            print(f"Active Positions: {data.get('active_positions', 0)}")
            print(f"System Health: {data.get('system_health', 'UNKNOWN')}")
            return data
        else:
            print(f"❌ Status error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error getting status: {e}")
        return None

def test_account():
    """Test account endpoint"""
    url = f"{BASE_URL}/api/trading/account"

    try:
        response = requests.get(url)
        print(f"Account API - Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("✅ Account info retrieved successfully!")
            print(f"  Login: {data.get('login')}")
            print(f"  Balance: ${data.get('balance', 0):,.2f}")
            print(f"  Equity: ${data.get('equity', 0):,.2f}")
            print(f"  Currency: {data.get('currency')}")
            return True
        else:
            print(f"❌ Account error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error getting account: {e}")
        return False

def test_positions():
    """Test positions endpoint"""
    url = f"{BASE_URL}/api/trading/positions"

    try:
        response = requests.get(url)
        print(f"Positions API - Status: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ Found {len(data)} positions")
            return True
        else:
            print(f"❌ Positions error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error getting positions: {e}")
        return False

if __name__ == "__main__":
    print("🚀 === FINAL COMPREHENSIVE TEST ===")

    print("\n1. Starting trading engine...")
    if start_engine():
        print("\n2. Waiting for initialization...")
        time.sleep(8)  # Wait for full initialization

        print("\n3. Checking system status...")
        status = check_status()

        print("\n4. Testing account API...")
        test_account()

        print("\n5. Testing positions API...")
        test_positions()

        if status:
            mt5_ok = status.get('is_mt5_connected', False)
            gpt_ok = status.get('is_gpt_available', False)
            trading_ok = status.get('is_trading_enabled', False)

            print(f"\n🎯 === FINAL RESULTS ===")
            print(f"MT5 Connection: {'✅ WORKING' if mt5_ok else '❌ FAILED'}")
            print(f"GPT Analysis: {'✅ WORKING' if gpt_ok else '❌ FAILED'}")
            print(f"Trading Engine: {'✅ WORKING' if trading_ok else '❌ FAILED'}")

            if mt5_ok and gpt_ok and trading_ok:
                print(f"\n🎉 SUCCESS! All systems are operational!")
                print(f"✅ Backend API: Working")
                print(f"✅ MetaTrader 5: Connected")
                print(f"✅ GPT Analysis: Available")
                print(f"✅ Trading Symbols: {status.get('active_positions', 0)} positions")
                print(f"\nYour trading system is ready to use! 🚀")
            else:
                print(f"\n⚠️  Some systems need attention:")
                if not mt5_ok:
                    print(f"   - Check MT5 connection and credentials")
                if not gpt_ok:
                    print(f"   - Check OpenAI API key and model")
                if not trading_ok:
                    print(f"   - Check trading engine configuration")
        else:
            print(f"\n❌ Could not get system status")
    else:
        print(f"\n❌ Failed to start trading engine")
