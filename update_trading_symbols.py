#!/usr/bin/env python3
"""
Helper script to update trading symbols configuration
"""
import os
import re
from typing import List, Dict

def read_env_file(file_path: str = ".env") -> Dict[str, str]:
    """Read .env file and return as dictionary"""
    env_vars = {}
    
    if not os.path.exists(file_path):
        print(f"❌ {file_path} not found")
        return env_vars
    
    with open(file_path, 'r') as f:
        for line in f:
            line = line.strip()
            if line and not line.startswith('#') and '=' in line:
                key, value = line.split('=', 1)
                env_vars[key.strip()] = value.strip()
    
    return env_vars

def write_env_file(env_vars: Dict[str, str], file_path: str = ".env"):
    """Write dictionary back to .env file"""
    with open(file_path, 'w') as f:
        f.write("# OpenAI Configuration\n")
        f.write(f"OPENAI_API_KEY={env_vars.get('OPENAI_API_KEY', '')}\n")
        f.write(f"OPENAI_MODEL={env_vars.get('OPENAI_MODEL', 'gpt-4o-mini')}\n\n")
        
        f.write("# MetaTrader 5 Configuration\n")
        f.write(f"MT5_LOGIN={env_vars.get('MT5_LOGIN', '')}\n")
        f.write(f"MT5_PASSWORD={env_vars.get('MT5_PASSWORD', '')}\n")
        f.write(f"MT5_SERVER={env_vars.get('MT5_SERVER', '')}\n\n")
        
        f.write("# Trading Configuration\n")
        f.write(f"DEFAULT_LOT_SIZE={env_vars.get('DEFAULT_LOT_SIZE', '0.01')}\n")
        f.write(f"MAX_RISK_PERCENT={env_vars.get('MAX_RISK_PERCENT', '2.0')}\n")
        f.write(f"STOP_LOSS_PIPS={env_vars.get('STOP_LOSS_PIPS', '50')}\n")
        f.write(f"TAKE_PROFIT_PIPS={env_vars.get('TAKE_PROFIT_PIPS', '100')}\n")
        f.write(f"TRAILING_STOP_PIPS={env_vars.get('TRAILING_STOP_PIPS', '30')}\n\n")
        
        f.write("# Symbols to Trade\n")
        f.write(f"TRADING_SYMBOLS={env_vars.get('TRADING_SYMBOLS', '')}\n\n")
        
        f.write("# API Configuration\n")
        f.write(f"API_HOST={env_vars.get('API_HOST', '0.0.0.0')}\n")
        f.write(f"API_PORT={env_vars.get('API_PORT', '8000')}\n")
        f.write(f"API_RELOAD={env_vars.get('API_RELOAD', 'true')}\n\n")
        
        f.write("# Database Configuration (Optional)\n")
        f.write(f"DATABASE_URL={env_vars.get('DATABASE_URL', 'sqlite:///./trading.db')}\n\n")
        
        f.write("# Logging Configuration\n")
        f.write(f"LOG_LEVEL={env_vars.get('LOG_LEVEL', 'INFO')}\n")
        f.write(f"LOG_FILE={env_vars.get('LOG_FILE', 'trading.log')}\n\n")
        
        f.write("# Risk Management\n")
        f.write(f"MAX_OPEN_POSITIONS={env_vars.get('MAX_OPEN_POSITIONS', '5')}\n")
        f.write(f"MAX_DAILY_LOSS={env_vars.get('MAX_DAILY_LOSS', '1000')}\n")
        f.write(f"MAX_DRAWDOWN_PERCENT={env_vars.get('MAX_DRAWDOWN_PERCENT', '10')}\n\n")
        
        f.write("# GPT Analysis Configuration\n")
        f.write(f"ANALYSIS_INTERVAL_MINUTES={env_vars.get('ANALYSIS_INTERVAL_MINUTES', '5')}\n")
        f.write(f"MARKET_DATA_LOOKBACK_HOURS={env_vars.get('MARKET_DATA_LOOKBACK_HOURS', '24')}\n\n")
        
        f.write("# Security\n")
        f.write(f"SECRET_KEY={env_vars.get('SECRET_KEY', '')}\n")
        f.write(f"ALGORITHM={env_vars.get('ALGORITHM', 'HS256')}\n")
        f.write(f"ACCESS_TOKEN_EXPIRE_MINUTES={env_vars.get('ACCESS_TOKEN_EXPIRE_MINUTES', '30')}\n")

def update_config_py(symbols: List[str]):
    """Update backend/config.py with new symbol configurations"""
    config_path = "backend/config.py"
    
    if not os.path.exists(config_path):
        print(f"❌ {config_path} not found")
        return False
    
    # Read current config
    with open(config_path, 'r') as f:
        content = f.read()
    
    # Generate new symbol config
    symbol_configs = []
    for symbol in symbols:
        # Determine pip value based on symbol type
        if any(pair in symbol.upper() for pair in ['EURUSD', 'GBPUSD', 'USDCHF', 'AUDUSD', 'USDCAD', 'NZDUSD']):
            pip_value = 0.0001
            spread_threshold = 3.0
        elif 'XAU' in symbol.upper() or 'GOLD' in symbol.upper():
            pip_value = 0.01
            spread_threshold = 5.0
        elif any(crypto in symbol.upper() for crypto in ['BTC', 'ETH', 'LTC']):
            pip_value = 1.0
            spread_threshold = 50.0
        elif any(index in symbol.upper() for index in ['US30', 'US500', 'NAS100']):
            pip_value = 1.0
            spread_threshold = 10.0
        else:
            pip_value = 0.01
            spread_threshold = 10.0
        
        symbol_config = f'''    "{symbol}": {{
        "pip_value": {pip_value},
        "min_lot": 0.01,
        "max_lot": 100.0,
        "lot_step": 0.01,
        "spread_threshold": {spread_threshold}
    }}'''
        symbol_configs.append(symbol_config)
    
    # Create new SYMBOL_CONFIG
    new_symbol_config = "# Symbol Configuration\nSYMBOL_CONFIG = {\n" + ",\n".join(symbol_configs) + "\n}\n"
    
    # Replace old SYMBOL_CONFIG
    pattern = r'# Symbol Configuration\nSYMBOL_CONFIG = \{[^}]*\}'
    if re.search(pattern, content, re.DOTALL):
        content = re.sub(pattern, new_symbol_config.strip(), content, flags=re.DOTALL)
    else:
        # If not found, append at the end
        content += "\n\n" + new_symbol_config
    
    # Write back to file
    with open(config_path, 'w') as f:
        f.write(content)
    
    return True

def get_popular_symbols():
    """Get list of popular trading symbols"""
    return {
        "Forex Major Pairs": [
            "EURUSD", "GBPUSD", "USDJPY", "USDCHF", 
            "AUDUSD", "USDCAD", "NZDUSD"
        ],
        "Forex Minor Pairs": [
            "EURJPY", "GBPJPY", "EURGBP", "AUDJPY",
            "EURAUD", "EURCHF", "GBPCHF"
        ],
        "Precious Metals": [
            "XAUUSD", "XAGUSD", "GOLD", "SILVER"
        ],
        "Cryptocurrencies": [
            "BTCUSD", "ETHUSD", "LTCUSD", "XRPUSD",
            "ADAUSD", "DOTUSD", "LINKUSD"
        ],
        "Stock Indices": [
            "US30", "US500", "NAS100", "UK100",
            "GER30", "FRA40", "JPN225"
        ],
        "Commodities": [
            "USOIL", "UKOIL", "NGAS", "WHEAT",
            "CORN", "SUGAR", "COFFEE"
        ]
    }

def main():
    """Main function"""
    print("🔧 === Trading Symbols Configuration Tool ===")
    
    # Read current configuration
    env_vars = read_env_file()
    current_symbols = env_vars.get('TRADING_SYMBOLS', '').split(',')
    current_symbols = [s.strip() for s in current_symbols if s.strip()]
    
    print(f"\n📊 Current symbols: {current_symbols}")
    
    while True:
        print(f"\n🎯 === OPTIONS ===")
        print("1. View popular symbols by category")
        print("2. Add symbols manually")
        print("3. Replace all symbols")
        print("4. Remove symbols")
        print("5. Save and exit")
        print("0. Exit without saving")
        
        choice = input("\nSelect option (0-5): ").strip()
        
        if choice == "0":
            print("👋 Exiting without saving")
            break
        elif choice == "1":
            popular = get_popular_symbols()
            for category, symbols in popular.items():
                print(f"\n📈 {category}:")
                for i, symbol in enumerate(symbols, 1):
                    status = "✅" if symbol in current_symbols else "⭕"
                    print(f"  {i}. {symbol} {status}")
        elif choice == "2":
            new_symbols = input("\nEnter symbols (comma-separated): ").strip()
            if new_symbols:
                symbols_to_add = [s.strip().upper() for s in new_symbols.split(',')]
                for symbol in symbols_to_add:
                    if symbol not in current_symbols:
                        current_symbols.append(symbol)
                        print(f"✅ Added {symbol}")
                    else:
                        print(f"⚠️  {symbol} already exists")
        elif choice == "3":
            new_symbols = input("\nEnter new symbols (comma-separated): ").strip()
            if new_symbols:
                current_symbols = [s.strip().upper() for s in new_symbols.split(',')]
                print(f"✅ Replaced with: {current_symbols}")
        elif choice == "4":
            if not current_symbols:
                print("❌ No symbols to remove")
                continue
            print(f"\nCurrent symbols:")
            for i, symbol in enumerate(current_symbols, 1):
                print(f"  {i}. {symbol}")
            
            to_remove = input("\nEnter symbol numbers to remove (comma-separated): ").strip()
            if to_remove:
                try:
                    indices = [int(x.strip()) - 1 for x in to_remove.split(',')]
                    for i in sorted(indices, reverse=True):
                        if 0 <= i < len(current_symbols):
                            removed = current_symbols.pop(i)
                            print(f"✅ Removed {removed}")
                except ValueError:
                    print("❌ Invalid input")
        elif choice == "5":
            if not current_symbols:
                print("❌ No symbols configured")
                continue
            
            print(f"\n💾 Saving configuration...")
            print(f"Symbols: {current_symbols}")
            
            # Update .env file
            env_vars['TRADING_SYMBOLS'] = ','.join(current_symbols)
            write_env_file(env_vars)
            print("✅ Updated .env file")
            
            # Update config.py
            if update_config_py(current_symbols):
                print("✅ Updated backend/config.py")
            else:
                print("❌ Failed to update backend/config.py")
            
            print(f"\n🎉 Configuration saved successfully!")
            print(f"📝 Next steps:")
            print(f"   1. Restart the trading engine")
            print(f"   2. Check that symbols are available in your MT5 broker")
            print(f"   3. Monitor the logs for any symbol-related errors")
            break
        else:
            print("❌ Invalid option")

if __name__ == "__main__":
    main()
