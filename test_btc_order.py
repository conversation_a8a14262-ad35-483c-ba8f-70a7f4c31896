#!/usr/bin/env python3
"""
Test BTCUSDm order placement specifically
"""
import requests
import time

def test_btc_order():
    """Test BTCUSDm order placement using new endpoint"""
    print("🎯 TESTING BTCUSDm ORDER PLACEMENT")
    print("=" * 60)
    
    # Start trading engine
    print("\n📋 STEP 1: START TRADING ENGINE")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        if response.status_code == 200:
            print("✅ Trading engine started successfully")
        else:
            print(f"❌ Failed to start: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Wait for initialization
    print("\n⏳ Waiting 3 seconds for initialization...")
    time.sleep(3)
    
    # Test BTCUSDm order using new endpoint
    print("\n📋 STEP 2: PLACE BTCUSDm ORDER")
    print("⚠️  ATTEMPTING TO PLACE REAL BTCUSDm ORDER!")
    print("📊 Order Details:")
    print("   - Symbol: BTCUSDm")
    print("   - Type: BUY")
    print("   - Lot Size: 0.01")
    print("   - Comment: Custom Test")
    
    try:
        # Use new custom symbol endpoint
        response = requests.post(
            "http://localhost:8001/api/trading/test-order/BTCUSDm?lot_size=0.01", 
            timeout=15
        )
        print(f"\n📡 API Response: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("📋 Response Details:")
            print(f"   Message: {result.get('message')}")
            print(f"   Ticket: {result.get('ticket')}")
            print(f"   Symbol: {result.get('symbol')}")
            print(f"   Lot Size: {result.get('lot_size')}")
            print(f"   Price: {result.get('price')}")
            
            if result.get('ticket'):
                print("\n🎉 SUCCESS! BTCUSDm ORDER PLACED!")
                print("✅ Check your MT5 terminal for the order")
                
                # Check positions
                print("\n📋 STEP 3: VERIFY POSITION")
                time.sleep(2)
                pos_response = requests.get("http://localhost:8001/api/trading/positions", timeout=5)
                if pos_response.status_code == 200:
                    positions = pos_response.json()
                    print(f"✅ Total positions: {len(positions)}")
                    
                    btc_found = False
                    for pos in positions:
                        if pos.get('symbol') == 'BTCUSDm':
                            print(f"   📈 BTCUSDm - {pos.get('order_type')} - {pos.get('lot_size')} lots")
                            print(f"   💰 Entry Price: ${pos.get('entry_price'):.2f}")
                            btc_found = True
                    
                    if not btc_found:
                        print("   ⚠️  BTCUSDm position not found (may have been closed quickly)")
                
                return True
            else:
                print("\n❌ BTCUSDm ORDER FAILED")
                print(f"   Error: {result.get('error')}")
                return False
        else:
            print(f"❌ API call failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

def test_via_force_analysis():
    """Alternative: Test BTCUSDm via force analysis"""
    print("\n🔄 ALTERNATIVE: FORCE ANALYSIS FOR BTCUSDm")
    print("=" * 50)
    
    try:
        # Force analysis for BTCUSDm
        data = {"symbol": "BTCUSDm"}
        response = requests.post("http://localhost:8001/api/trading/analyze", json=data, timeout=15)
        
        if response.status_code == 200:
            result = response.json()
            print(f"✅ Analysis triggered: {result.get('message')}")
            
            print("\n⏳ Waiting 10 seconds for potential order...")
            time.sleep(10)
            
            # Check positions
            pos_response = requests.get("http://localhost:8001/api/trading/positions", timeout=5)
            if pos_response.status_code == 200:
                positions = pos_response.json()
                print(f"📈 Current positions: {len(positions)}")
                
                for pos in positions:
                    if pos.get('symbol') == 'BTCUSDm':
                        print(f"✅ FOUND BTCUSDm POSITION!")
                        print(f"   Type: {pos.get('order_type')}")
                        print(f"   Lot Size: {pos.get('lot_size')}")
                        print(f"   Entry Price: ${pos.get('entry_price'):.2f}")
                        return True
                
                print("❌ No BTCUSDm position found")
                print("   Possible reasons:")
                print("   - GPT gave HOLD signal")
                print("   - Insufficient margin")
                print("   - Order failed")
                return False
        else:
            print(f"❌ Analysis failed: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error: {e}")
        return False

if __name__ == "__main__":
    print("🎯 BTCUSDm ORDER PLACEMENT TEST")
    print("This will test BTCUSDm order placement specifically")
    print("Based on previous success: Ticket 359227388")
    print("=" * 60)
    
    print("\n💰 MARGIN ANALYSIS:")
    print("   BTCUSDm Price: ~$108,800")
    print("   Position Value: $1,088 (0.01 lots)")
    print("   Margin Needed: ~$110 (10:1 leverage)")
    print("   Available Margin: ~$16.33")
    print("   ⚠️  INSUFFICIENT MARGIN - Order may fail!")
    
    print("\n🎯 TESTING OPTIONS:")
    print("1. Direct BTCUSDm order (may fail due to margin)")
    print("2. Force analysis (triggers natural trading)")
    
    choice = input("\nChoose test method (1 or 2, default: 2): ").strip()
    
    if choice == "1":
        print("\n🚀 Testing direct BTCUSDm order...")
        success = test_btc_order()
    else:
        print("\n🚀 Testing via force analysis...")
        success = test_via_force_analysis()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 BTCUSDm TEST SUCCESSFUL!")
        print("✅ Order placed and position found")
        print("✅ System working correctly for BTCUSDm")
        print("⚠️  Monitor position in MT5 terminal")
    else:
        print("❌ BTCUSDm TEST FAILED")
        print("🔍 Possible reasons:")
        print("   - Insufficient margin ($110 needed, $16.33 available)")
        print("   - Market conditions")
        print("   - GPT analysis gave HOLD signal")
        print("   - Check backend logs for details")
    
    print("\n🌐 Check backend terminal for detailed logs")
    print("📊 Previous success: BTCUSDm Ticket 359227388 was placed and closed successfully!")
