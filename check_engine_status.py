#!/usr/bin/env python3
"""
Check Trading Engine Status
"""
import requests
import json

BASE_URL = "http://localhost:8001"

def check_status():
    """Check system status"""
    print("🔍 === CHECKING TRADING ENGINE STATUS ===")

    url = f"{BASE_URL}/api/trading/status"

    try:
        response = requests.get(url)
        print(f"📡 API Response: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print("\n📊 SYSTEM STATUS:")
            print(f"   MT5 Connected: {'✅' if data.get('is_mt5_connected') else '❌'}")
            print(f"   GPT Available: {'✅' if data.get('is_gpt_available') else '❌'}")
            print(f"   Trading Enabled: {'✅' if data.get('is_trading_enabled') else '❌'}")
            print(f"   Active Positions: {data.get('active_positions', 0)}")
            print(f"   System Health: {data.get('system_health', 'UNKNOWN')}")
            print(f"   Daily Profit: ${data.get('daily_profit', 0)}")

            if data.get('error_messages'):
                print(f"\n❌ ERRORS:")
                for error in data.get('error_messages', []):
                    print(f"   - {error}")

            return data
        else:
            print(f"❌ Status error: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error getting status: {e}")
        return None

def start_engine():
    """Start trading engine"""
    print("\n🚀 === STARTING TRADING ENGINE ===")

    url = f"{BASE_URL}/api/trading/start"
    data = {"auto_trading_enabled": True}

    try:
        response = requests.post(url, json=data)
        print(f"📡 Start Response: {response.status_code}")

        if response.status_code == 200:
            data = response.json()
            print(f"✅ {data.get('message', 'Engine started')}")
            return True
        else:
            print(f"❌ Start error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error starting engine: {e}")
        return False

def main():
    """Main function"""
    # Check current status
    status = check_status()

    if not status:
        print("\n❌ Cannot get system status")
        return

    # If MT5 not connected, try to start engine
    if not status.get('is_mt5_connected'):
        print("\n⚠️ MT5 not connected. Attempting to start trading engine...")

        if start_engine():
            print("\n⏳ Waiting for engine to initialize...")
            import time
            time.sleep(3)

            # Check status again
            print("\n🔄 Checking status after start...")
            new_status = check_status()

            if new_status and new_status.get('is_mt5_connected'):
                print("\n🎉 SUCCESS! MT5 is now connected!")
            else:
                print("\n❌ MT5 still not connected. Check the issues below:")
                print("\n🔧 TROUBLESHOOTING STEPS:")
                print("1. Make sure MetaTrader 5 is running")
                print("2. Login manually to MT5 with your credentials")
                print("3. Check internet connection")
                print("4. Verify .env credentials are correct")
                print("5. Check if account is active and trading is allowed")
        else:
            print("\n❌ Failed to start trading engine")
    else:
        print("\n✅ MT5 is already connected!")

if __name__ == "__main__":
    main()
