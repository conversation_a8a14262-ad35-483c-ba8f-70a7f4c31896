#!/usr/bin/env python3
import requests

BASE_URL = "http://localhost:8001"

# Test current symbols
response = requests.get(f"{BASE_URL}/api/trading/symbols/current")
if response.status_code == 200:
    data = response.json()
    symbols = data.get('symbols', [])
    print(f"✅ Current configured symbols ({len(symbols)}):")
    for i, symbol in enumerate(symbols, 1):
        print(f"  {i}. {symbol}")
else:
    print(f"❌ Error: {response.text}")
