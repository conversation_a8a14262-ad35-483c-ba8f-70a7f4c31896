#!/usr/bin/env python3
"""
Auto Trading System Monitoring Script
Monitor system health, performance, and trading metrics
"""
import asyncio
import time
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Any

# Add backend to path
sys.path.append(str(Path(__file__).parent))

from backend.services.trading_engine import TradingEngine
from backend.models.trading_models import SystemStatus, Position
from backend.config import settings

class SystemMonitor:
    """System monitoring and alerting"""
    
    def __init__(self):
        self.trading_engine = None
        self.monitoring_data = []
        self.alerts = []
        
    async def initialize(self):
        """Initialize monitoring"""
        self.trading_engine = TradingEngine()
        await self.trading_engine.initialize()
        
    async def collect_metrics(self) -> Dict[str, Any]:
        """Collect system metrics"""
        try:
            # Get system status
            system_status = await self.trading_engine.get_system_status()
            
            # Get positions
            positions = await self.trading_engine.get_positions()
            
            # Get account info
            account_info = self.trading_engine.account_info
            
            # Calculate metrics
            open_positions = [p for p in positions if p.status.value == "OPEN"]
            total_profit = sum(p.profit or 0 for p in positions)
            unrealized_pnl = sum(p.profit or 0 for p in open_positions)
            
            # Calculate daily metrics
            today = datetime.now().date()
            daily_positions = [
                p for p in positions 
                if p.close_time and p.close_time.date() == today
            ]
            daily_profit = sum(p.profit or 0 for p in daily_positions)
            
            metrics = {
                "timestamp": datetime.now().isoformat(),
                "system_health": system_status.system_health if system_status else "UNKNOWN",
                "mt5_connected": system_status.is_mt5_connected if system_status else False,
                "gpt_available": system_status.is_gpt_available if system_status else False,
                "trading_enabled": system_status.is_trading_enabled if system_status else False,
                "account_balance": account_info.balance if account_info else 0,
                "account_equity": account_info.equity if account_info else 0,
                "total_positions": len(positions),
                "open_positions": len(open_positions),
                "total_profit": total_profit,
                "unrealized_pnl": unrealized_pnl,
                "daily_profit": daily_profit,
                "daily_trades": len(daily_positions),
                "margin_level": account_info.margin_level if account_info else 0,
                "free_margin": account_info.free_margin if account_info else 0
            }
            
            return metrics
            
        except Exception as e:
            print(f"Error collecting metrics: {e}")
            return {
                "timestamp": datetime.now().isoformat(),
                "error": str(e)
            }
    
    def check_alerts(self, metrics: Dict[str, Any]):
        """Check for alert conditions"""
        alerts = []
        
        # System health alerts
        if metrics.get("system_health") == "ERROR":
            alerts.append({
                "level": "CRITICAL",
                "message": "System health is ERROR",
                "timestamp": datetime.now().isoformat()
            })
        
        # Connection alerts
        if not metrics.get("mt5_connected"):
            alerts.append({
                "level": "CRITICAL",
                "message": "MetaTrader 5 disconnected",
                "timestamp": datetime.now().isoformat()
            })
        
        if not metrics.get("gpt_available"):
            alerts.append({
                "level": "WARNING",
                "message": "GPT API unavailable",
                "timestamp": datetime.now().isoformat()
            })
        
        # Trading alerts
        daily_profit = metrics.get("daily_profit", 0)
        if daily_profit < -settings.max_daily_loss:
            alerts.append({
                "level": "CRITICAL",
                "message": f"Daily loss limit exceeded: {daily_profit}",
                "timestamp": datetime.now().isoformat()
            })
        
        # Margin alerts
        margin_level = metrics.get("margin_level", 0)
        if margin_level > 0 and margin_level < 200:  # 200% margin level threshold
            alerts.append({
                "level": "WARNING",
                "message": f"Low margin level: {margin_level}%",
                "timestamp": datetime.now().isoformat()
            })
        
        # Position count alerts
        open_positions = metrics.get("open_positions", 0)
        if open_positions >= settings.max_open_positions:
            alerts.append({
                "level": "WARNING",
                "message": f"Maximum positions reached: {open_positions}",
                "timestamp": datetime.now().isoformat()
            })
        
        return alerts
    
    def save_metrics(self, metrics: Dict[str, Any]):
        """Save metrics to file"""
        try:
            # Create logs directory if it doesn't exist
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)
            
            # Save to daily file
            date_str = datetime.now().strftime("%Y-%m-%d")
            metrics_file = logs_dir / f"metrics_{date_str}.jsonl"
            
            with open(metrics_file, "a") as f:
                f.write(json.dumps(metrics) + "\n")
                
        except Exception as e:
            print(f"Error saving metrics: {e}")
    
    def save_alerts(self, alerts: List[Dict[str, Any]]):
        """Save alerts to file"""
        if not alerts:
            return
            
        try:
            # Create logs directory if it doesn't exist
            logs_dir = Path("logs")
            logs_dir.mkdir(exist_ok=True)
            
            # Save to alerts file
            alerts_file = logs_dir / "alerts.jsonl"
            
            with open(alerts_file, "a") as f:
                for alert in alerts:
                    f.write(json.dumps(alert) + "\n")
                    
        except Exception as e:
            print(f"Error saving alerts: {e}")
    
    def print_status(self, metrics: Dict[str, Any], alerts: List[Dict[str, Any]]):
        """Print current status"""
        print(f"\n{'='*60}")
        print(f"Auto Trading System Monitor - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"{'='*60}")
        
        # System status
        print(f"System Health: {metrics.get('system_health', 'UNKNOWN')}")
        print(f"MT5 Connected: {'✅' if metrics.get('mt5_connected') else '❌'}")
        print(f"GPT Available: {'✅' if metrics.get('gpt_available') else '❌'}")
        print(f"Trading Enabled: {'✅' if metrics.get('trading_enabled') else '❌'}")
        
        # Account info
        print(f"\nAccount Information:")
        print(f"Balance: ${metrics.get('account_balance', 0):,.2f}")
        print(f"Equity: ${metrics.get('account_equity', 0):,.2f}")
        print(f"Free Margin: ${metrics.get('free_margin', 0):,.2f}")
        print(f"Margin Level: {metrics.get('margin_level', 0):.1f}%")
        
        # Trading info
        print(f"\nTrading Information:")
        print(f"Total Positions: {metrics.get('total_positions', 0)}")
        print(f"Open Positions: {metrics.get('open_positions', 0)}")
        print(f"Total P&L: ${metrics.get('total_profit', 0):,.2f}")
        print(f"Unrealized P&L: ${metrics.get('unrealized_pnl', 0):,.2f}")
        print(f"Daily P&L: ${metrics.get('daily_profit', 0):,.2f}")
        print(f"Daily Trades: {metrics.get('daily_trades', 0)}")
        
        # Alerts
        if alerts:
            print(f"\n⚠️ ALERTS ({len(alerts)}):")
            for alert in alerts:
                level_emoji = "🔴" if alert["level"] == "CRITICAL" else "🟡"
                print(f"{level_emoji} {alert['level']}: {alert['message']}")
        else:
            print(f"\n✅ No alerts")
        
        print(f"{'='*60}")
    
    async def run_monitoring(self, interval: int = 60):
        """Run continuous monitoring"""
        print("🔍 Starting Auto Trading System Monitor...")
        print(f"Monitoring interval: {interval} seconds")
        
        try:
            await self.initialize()
            
            while True:
                # Collect metrics
                metrics = await self.collect_metrics()
                
                # Check for alerts
                alerts = self.check_alerts(metrics)
                
                # Save data
                self.save_metrics(metrics)
                self.save_alerts(alerts)
                
                # Store in memory for analysis
                self.monitoring_data.append(metrics)
                self.alerts.extend(alerts)
                
                # Keep only last 1000 entries
                if len(self.monitoring_data) > 1000:
                    self.monitoring_data = self.monitoring_data[-1000:]
                
                # Print status
                self.print_status(metrics, alerts)
                
                # Wait for next iteration
                await asyncio.sleep(interval)
                
        except KeyboardInterrupt:
            print("\n🛑 Monitoring stopped by user")
        except Exception as e:
            print(f"\n❌ Monitoring error: {e}")
        finally:
            if self.trading_engine:
                await self.trading_engine.stop()
    
    def generate_report(self, hours: int = 24) -> Dict[str, Any]:
        """Generate performance report"""
        try:
            # Filter data for the specified time period
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_data = [
                m for m in self.monitoring_data
                if datetime.fromisoformat(m["timestamp"]) > cutoff_time
            ]
            
            if not recent_data:
                return {"error": "No data available for the specified period"}
            
            # Calculate statistics
            profits = [m.get("daily_profit", 0) for m in recent_data]
            balances = [m.get("account_balance", 0) for m in recent_data]
            
            report = {
                "period_hours": hours,
                "data_points": len(recent_data),
                "avg_daily_profit": sum(profits) / len(profits) if profits else 0,
                "max_daily_profit": max(profits) if profits else 0,
                "min_daily_profit": min(profits) if profits else 0,
                "current_balance": balances[-1] if balances else 0,
                "balance_change": balances[-1] - balances[0] if len(balances) > 1 else 0,
                "total_alerts": len([a for a in self.alerts if 
                                   datetime.fromisoformat(a["timestamp"]) > cutoff_time]),
                "critical_alerts": len([a for a in self.alerts if 
                                      a["level"] == "CRITICAL" and 
                                      datetime.fromisoformat(a["timestamp"]) > cutoff_time])
            }
            
            return report
            
        except Exception as e:
            return {"error": f"Error generating report: {e}"}

async def main():
    """Main monitoring function"""
    import argparse
    
    parser = argparse.ArgumentParser(description="Auto Trading System Monitor")
    parser.add_argument("--interval", type=int, default=60, 
                       help="Monitoring interval in seconds (default: 60)")
    parser.add_argument("--report", type=int, 
                       help="Generate report for last N hours and exit")
    
    args = parser.parse_args()
    
    monitor = SystemMonitor()
    
    if args.report:
        # Generate report mode
        print(f"📊 Generating report for last {args.report} hours...")
        await monitor.initialize()
        
        # Load existing data if available
        try:
            logs_dir = Path("logs")
            for metrics_file in logs_dir.glob("metrics_*.jsonl"):
                with open(metrics_file, "r") as f:
                    for line in f:
                        data = json.loads(line.strip())
                        monitor.monitoring_data.append(data)
        except Exception as e:
            print(f"Warning: Could not load historical data: {e}")
        
        report = monitor.generate_report(args.report)
        print(json.dumps(report, indent=2))
        
    else:
        # Continuous monitoring mode
        await monitor.run_monitoring(args.interval)

if __name__ == "__main__":
    asyncio.run(main())
