#!/usr/bin/env python3
"""
Test starting trading engine with correct symbols
"""
import requests
import json
import time

BASE_URL = "http://localhost:8001"

def start_engine():
    """Start trading engine"""
    url = f"{BASE_URL}/api/trading/start"
    data = {"auto_trading_enabled": True}

    try:
        response = requests.post(url, json=data)
        print(f"Start Engine - Status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Trading engine started successfully!")
            return True
        else:
            print(f"❌ Error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error starting engine: {e}")
        return False

def check_status():
    """Check system status"""
    url = f"{BASE_URL}/api/trading/status"

    try:
        response = requests.get(url)
        if response.status_code == 200:
            data = response.json()
            print(f"MT5 Connected: {'✅' if data.get('is_mt5_connected') else '❌'}")
            print(f"GPT Available: {'✅' if data.get('is_gpt_available') else '❌'}")
            print(f"Trading Enabled: {'✅' if data.get('is_trading_enabled') else '❌'}")
            return data
        else:
            print(f"❌ Status check failed: {response.text}")
            return None
    except Exception as e:
        print(f"❌ Error checking status: {e}")
        return None

if __name__ == "__main__":
    print("=== Testing Trading Engine with Correct Symbols ===")

    print("\n1. Checking initial status...")
    check_status()

    print("\n2. Starting trading engine...")
    if start_engine():
        print("\n3. Waiting for initialization...")
        time.sleep(5)

        print("\n4. Checking final status...")
        status = check_status()

        if status and status.get('is_mt5_connected') and status.get('is_gpt_available'):
            print("\n🎉 SUCCESS! All systems are operational!")
        else:
            print("\n⚠️  Some systems are still not ready. Check logs for details.")
    else:
        print("\n❌ Failed to start trading engine")
