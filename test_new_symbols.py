#!/usr/bin/env python3
"""
Test new symbols configuration
"""
import requests
import time

BASE_URL = "http://localhost:8000"

def test_new_symbols():
    """Test the new symbols configuration"""
    print("🔧 === TESTING NEW SYMBOLS CONFIGURATION ===")
    
    # Test current symbols
    try:
        response = requests.get(f"{BASE_URL}/api/trading/symbols/current")
        if response.status_code == 200:
            data = response.json()
            symbols = data.get('symbols', [])
            print(f"\n✅ Current configured symbols ({len(symbols)}):")
            for i, symbol in enumerate(symbols, 1):
                print(f"  {i}. {symbol}")
        else:
            print(f"❌ Error getting current symbols: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")
    
    # Start trading engine to test new symbols
    print(f"\n🚀 Starting trading engine to test new symbols...")
    try:
        response = requests.post(f"{BASE_URL}/api/trading/start", json={"auto_trading_enabled": True})
        if response.status_code == 200:
            print("✅ Trading engine start request sent")
            
            # Wait for initialization
            print("⏳ Waiting for initialization...")
            time.sleep(10)
            
            # Check status
            response = requests.get(f"{BASE_URL}/api/trading/status")
            if response.status_code == 200:
                status = response.json()
                print(f"\n📊 System Status:")
                print(f"  MT5 Connected: {'✅' if status.get('is_mt5_connected') else '❌'}")
                print(f"  GPT Available: {'✅' if status.get('is_gpt_available') else '❌'}")
                print(f"  Trading Enabled: {'✅' if status.get('is_trading_enabled') else '❌'}")
                
                if status.get('is_mt5_connected'):
                    print(f"\n🎉 SUCCESS! New symbols should be processed by the trading engine.")
                    print(f"📝 Check the backend logs to see:")
                    print(f"   - Symbol selection status")
                    print(f"   - Market data retrieval")
                    print(f"   - GPT analysis for each symbol")
                else:
                    print(f"\n⚠️  MT5 not connected. Check configuration.")
            else:
                print(f"❌ Error getting status: {response.text}")
        else:
            print(f"❌ Error starting engine: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")

def show_symbol_info():
    """Show information about the new symbols"""
    print(f"\n📋 === NEW SYMBOLS INFORMATION ===")
    
    new_symbols = {
        "USDBRL": "USD/Brazilian Real - High volatility, good for swing trading",
        "USDCNY": "USD/Chinese Yuan - Stable, good liquidity",
        "USDINR": "USD/Indian Rupee - Moderate volatility, emerging market",
        "USDKRW": "USD/Korean Won - Good volatility, Asian market hours"
    }
    
    print(f"\n🆕 Added Symbols:")
    for symbol, description in new_symbols.items():
        print(f"  📈 {symbol}: {description}")
    
    print(f"\n⚙️  Configuration Details:")
    print(f"  - Total symbols: 8 (4 original + 4 new)")
    print(f"  - All symbols are USD exotic pairs")
    print(f"  - Spread thresholds: 10-20 pips")
    print(f"  - Minimum lot size: 0.01")
    print(f"  - Maximum lot size: 100.0")
    
    print(f"\n📊 Expected Behavior:")
    print(f"  - Trading engine will process all 8 symbols")
    print(f"  - Each symbol analyzed every ~60 seconds")
    print(f"  - GPT analysis for each symbol")
    print(f"  - More trading opportunities")
    print(f"  - Diversified across different currencies")

if __name__ == "__main__":
    show_symbol_info()
    test_new_symbols()
    
    print(f"\n🎯 === NEXT STEPS ===")
    print(f"1. Monitor backend logs for symbol processing")
    print(f"2. Check frontend for new symbols in dashboard")
    print(f"3. Observe GPT analysis for each symbol")
    print(f"4. Monitor trading signals and opportunities")
    print(f"5. Adjust spread thresholds if needed")
    
    print(f"\n📞 === TROUBLESHOOTING ===")
    print(f"If symbols not working:")
    print(f"  - Restart backend completely")
    print(f"  - Check .env file for correct symbol list")
    print(f"  - Check config.py for symbol configurations")
    print(f"  - Verify symbols exist in your MT5 broker")
    print(f"  - Check backend logs for errors")
