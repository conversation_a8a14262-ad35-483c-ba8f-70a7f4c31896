"""
TRIGGER MONITOR
- Cek harga real-time tiap 1-5 menit
- Hitung persentase pergerakan dari reference_price 15 menit terakhir
- Pair-specific thresholds untuk trigger GPT analyze
- Cooldown timer untuk mencegah over-analysis
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, Optional, Any
from backend.services.data_fetcher import DataFetcher

logger = logging.getLogger(__name__)

class TriggerMonitor:
    """
    Monitor price movements and trigger analysis when thresholds are met
    """
    
    def __init__(self):
        self.data_fetcher = DataFetcher()
        self.last_analysis_time = {}  # Symbol -> datetime
        self.reference_prices = {}    # Symbol -> price
        self.last_price_update = {}   # Symbol -> datetime
        
        # Pair-specific movement thresholds (percentage)
        self.PAIR_THRESHOLDS = {
            "BTCUSD": 0.30,   # 0.30% movement triggers analysis
            "BTCUSDm": 0.30,
            "ETHUSD": 0.35,   # 0.35% movement triggers analysis
            "ETHUSDm": 0.35,
            "EURUSD": 0.15,   # 0.15% movement triggers analysis
            "XAUUSD": 0.25,   # 0.25% movement triggers analysis
            "XAUUSD": 0.25,
            "USDAED": 0.10,   # 0.10% movement triggers analysis
        }
        
        # Cooldown settings
        self.min_interval_minutes = 15  # Minimum 15 minutes between analyses
        
        # Monitoring settings
        self.check_interval_seconds = 300  # Check every 5 minutes
        self.reference_window_minutes = 15  # 15-minute reference window
        
    async def start_monitoring(self, symbols: list):
        """Start monitoring price movements for given symbols"""
        logger.info(f"🔍 Starting trigger monitor for symbols: {symbols}")
        
        # Initialize reference prices
        await self._initialize_reference_prices(symbols)
        
        # Start monitoring loop
        while True:
            try:
                for symbol in symbols:
                    await self._check_symbol_trigger(symbol)
                
                # Wait before next check
                await asyncio.sleep(self.check_interval_seconds)
                
            except Exception as e:
                logger.error(f"❌ Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait 1 minute on error
    
    async def _initialize_reference_prices(self, symbols: list):
        """Initialize reference prices for all symbols"""
        logger.info("📊 Initializing reference prices...")
        
        for symbol in symbols:
            try:
                current_price = await self.data_fetcher.get_current_price(symbol)
                if current_price:
                    self.reference_prices[symbol] = current_price
                    self.last_price_update[symbol] = datetime.now()
                    logger.info(f"✅ {symbol} reference price: ${current_price:.5f}")
                else:
                    logger.warning(f"⚠️ Failed to get reference price for {symbol}")
            except Exception as e:
                logger.error(f"❌ Error initializing {symbol}: {e}")
    
    async def _check_symbol_trigger(self, symbol: str):
        """Check if symbol meets trigger criteria"""
        try:
            # Get current price
            current_price = await self.data_fetcher.get_current_price(symbol)
            if not current_price:
                return False
            
            # Check if we have reference price
            if symbol not in self.reference_prices:
                self.reference_prices[symbol] = current_price
                self.last_price_update[symbol] = datetime.now()
                return False
            
            # Update reference price if needed (every 15 minutes)
            if self._should_update_reference_price(symbol):
                await self._update_reference_price(symbol, current_price)
            
            # Check movement threshold
            movement_triggered = self._check_movement_threshold(symbol, current_price)
            
            # Check ATR threshold
            atr_triggered = await self._check_atr_threshold(symbol)
            
            # Check cooldown
            cooldown_ok = self._check_cooldown(symbol)
            
            # Trigger analysis if all conditions met
            if movement_triggered and atr_triggered and cooldown_ok:
                logger.info(f"🚨 TRIGGER ACTIVATED for {symbol}")
                logger.info(f"   💰 Price movement: {self._calculate_movement_percent(symbol, current_price):.2f}%")
                logger.info(f"   📊 ATR threshold: ✅")
                logger.info(f"   ⏰ Cooldown: ✅")
                
                # Update last analysis time
                self.last_analysis_time[symbol] = datetime.now()
                
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"❌ Error checking trigger for {symbol}: {e}")
            return False
    
    def _should_update_reference_price(self, symbol: str) -> bool:
        """Check if reference price should be updated"""
        if symbol not in self.last_price_update:
            return True
        
        time_since_update = datetime.now() - self.last_price_update[symbol]
        return time_since_update.total_seconds() >= (self.reference_window_minutes * 60)
    
    async def _update_reference_price(self, symbol: str, current_price: float):
        """Update reference price for symbol"""
        old_price = self.reference_prices.get(symbol, 0)
        self.reference_prices[symbol] = current_price
        self.last_price_update[symbol] = datetime.now()
        
        logger.debug(f"📊 Updated {symbol} reference: ${old_price:.5f} → ${current_price:.5f}")
    
    def _check_movement_threshold(self, symbol: str, current_price: float) -> bool:
        """Check if price movement exceeds threshold"""
        if symbol not in self.reference_prices:
            return False
        
        movement_percent = self._calculate_movement_percent(symbol, current_price)
        threshold = self.PAIR_THRESHOLDS.get(symbol, 0.20)  # Default 0.20%
        
        triggered = abs(movement_percent) >= threshold
        
        if triggered:
            logger.info(f"📈 {symbol} movement: {movement_percent:.2f}% (threshold: {threshold}%)")
        
        return triggered
    
    def _calculate_movement_percent(self, symbol: str, current_price: float) -> float:
        """Calculate percentage movement from reference price"""
        reference_price = self.reference_prices.get(symbol, current_price)
        
        if reference_price == 0:
            return 0.0
        
        movement = ((current_price - reference_price) / reference_price) * 100
        return round(movement, 3)
    
    async def _check_atr_threshold(self, symbol: str) -> bool:
        """Check if candle range > 1.5 × ATR"""
        try:
            # Get recent scalping data for ATR
            scalping_data = await self.data_fetcher.get_scalping_data(symbol, "M15")
            
            if not scalping_data:
                return True  # Default to true if no data
            
            atr = scalping_data.get('atr', 0)
            candle_structure = scalping_data.get('candle_structure', {})
            candle_range = candle_structure.get('range', 0)
            
            if atr == 0:
                return True  # Default to true if no ATR
            
            atr_threshold = 1.5 * atr
            triggered = candle_range > atr_threshold
            
            if triggered:
                logger.info(f"📊 {symbol} candle range: {candle_range:.5f} > ATR threshold: {atr_threshold:.5f}")
            
            return triggered
            
        except Exception as e:
            logger.error(f"❌ Error checking ATR threshold for {symbol}: {e}")
            return True  # Default to true on error
    
    def _check_cooldown(self, symbol: str) -> bool:
        """Check if cooldown period has passed"""
        if symbol not in self.last_analysis_time:
            return True
        
        time_since_last = datetime.now() - self.last_analysis_time[symbol]
        cooldown_passed = time_since_last.total_seconds() >= (self.min_interval_minutes * 60)
        
        if not cooldown_passed:
            remaining_minutes = self.min_interval_minutes - (time_since_last.total_seconds() / 60)
            logger.debug(f"⏰ {symbol} cooldown: {remaining_minutes:.1f} minutes remaining")
        
        return cooldown_passed
    
    def price_moved_enough(
        self,
        current_price: float,
        reference_price: float,
        threshold_percent: float
    ) -> bool:
        """
        Utility function to check if price moved enough
        """
        if reference_price == 0:
            return False
        
        movement = abs(current_price - reference_price) / reference_price * 100
        return movement >= threshold_percent
    
    async def manual_trigger_check(self, symbol: str) -> Dict[str, Any]:
        """Manually check trigger status for a symbol"""
        try:
            current_price = await self.data_fetcher.get_current_price(symbol)
            if not current_price:
                return {"error": "Failed to get current price"}
            
            # Calculate movement
            movement_percent = self._calculate_movement_percent(symbol, current_price)
            threshold = self.PAIR_THRESHOLDS.get(symbol, 0.20)
            movement_triggered = abs(movement_percent) >= threshold
            
            # Check ATR
            atr_triggered = await self._check_atr_threshold(symbol)
            
            # Check cooldown
            cooldown_ok = self._check_cooldown(symbol)
            
            # Overall trigger status
            trigger_ready = movement_triggered and atr_triggered and cooldown_ok
            
            return {
                "symbol": symbol,
                "current_price": current_price,
                "reference_price": self.reference_prices.get(symbol, 0),
                "movement_percent": movement_percent,
                "movement_threshold": threshold,
                "movement_triggered": movement_triggered,
                "atr_triggered": atr_triggered,
                "cooldown_ok": cooldown_ok,
                "trigger_ready": trigger_ready,
                "last_analysis": self.last_analysis_time.get(symbol, "Never"),
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"❌ Error in manual trigger check for {symbol}: {e}")
            return {"error": str(e)}
    
    def get_monitoring_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        return {
            "monitored_symbols": list(self.reference_prices.keys()),
            "pair_thresholds": self.PAIR_THRESHOLDS,
            "min_interval_minutes": self.min_interval_minutes,
            "check_interval_seconds": self.check_interval_seconds,
            "reference_window_minutes": self.reference_window_minutes,
            "last_analysis_times": {
                symbol: time.isoformat() if isinstance(time, datetime) else str(time)
                for symbol, time in self.last_analysis_time.items()
            },
            "reference_prices": self.reference_prices,
            "timestamp": datetime.now().isoformat()
        }
