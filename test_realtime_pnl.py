#!/usr/bin/env python3
"""
Test Real-time P&L calculation
"""
import asyncio
import websockets
import json
import requests
import time

async def test_realtime_pnl():
    """Test real-time P&L updates via WebSocket"""
    print("💰 TESTING REAL-TIME P&L CALCULATION")
    print("=" * 60)
    
    # Start backend first
    print("\n📋 STEP 1: VERIFY BACKEND IS RUNNING")
    try:
        response = requests.get("http://localhost:8001/api/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is running")
        else:
            print("❌ Backend not responding")
            return False
    except Exception as e:
        print(f"❌ Backend error: {e}")
        return False
    
    # Start trading engine
    print("\n📋 STEP 2: START TRADING ENGINE")
    try:
        data = {"auto_trading_enabled": True}
        response = requests.post("http://localhost:8001/api/trading/start", json=data, timeout=10)
        if response.status_code == 200:
            print("✅ Trading engine started")
        else:
            print(f"❌ Failed to start: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Test API endpoint with real-time P&L
    print("\n📋 STEP 3: TEST API WITH REAL-TIME P&L")
    try:
        response = requests.get("http://localhost:8001/api/trading/positions?realtime_pnl=true", timeout=5)
        if response.status_code == 200:
            positions = response.json()
            print(f"✅ API returned {len(positions)} positions with real-time P&L")
            for pos in positions:
                symbol = pos.get('symbol', 'Unknown')
                entry_price = pos.get('entry_price', 0)
                current_price = pos.get('current_price', 0)
                profit = pos.get('profit', 0)
                print(f"   📊 {symbol}: Entry ${entry_price:.5f}, Current ${current_price:.5f}, P&L ${profit:.2f}")
        else:
            print(f"❌ API error: {response.text}")
            return False
    except Exception as e:
        print(f"❌ Error: {e}")
        return False
    
    # Connect to WebSocket for real-time updates
    print("\n📋 STEP 4: MONITOR REAL-TIME P&L VIA WEBSOCKET")
    try:
        uri = "ws://localhost:8001/ws"
        async with websockets.connect(uri) as websocket:
            print("✅ WebSocket connected for real-time P&L monitoring")
            
            # Send a test message
            await websocket.send("Monitor P&L")
            
            print("\n💰 REAL-TIME P&L MONITORING:")
            print("⏳ Watching for P&L changes...")
            print("📈 Look for profit/loss fluctuations based on market price")
            
            message_count = 0
            start_time = time.time()
            last_pnl = {}
            
            while True:
                try:
                    # Wait for message with timeout
                    message = await asyncio.wait_for(websocket.recv(), timeout=3.0)
                    message_count += 1
                    
                    try:
                        data = json.loads(message)
                        msg_type = data.get('type', 'unknown')
                        
                        if msg_type == 'position_update':
                            positions = data.get('positions', [])
                            print(f"\n💰 P&L Update #{message_count}:")
                            
                            for pos in positions:
                                symbol = pos.get('symbol', 'Unknown')
                                entry_price = pos.get('entry_price', 0)
                                current_price = pos.get('current_price', 0)
                                profit = pos.get('profit', 0)
                                order_type = pos.get('order_type', 'Unknown')
                                
                                # Check for P&L changes
                                prev_pnl = last_pnl.get(symbol, profit)
                                pnl_change = profit - prev_pnl
                                last_pnl[symbol] = profit
                                
                                # Show change indicator
                                if pnl_change > 0:
                                    change_indicator = f"📈 +${pnl_change:.2f}"
                                elif pnl_change < 0:
                                    change_indicator = f"📉 ${pnl_change:.2f}"
                                else:
                                    change_indicator = "➡️  No change"
                                
                                print(f"   {symbol} ({order_type}):")
                                print(f"      Entry: ${entry_price:.5f}")
                                print(f"      Current: ${current_price:.5f}")
                                print(f"      P&L: ${profit:.2f} {change_indicator}")
                    
                    except json.JSONDecodeError:
                        # Skip non-JSON messages
                        continue
                
                except asyncio.TimeoutError:
                    # No message received, continue listening
                    elapsed = time.time() - start_time
                    if elapsed > 30:  # Stop after 30 seconds
                        print(f"\n⏰ P&L monitoring completed after 30 seconds")
                        print(f"📊 Total updates received: {message_count}")
                        break
                    continue
                
                except websockets.exceptions.ConnectionClosed:
                    print("❌ WebSocket connection closed")
                    break
                
                except Exception as e:
                    print(f"❌ Error receiving message: {e}")
                    break
    
    except Exception as e:
        print(f"❌ WebSocket connection error: {e}")
        return False
    
    return True

def compare_static_vs_realtime():
    """Compare static vs real-time P&L"""
    print("\n📋 STEP 5: COMPARE STATIC VS REAL-TIME P&L")
    
    try:
        # Get static P&L
        print("   📊 Static P&L (from MT5):")
        response = requests.get("http://localhost:8001/api/trading/positions?realtime_pnl=false", timeout=5)
        if response.status_code == 200:
            static_positions = response.json()
            for pos in static_positions:
                symbol = pos.get('symbol', 'Unknown')
                profit = pos.get('profit', 0)
                print(f"      {symbol}: ${profit:.2f}")
        
        # Get real-time P&L
        print("   💰 Real-time P&L (calculated):")
        response = requests.get("http://localhost:8001/api/trading/positions?realtime_pnl=true", timeout=5)
        if response.status_code == 200:
            realtime_positions = response.json()
            for pos in realtime_positions:
                symbol = pos.get('symbol', 'Unknown')
                profit = pos.get('profit', 0)
                current_price = pos.get('current_price', 0)
                print(f"      {symbol}: ${profit:.2f} (Current: ${current_price:.5f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error comparing P&L: {e}")
        return False

async def main():
    """Main test function"""
    print("🎯 REAL-TIME P&L CALCULATION TEST")
    print("This will test the new real-time P&L functionality")
    print("=" * 60)
    
    # Test real-time P&L
    pnl_success = await test_realtime_pnl()
    
    # Compare static vs real-time
    compare_success = compare_static_vs_realtime()
    
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS:")
    
    if pnl_success:
        print("✅ Real-time P&L calculation: WORKING")
        print("   - WebSocket delivers live P&L updates")
        print("   - P&L calculated from current market price")
        print("   - Changes visible in real-time")
    else:
        print("❌ Real-time P&L calculation: FAILED")
    
    if compare_success:
        print("✅ P&L comparison: WORKING")
        print("   - Static vs real-time P&L comparison available")
    else:
        print("❌ P&L comparison: FAILED")
    
    print("\n🎉 FRONTEND BENEFITS:")
    print("✅ Live P&L updates without refresh")
    print("✅ Real-time profit/loss tracking")
    print("✅ Immediate market price reflection")
    print("✅ Visual change indicators")
    print("✅ Accurate position valuation")
    
    print("\n💡 HOW TO SEE REAL-TIME P&L:")
    print("1. Open frontend: http://localhost:8000")
    print("2. Go to Position Manager")
    print("3. Watch P&L column update automatically")
    print("4. P&L changes as market prices move")
    print("5. No refresh needed - live updates!")

if __name__ == "__main__":
    asyncio.run(main())
