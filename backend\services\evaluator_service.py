"""
EVALUATOR SERVICE
- Setelah order ditutup (TP/SL), kirim data ke GPT untuk evaluasi
- Output: {"valid": true/false, "masalah": "...", "rekomendasi": "..."}
- Simpan evaluasi untuk pembelajaran sistem
"""

import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from pathlib import Path
from backend.services.scalping_gpt_analyzer import ScalpingGPTAnalyzer

logger = logging.getLogger(__name__)

class EvaluatorService:
    """
    Evaluate closed trades and provide learning feedback
    """
    
    def __init__(self):
        self.gpt_analyzer = ScalpingGPTAnalyzer()
        
        # Setup evaluation storage
        self.evaluations_dir = Path("logs/evaluations")
        self.evaluations_dir.mkdir(parents=True, exist_ok=True)
        
        self.evaluations_file = self.evaluations_dir / "trade_evaluations.json"
        self.learning_insights_file = self.evaluations_dir / "learning_insights.json"
        
        # Load existing evaluations
        self.evaluations = self._load_evaluations()
        self.learning_insights = self._load_learning_insights()
        
    async def evaluate_closed_trade(
        self,
        trade_data: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Evaluate a closed trade using GPT analysis
        """
        try:
            logger.info(f"📊 Evaluating closed trade: {trade_data.get('order_id', 'UNKNOWN')}")
            
            # Prepare evaluation prompt
            evaluation_prompt = self._create_evaluation_prompt(trade_data)
            
            # Get GPT evaluation
            evaluation_result = await self._get_gpt_evaluation(evaluation_prompt)
            
            if not evaluation_result:
                logger.error("❌ Failed to get GPT evaluation")
                return None
            
            # Create evaluation record
            evaluation_record = {
                "evaluation_id": f"eval_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                "order_id": trade_data.get("order_id", "UNKNOWN"),
                "symbol": trade_data.get("symbol", "UNKNOWN"),
                "trade_data": trade_data,
                "gpt_evaluation": evaluation_result,
                "evaluation_timestamp": datetime.now().isoformat(),
                "learning_points": self._extract_learning_points(evaluation_result)
            }
            
            # Store evaluation
            self.evaluations.append(evaluation_record)
            self._save_evaluations()
            
            # Update learning insights
            self._update_learning_insights(evaluation_record)
            
            logger.info(f"✅ Trade evaluation completed: {evaluation_result.get('valid', 'UNKNOWN')}")
            return evaluation_record
            
        except Exception as e:
            logger.error(f"❌ Error evaluating closed trade: {e}")
            return None
    
    def _create_evaluation_prompt(self, trade_data: Dict[str, Any]) -> str:
        """Create evaluation prompt for GPT"""
        
        order_id = trade_data.get("order_id", "UNKNOWN")
        symbol = trade_data.get("symbol", "UNKNOWN")
        entry_price = trade_data.get("entry_price", 0)
        exit_price = trade_data.get("exit_price", 0)
        profit_loss = trade_data.get("profit_loss", 0)
        exit_reason = trade_data.get("exit_reason", "UNKNOWN")
        duration_minutes = trade_data.get("duration_minutes", 0)
        
        # Original analysis data
        original_analysis = trade_data.get("original_gpt_analysis", {})
        original_rsi = trade_data.get("original_scalping_data", {}).get("rsi", 50)
        original_volume_change = trade_data.get("original_scalping_data", {}).get("volume_metrics", {}).get("volume_increase_percent", 0)
        
        # Exit conditions
        exit_rsi = trade_data.get("exit_scalping_data", {}).get("rsi", 50)
        exit_volume_change = trade_data.get("exit_scalping_data", {}).get("volume_metrics", {}).get("volume_increase_percent", 0)
        
        prompt = f"""
SCALPING TRADE EVALUATION

TRADE SUMMARY:
- Order ID: {order_id}
- Symbol: {symbol}
- Entry Price: ${entry_price}
- Exit Price: ${exit_price}
- Profit/Loss: ${profit_loss}
- Exit Reason: {exit_reason}
- Duration: {duration_minutes} minutes

ORIGINAL ANALYSIS:
- GPT Decision: {original_analysis.get('analisis', 'UNKNOWN')}
- Entry Reason: {original_analysis.get('alasan', 'No reason provided')}
- RSI at Entry: {original_rsi}
- Volume Change at Entry: {original_volume_change}%

EXIT CONDITIONS:
- RSI at Exit: {exit_rsi}
- Volume Change at Exit: {exit_volume_change}%

EVALUATION CRITERIA:
1. Was the original scalping setup valid?
2. Did the entry criteria align with actual market behavior?
3. Were the stop loss and take profit levels appropriate?
4. What could be improved in the analysis?
5. Any pattern recognition for future trades?

Please evaluate this scalping trade and provide learning insights.

Respond with JSON format:
{{
  "valid": true/false,
  "masalah": "Detailed analysis of what went wrong (if any)",
  "rekomendasi": "Specific recommendations for improvement",
  "setup_quality": "EXCELLENT/GOOD/FAIR/POOR",
  "exit_timing": "OPTIMAL/GOOD/EARLY/LATE",
  "learning_points": ["point1", "point2", "point3"],
  "pattern_recognition": "Any patterns identified for future reference"
}}
"""
        
        return prompt
    
    async def _get_gpt_evaluation(self, prompt: str) -> Optional[Dict[str, Any]]:
        """Get GPT evaluation response"""
        try:
            response = await self.gpt_analyzer.client.chat.completions.create(
                model=self.gpt_analyzer.model,
                messages=[
                    {
                        "role": "system",
                        "content": """You are an expert scalping trade evaluator. 
                        Analyze completed trades objectively and provide constructive feedback.
                        Focus on setup quality, execution, and learning opportunities.
                        Be honest about mistakes and clear about improvements."""
                    },
                    {
                        "role": "user",
                        "content": prompt
                    }
                ],
                temperature=0.3,  # Slightly higher for more nuanced evaluation
                max_tokens=800    # More tokens for detailed evaluation
            )
            
            # Parse response
            response_text = response.choices[0].message.content.strip()
            
            # Extract JSON
            start_idx = response_text.find('{')
            end_idx = response_text.rfind('}') + 1
            
            if start_idx != -1 and end_idx > start_idx:
                json_text = response_text[start_idx:end_idx]
                evaluation_result = json.loads(json_text)
                return evaluation_result
            else:
                logger.error("❌ No valid JSON found in GPT evaluation response")
                return None
                
        except Exception as e:
            logger.error(f"❌ Error getting GPT evaluation: {e}")
            return None
    
    def _extract_learning_points(self, evaluation_result: Dict[str, Any]) -> List[str]:
        """Extract key learning points from evaluation"""
        learning_points = []
        
        # Add learning points from GPT response
        gpt_learning_points = evaluation_result.get("learning_points", [])
        learning_points.extend(gpt_learning_points)
        
        # Add automatic learning points based on evaluation
        if not evaluation_result.get("valid", True):
            learning_points.append("Invalid setup - review entry criteria")
        
        setup_quality = evaluation_result.get("setup_quality", "")
        if setup_quality in ["FAIR", "POOR"]:
            learning_points.append(f"Setup quality was {setup_quality} - improve analysis")
        
        exit_timing = evaluation_result.get("exit_timing", "")
        if exit_timing in ["EARLY", "LATE"]:
            learning_points.append(f"Exit timing was {exit_timing} - review exit strategy")
        
        return learning_points
    
    def _update_learning_insights(self, evaluation_record: Dict[str, Any]):
        """Update learning insights based on evaluation"""
        try:
            symbol = evaluation_record["symbol"]
            gpt_evaluation = evaluation_record["gpt_evaluation"]
            
            # Initialize symbol insights if not exists
            if symbol not in self.learning_insights:
                self.learning_insights[symbol] = {
                    "total_evaluations": 0,
                    "valid_setups": 0,
                    "invalid_setups": 0,
                    "setup_quality_distribution": {"EXCELLENT": 0, "GOOD": 0, "FAIR": 0, "POOR": 0},
                    "exit_timing_distribution": {"OPTIMAL": 0, "GOOD": 0, "EARLY": 0, "LATE": 0},
                    "common_problems": {},
                    "key_recommendations": {},
                    "learning_points": [],
                    "last_updated": datetime.now().isoformat()
                }
            
            insights = self.learning_insights[symbol]
            
            # Update counters
            insights["total_evaluations"] += 1
            
            if gpt_evaluation.get("valid", True):
                insights["valid_setups"] += 1
            else:
                insights["invalid_setups"] += 1
            
            # Update distributions
            setup_quality = gpt_evaluation.get("setup_quality", "FAIR")
            if setup_quality in insights["setup_quality_distribution"]:
                insights["setup_quality_distribution"][setup_quality] += 1
            
            exit_timing = gpt_evaluation.get("exit_timing", "GOOD")
            if exit_timing in insights["exit_timing_distribution"]:
                insights["exit_timing_distribution"][exit_timing] += 1
            
            # Update problems and recommendations
            masalah = gpt_evaluation.get("masalah", "")
            if masalah:
                insights["common_problems"][masalah] = insights["common_problems"].get(masalah, 0) + 1
            
            rekomendasi = gpt_evaluation.get("rekomendasi", "")
            if rekomendasi:
                insights["key_recommendations"][rekomendasi] = insights["key_recommendations"].get(rekomendasi, 0) + 1
            
            # Add learning points
            learning_points = evaluation_record.get("learning_points", [])
            insights["learning_points"].extend(learning_points)
            
            # Keep only last 50 learning points
            insights["learning_points"] = insights["learning_points"][-50:]
            
            insights["last_updated"] = datetime.now().isoformat()
            
            # Save insights
            self._save_learning_insights()
            
        except Exception as e:
            logger.error(f"❌ Error updating learning insights: {e}")
    
    def _load_evaluations(self) -> List[Dict[str, Any]]:
        """Load evaluations from file"""
        try:
            if self.evaluations_file.exists():
                with open(self.evaluations_file, 'r') as f:
                    evaluations = json.load(f)
                logger.info(f"📂 Loaded {len(evaluations)} trade evaluations")
                return evaluations
            else:
                logger.info("📂 No existing evaluations file")
                return []
        except Exception as e:
            logger.error(f"❌ Error loading evaluations: {e}")
            return []
    
    def _save_evaluations(self):
        """Save evaluations to file"""
        try:
            with open(self.evaluations_file, 'w') as f:
                json.dump(self.evaluations, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Error saving evaluations: {e}")
    
    def _load_learning_insights(self) -> Dict[str, Any]:
        """Load learning insights from file"""
        try:
            if self.learning_insights_file.exists():
                with open(self.learning_insights_file, 'r') as f:
                    insights = json.load(f)
                logger.info(f"📚 Loaded learning insights for {len(insights)} symbols")
                return insights
            else:
                logger.info("📚 No existing learning insights file")
                return {}
        except Exception as e:
            logger.error(f"❌ Error loading learning insights: {e}")
            return {}
    
    def _save_learning_insights(self):
        """Save learning insights to file"""
        try:
            with open(self.learning_insights_file, 'w') as f:
                json.dump(self.learning_insights, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Error saving learning insights: {e}")
    
    def get_evaluation_summary(self, symbol: Optional[str] = None) -> Dict[str, Any]:
        """Get evaluation summary for symbol or all symbols"""
        try:
            if symbol:
                # Filter evaluations for specific symbol
                symbol_evaluations = [
                    eval_record for eval_record in self.evaluations
                    if eval_record.get("symbol") == symbol
                ]
                
                return {
                    "symbol": symbol,
                    "total_evaluations": len(symbol_evaluations),
                    "recent_evaluations": symbol_evaluations[-10:],  # Last 10
                    "learning_insights": self.learning_insights.get(symbol, {}),
                    "timestamp": datetime.now().isoformat()
                }
            else:
                # Summary for all symbols
                total_evaluations = len(self.evaluations)
                symbols_evaluated = list(set(eval_record.get("symbol") for eval_record in self.evaluations))
                
                return {
                    "total_evaluations": total_evaluations,
                    "symbols_evaluated": symbols_evaluated,
                    "recent_evaluations": self.evaluations[-20:],  # Last 20
                    "learning_insights_summary": {
                        symbol: {
                            "total_evaluations": insights.get("total_evaluations", 0),
                            "valid_setup_rate": round(
                                (insights.get("valid_setups", 0) / insights.get("total_evaluations", 1)) * 100, 2
                            ),
                            "last_updated": insights.get("last_updated", "Never")
                        }
                        for symbol, insights in self.learning_insights.items()
                    },
                    "timestamp": datetime.now().isoformat()
                }
                
        except Exception as e:
            logger.error(f"❌ Error getting evaluation summary: {e}")
            return {"error": str(e)}
