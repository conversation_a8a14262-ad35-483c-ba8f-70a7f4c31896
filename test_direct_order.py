#!/usr/bin/env python3
"""
Test direct order placement without GPT analysis
This will help us isolate and test the MT5 order placement functionality
"""
import requests
import time
import json

def test_direct_order_placement():
    """Test direct order placement to MT5"""
    print("🔧 === TESTING DIRECT ORDER PLACEMENT ===")
    
    # Test 1: Check if trading engine is running
    print("\n1. Checking trading engine status...")
    try:
        response = requests.get("http://localhost:8001/api/trading/status", timeout=5)
        if response.status_code == 200:
            status = response.json()
            print(f"✅ Trading engine status: {status.get('system_health', 'Unknown')}")
            print(f"   MT5 Connected: {status.get('is_mt5_connected', False)}")
            print(f"   Trading Enabled: {status.get('is_trading_enabled', False)}")
        else:
            print(f"❌ Status check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Status check error: {e}")
        return False
    
    # Test 2: Get account info
    print("\n2. Getting account information...")
    try:
        response = requests.get("http://localhost:8001/api/trading/account", timeout=5)
        if response.status_code == 200:
            account = response.json()
            balance = account.get('balance', 0)
            free_margin = account.get('free_margin', 0)
            print(f"✅ Account Balance: ${balance:.2f}")
            print(f"   Free Margin: ${free_margin:.2f}")
            print(f"   Leverage: {account.get('leverage', 'Unknown')}")
        else:
            print(f"⚠️  Account info: {response.status_code}")
    except Exception as e:
        print(f"❌ Account info error: {e}")
    
    # Test 3: Get current market data
    print("\n3. Getting market data for BTCUSDm...")
    try:
        response = requests.get("http://localhost:8001/api/trading/market-data/BTCUSDm", timeout=5)
        if response.status_code == 200:
            market = response.json()
            bid = market.get('bid', 0)
            ask = market.get('ask', 0)
            spread = market.get('spread', 0)
            print(f"✅ Market Data:")
            print(f"   Bid: ${bid:.2f}")
            print(f"   Ask: ${ask:.2f}")
            print(f"   Spread: ${spread:.2f}")
        else:
            print(f"❌ Market data failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Market data error: {e}")
        return False
    
    # Test 4: Create a manual trading signal for testing
    print("\n4. Creating test trading signal...")
    print("   ⚠️  This will attempt to place a REAL order!")
    print("   📊 Order details:")
    print("   - Symbol: BTCUSDm")
    print("   - Type: BUY")
    print("   - Lot Size: 0.01 (minimum)")
    print("   - Comment: Test Order")
    
    # Ask for confirmation
    print("\n   🤔 Do you want to proceed with placing a test order?")
    print("   This will use real money from your MT5 account!")
    
    # For automated testing, we'll skip the actual order placement
    # but show what the request would look like
    print("\n   📝 Test Order Request (NOT EXECUTED):")
    
    test_order = {
        "symbol": "BTCUSDm",
        "order_type": "BUY",
        "lot_size": 0.01,
        "comment": "Test Order",
        "deviation": 100
    }
    
    print(f"   {json.dumps(test_order, indent=2)}")
    
    # Test 5: Simulate order validation
    print("\n5. Testing order validation logic...")
    print("   ✅ Symbol: BTCUSDm (valid)")
    print("   ✅ Lot size: 0.01 (minimum allowed)")
    print("   ✅ Order type: BUY (valid)")
    print("   ✅ Comment: 'Test Order' (short and safe)")
    print("   ✅ Deviation: 100 points (adequate for crypto)")
    
    # Test 6: Check what would happen with margin
    print("\n6. Margin calculation check...")
    position_value = 0.01 * 108000  # Approximate BTC price
    margin_10x = position_value / 10  # 10:1 leverage
    margin_20x = position_value / 20  # 20:1 leverage
    
    print(f"   Position value: ${position_value:.2f}")
    print(f"   Margin needed (10:1): ${margin_10x:.2f}")
    print(f"   Margin needed (20:1): ${margin_20x:.2f}")
    print(f"   Available margin: ${free_margin:.2f}")
    
    if margin_20x <= free_margin:
        print("   ✅ Sufficient margin for 20:1 leverage")
    elif margin_10x <= free_margin:
        print("   ⚠️  Only sufficient for 10:1 leverage")
    else:
        print("   ❌ Insufficient margin for this position size")
    
    return True

def create_manual_order_endpoint_test():
    """Create a test that would call the MT5 connector directly"""
    print("\n🔧 === MANUAL ORDER ENDPOINT TEST ===")
    
    print("\n📋 To test actual order placement, you could:")
    print("1. Create a test endpoint in the backend")
    print("2. Call MT5 connector directly with test parameters")
    print("3. Use minimal lot size (0.01)")
    print("4. Set tight stop loss for safety")
    
    print("\n💡 Suggested test order parameters:")
    print("   Symbol: BTCUSDm")
    print("   Action: BUY")
    print("   Volume: 0.01")
    print("   Type: Market order")
    print("   Comment: 'Test'")
    print("   Deviation: 100")
    print("   Stop Loss: Current price - 100 points")
    print("   Take Profit: Current price + 50 points")
    
    print("\n⚠️  SAFETY RECOMMENDATIONS:")
    print("   - Use demo account if possible")
    print("   - Use minimum lot size")
    print("   - Set tight stop loss")
    print("   - Close position immediately after test")
    print("   - Monitor MT5 terminal during test")

def analyze_potential_issues():
    """Analyze potential issues with order placement"""
    print("\n🔍 === POTENTIAL ORDER PLACEMENT ISSUES ===")
    
    print("\n🚫 Common MT5 order rejection reasons:")
    print("1. Market closed - Check trading hours")
    print("2. Insufficient margin - Account balance too low")
    print("3. Invalid lot size - Below minimum or above maximum")
    print("4. Price too far from market - Large spread or gap")
    print("5. Symbol not tradeable - Broker restrictions")
    print("6. Invalid parameters - Wrong order type or values")
    print("7. Connection issues - MT5 not properly connected")
    print("8. Account restrictions - Demo/live account limitations")
    
    print("\n✅ Our current fixes:")
    print("   - Symbol validation and selection")
    print("   - Lot size rounding to valid steps")
    print("   - Increased deviation (100 points)")
    print("   - Short, safe comment ('GPT Signal')")
    print("   - Proper filling type detection")
    print("   - Detailed error logging")
    
    print("\n🎯 Next debugging steps if orders still fail:")
    print("   1. Check MT5 terminal for error messages")
    print("   2. Verify account has trading permissions")
    print("   3. Test with different symbols (EURUSD, XAUUSD)")
    print("   4. Try different lot sizes (0.1, 0.05)")
    print("   5. Check broker-specific requirements")
    print("   6. Verify market is open for trading")

if __name__ == "__main__":
    print("🎯 Direct Order Placement Test")
    print("=" * 60)
    print("This test will check order placement capability without GPT analysis")
    print("=" * 60)
    
    success = test_direct_order_placement()
    create_manual_order_endpoint_test()
    analyze_potential_issues()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 DIRECT ORDER TEST COMPLETED!")
        print("\n📊 Test Results:")
        print("✅ System connectivity verified")
        print("✅ Account information accessible")
        print("✅ Market data available")
        print("✅ Order parameters validated")
        print("✅ Margin calculations working")
        
        print("\n🔍 To test actual order placement:")
        print("   - All prerequisites are met")
        print("   - System is ready for order placement")
        print("   - Consider testing with demo account first")
        print("   - Monitor MT5 terminal during tests")
    else:
        print("❌ DIRECT ORDER TEST INCOMPLETE")
        print("   - Fix connectivity issues first")
        print("   - Ensure MT5 is properly connected")
    
    print(f"\n🌐 System is ready for order placement testing")
