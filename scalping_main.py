#!/usr/bin/env python3
"""
Main entry point for the SCALPING trading system
Jalankan siklus:
- check_time_or_price_trigger() → data_fetcher → gpt_analyzer → order_manager
- Monitor buffer trigger via trigger_monitor
- Jalankan reevaluation saat harga mendekati
- <PERSON><PERSON><PERSON> setiap posisi yang ditutup
"""

import asyncio
import uvicorn
import logging
import signal
import sys
from pathlib import Path
from backend.services.scalping_engine import ScalpingEngine
from backend.api.main import app

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/scalping_system.log'),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# Create logs directory
Path("logs").mkdir(exist_ok=True)

# Global scalping engine instance
scalping_engine = None
server_task = None
engine_task = None

async def start_scalping_system():
    """Start the complete scalping trading system"""
    global scalping_engine, server_task, engine_task
    
    try:
        logger.info("🚀 Starting SCALPING Trading System...")
        
        # Initialize scalping engine
        scalping_engine = ScalpingEngine()
        
        # Start scalping engine in background
        engine_task = asyncio.create_task(scalping_engine.start_engine())
        
        # Start FastAPI server
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8001,
            log_level="info",
            access_log=False  # Reduce log noise
        )
        
        server = uvicorn.Server(config)
        server_task = asyncio.create_task(server.serve())
        
        logger.info("✅ Scalping system started successfully")
        logger.info("📊 API Server: http://localhost:8001")
        logger.info("🔄 Scalping Engine: Running")
        logger.info("📈 Monitored Symbols: BTCUSDm, ETHUSDm, EURUSD, XAUUSD")
        
        # Wait for both tasks
        await asyncio.gather(engine_task, server_task)
        
    except asyncio.CancelledError:
        logger.info("🛑 Tasks cancelled, shutting down...")
    except Exception as e:
        logger.error(f"❌ Error starting scalping system: {e}")
        raise

async def shutdown_system():
    """Gracefully shutdown the scalping system"""
    global scalping_engine, server_task, engine_task
    
    logger.info("🛑 Shutting down scalping system...")
    
    try:
        # Stop scalping engine
        if scalping_engine:
            await scalping_engine.stop_engine()
        
        # Cancel tasks
        if engine_task and not engine_task.done():
            engine_task.cancel()
            
        if server_task and not server_task.done():
            server_task.cancel()
        
        # Wait a bit for cleanup
        await asyncio.sleep(2)
        
        logger.info("✅ Scalping system shutdown complete")
        
    except Exception as e:
        logger.error(f"❌ Error during shutdown: {e}")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"🛑 Received signal {signum}, initiating shutdown...")
    
    # Get the current event loop
    try:
        loop = asyncio.get_running_loop()
        loop.create_task(shutdown_system())
    except RuntimeError:
        # No running loop, create one
        asyncio.run(shutdown_system())

async def main():
    """Main function to run the scalping system"""
    # Setup signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await start_scalping_system()
    except KeyboardInterrupt:
        logger.info("🛑 Shutdown requested by user (Ctrl+C)")
        await shutdown_system()
    except Exception as e:
        logger.error(f"❌ Fatal error in main: {e}")
        await shutdown_system()
        sys.exit(1)

def print_startup_banner():
    """Print startup banner"""
    print("=" * 70)
    print("🎯 SCALPING TRADING SYSTEM")
    print("=" * 70)
    print("📈 Auto Trading Scalping untuk Futures berbasis Python + GPT-4o")
    print("🧠 GPT sebagai Validator Struktur (bukan prediktor)")
    print("⚡ Pending Orders dengan Buffer & Revalidasi")
    print("📊 Sistem Logging dan Evaluator setelah posisi ditutup")
    print("")
    print("🔧 KOMPONEN SISTEM:")
    print("   📊 Data Fetcher - 30 candle M15 + RSI(14) + ATR(14)")
    print("   🧠 GPT Analyzer - Validator struktur candle + indikator")
    print("   🚨 Trigger Monitor - Pergerakan harga + cooldown timer")
    print("   📋 Order Manager - Pending orders + buffer zones")
    print("   💰 Price Monitor - Real-time tracking + moving window")
    print("   🔄 Reevaluation - Buffer zone analysis + order validation")
    print("   📈 Evaluator - Post-trade analysis + learning insights")
    print("")
    print("⚙️ PAIR THRESHOLDS:")
    print("   BTCUSDm: 0.30% movement triggers analysis")
    print("   ETHUSDm: 0.35% movement triggers analysis")
    print("   EURUSD: 0.15% movement triggers analysis")
    print("   XAUUSD: 0.25% movement triggers analysis")
    print("")
    print("🎯 SCALPING CRITERIA:")
    print("   BUY: Breakout bullish + RSI <40→>50 + Volume +20% + No upper wick")
    print("   SELL: Breakdown bearish + RSI >60→<50 + Volume +20% + No lower tail")
    print("")
    print("⏰ TIMING:")
    print("   Main Cycle: 5 minutes")
    print("   Re-evaluation: 3 minutes")
    print("   Cleanup: 1 hour")
    print("   Order Expiry: 2 hours")
    print("")
    print("📂 LOGS:")
    print("   System: logs/scalping_system.log")
    print("   Orders: logs/pending_orders.json")
    print("   Expired: logs/expired_orders.json")
    print("   Evaluations: logs/evaluations/")
    print("=" * 70)
    print("")

if __name__ == "__main__":
    print_startup_banner()
    
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n🛑 System interrupted by user")
    except Exception as e:
        print(f"\n❌ System failed to start: {e}")
        sys.exit(1)
